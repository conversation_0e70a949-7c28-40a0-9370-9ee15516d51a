package com.topdon.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SellerAuthorizationDTO {
    private Integer current = 1;
    private Integer size = 10;

    private String authCertNo;
    private String authorizedEntity;
    private List<Integer> dealerLevel;
    private List<Integer> status;
    private List<Integer> authType;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date authStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date authEndDate;
    private String authPlatform;
    private List<String> regionList;
    private List<Integer> productLinesList;

    private String order;
    private String orderProp;
}
