package com.jeesite.modules;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.jeesite.common.config.Global;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.ExceptionUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.file.entity.FileEntity;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.support.FileUploadServiceExtendSupport;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Optional;

@Service
public class FileUploadServiceExtendImpl extends FileUploadServiceExtendSupport {

    private static final String endpoint = Global.getConfig("file.client.aliyun.endpoint");//"oss-cn-shenzhen.aliyuncs.com";
    private static final String accessKeyId = Global.getConfig("file.client.aliyun.accessKeyId");
    private static final String accessKeySecret = Global.getConfig("file.client.aliyun.accessKeySecret");
    private static final String bucketName =  Global.getConfig("file.client.aliyun.bucketName");
    private static final String root =  Global.getConfig("file.client.aliyun.root");
    private static final String proxyEndpoint = Optional.ofNullable(Global.getConfig("file.client.aliyun.proxyEndpoint")).filter(Strings::isNotEmpty).orElse(bucketName + "." + endpoint);

    /**
     * 验证文件是否真实的存在
     *
     * @param fileEntity 文件实体信息
     * @return 文件存在true，不存在false
     */
    public boolean fileExists(FileEntity fileEntity) {
        String path = fileEntity.getFileRealPath();
        File localFile = new File(path);
        return localFile.exists();
    }

    /**
     * 上传文件，首次上传文件都调用（保存到文件实体表之前调用）
     *
     * @param fileEntity                   文件实体信息
     * @throws 支持抛出 throw ServiceException("文件不符合要求") v4.1.5
     */
    public void uploadFile(FileEntity fileEntity) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            /*
             * Determine whether the bucket exists
             */
            if (!ossClient.doesBucketExist(bucketName)) {
                /*
                 * Create a new OSS bucket
                 */
                ossClient.createBucket(bucketName);
                CreateBucketRequest createBucketRequest= new CreateBucketRequest(bucketName);
                createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
                ossClient.createBucket(createBucketRequest);
            }
            File file = new File(fileEntity.getFileRealPath());
            ossClient.putObject(new PutObjectRequest(bucketName, root + fileEntity.getFileUploadParams().getBizType() +"/"+ fileEntity.getFileUploadParams().getFileName(), file));
        } catch (OSSException | ClientException oe) {
            throw ExceptionUtils.unchecked(oe);
        } finally {
            ossClient.shutdown();
            FileUtils.deleteFile(fileEntity.getFileRealPath());
        }
    }

    /**
     * 获取访问文件的URL地址
     *
     * @param fileUpload 文件上传的信息，包括文件实体
     * @return 无文件下载地址，则返回null，方便后续处理
     */
    public String getFileUrl(FileUpload fileUpload) {
        if (StringUtils.isEmpty(fileUpload.getBizType())) {
            return "https://" + proxyEndpoint + "/" + root + fileUpload.getFileEntity().getFileUploadParams().getBizType() + "/" + fileUpload.getFileName();
        }else{
            return "https://" + proxyEndpoint + "/" + root + fileUpload.getBizType() + "/" + fileUpload.getFileName();
        }
    }

    /**
     * 下载文件到浏览器
     *
     * @param fileUpload 文件上传的信息
     * @param request    请求对象，可能断点续传用
     * @param response   响应对象，输出文件流使用
     * @return 如果不是文件流数据，也可返回文件的URL地址进行跳转，如果文件不存在返回404字符串
     */
    public String downFile(FileUpload fileUpload, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isEmpty(fileUpload.getBizType())) {
            return "https://" + proxyEndpoint + "/" + root + fileUpload.getFileEntity().getFileUploadParams().getBizType() + "/" + fileUpload.getFileName();
        }else{
            return "https://" + proxyEndpoint + "/" + root + fileUpload.getBizType() + "/" + fileUpload.getFileName();
        }
    }
}
