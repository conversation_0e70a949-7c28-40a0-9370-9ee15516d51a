package com.topdon.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.topdon.admin.dto.SellerAuthorizationDTO;
import com.topdon.admin.dto.SellerAuthorizationDetailDTO;
import com.topdon.admin.dto.SellerAuthorizationStatusChangeDTO;
import com.topdon.admin.service.SellerAuthorizationService;
import com.topdon.admin.vo.SellerAuthorizationExportVo;
import com.topdon.admin.vo.SellerAuthorizationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@Slf4j
@RequestMapping("${adminPath}/seller/authorization")
public class SellerAuthorizationController {

    @Resource
    private SellerAuthorizationService sellerAuthorizationService;


    @RequiresPermissions("seller:authorization:view")
    @GetMapping
    public String view(Model model) throws JsonProcessingException {
        return "modules/seller/authorization";
    }

    @ResponseBody
    @PostMapping("/page")
    public IPage<SellerAuthorizationVo> page(@RequestBody SellerAuthorizationDTO sellerAuthorizationDTO) {
        return sellerAuthorizationService.getPage(sellerAuthorizationDTO);
    }

    /**
     * 导出用户数据
     */
    @RequiresPermissions("seller:authorization:view")
    @PostMapping(value = "/exportData")
    public void exportData(@RequestBody SellerAuthorizationDTO sellerAuthorizationDTO, HttpServletResponse response) {
        List<SellerAuthorizationExportVo> list = sellerAuthorizationService.exportData(sellerAuthorizationDTO);
        String fileName = "授权卖家" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try(ExcelExport ee = new ExcelExport("授权卖家", SellerAuthorizationExportVo.class)){
            ee.setDataList(list).write(response, fileName);
        }
    }

    @ResponseBody
    @GetMapping("/detail/{id}")
    public SellerAuthorizationDetailDTO detail(@PathVariable Integer id) {
        return sellerAuthorizationService.detail(id);
    }

    @RequiresPermissions("seller:authorization:edit")
    @ResponseBody
    @PostMapping("/saveOrUpdate")
    public String saveOrUpdate(@RequestBody SellerAuthorizationDetailDTO sellerAuthorizationDetailDTO) {
        try {
            sellerAuthorizationService.saveOrUpdate(sellerAuthorizationDetailDTO);
        } catch (RuntimeException e) {
            return e.getMessage();
        }
        return null;
    }

    @RequiresPermissions("seller:authorization:edit")
    @ResponseBody
    @PostMapping("/changeStatus")
    public String changeStatus(@RequestBody SellerAuthorizationStatusChangeDTO SellerAuthorizationStatusChangeDTO) {
        try {
            sellerAuthorizationService.changeStatus(SellerAuthorizationStatusChangeDTO);
        } catch (RuntimeException e) {
            return e.getMessage();
        }
        return null;
    }

    @RequiresPermissions("seller:authorization:edit")
    @ResponseBody
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Integer id) {
        sellerAuthorizationService.removeById(id);
    }

}
