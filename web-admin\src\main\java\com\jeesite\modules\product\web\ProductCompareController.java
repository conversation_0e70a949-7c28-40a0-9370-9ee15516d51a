package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductCompare;
import com.jeesite.modules.product.service.ProductCompareService;

/**
 * 产品对比管理Controller
 * <AUTHOR>
 * @version 2022-04-21
 */
@Deprecated
//@Controller
//@RequestMapping(value = "${adminPath}/product/productCompare")
public class ProductCompareController extends BaseController {

	@Autowired
	private ProductCompareService productCompareService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ProductCompare get(String id, boolean isNewRecord) {
		return productCompareService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("product:productCompare:view")
	@RequestMapping(value = {"list", ""})
	public String list(ProductCompare productCompare, Model model) {
		model.addAttribute("productCompare", productCompare);
		return "modules/product/productCompareList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("product:productCompare:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ProductCompare> listData(ProductCompare productCompare, HttpServletRequest request, HttpServletResponse response) {
		productCompare.setPage(new Page<>(request, response));
		Page<ProductCompare> page = productCompareService.findPage(productCompare);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("product:productCompare:view")
	@RequestMapping(value = "form")
	public String form(ProductCompare productCompare, Model model) {
		model.addAttribute("productCompare", productCompare);
		return "modules/product/productCompareForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("product:productCompare:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ProductCompare productCompare) {
		productCompareService.save(productCompare);
		return renderResult(Global.TRUE, text("保存产品对比成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("product:productCompare:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ProductCompare productCompare) {
		productCompareService.delete(productCompare);
		return renderResult(Global.TRUE, text("删除产品对比成功！"));
	}
	
}