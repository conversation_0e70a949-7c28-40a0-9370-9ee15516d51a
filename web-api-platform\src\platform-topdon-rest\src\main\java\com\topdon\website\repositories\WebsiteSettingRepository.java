package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.WebsiteSettingMapper;
import com.topdon.website.model.WebsiteSetting;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class WebsiteSettingRepository extends MysqlJDBCSupport {

    @Inject
    protected WebsiteSettingRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public AbstractOption<WebsiteSetting> option(String key) {
        @Language("SQL") String sql = "select id, `key`, value, remarks from website_setting where `key` = :key";
        MapSqlParameterSource param = new MapSqlParameterSource("key", key);
        return option(sql, WebsiteSettingMapper.DETAIL, param);
    }
}
