package com.jeesite.modules.footer.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 底部菜单Entity
 * <AUTHOR>
 * @version 2022-04-25
 */
@Table(name="footer_menu", alias="a", label="菜单信息", columns={
		@Column(name="code", attrName="code", label="编码", isPK=true),
		@Column(name="name", attrName="name", label="名称", queryType=QueryType.LIKE, isTreeName=true),
		@Column(name="link", attrName="link", label="链接"),
		@Column(name="mobile_link", attrName="mobileLink", label="链接"),
		@Column(includeEntity=TreeEntity.class),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="remarks", attrName="remarks", label="备注", queryType=QueryType.LIKE),
	}, orderBy="a.tree_sorts, a.code"
)
public class FooterMenu extends TreeEntity<FooterMenu> {
	
	private static final long serialVersionUID = 1L;
	private String code;		// 编码
	private String name;		// 名称
	private String link;		// 链接
	private String mobileLink;		// 链接
	
	public FooterMenu() {
		this(null);
	}

	public FooterMenu(String id){
		super(id);
	}
	
	@Override
	public FooterMenu getParent() {
		return parent;
	}

	@Override
	public void setParent(FooterMenu parent) {
		this.parent = parent;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	@Size(min=0, max=256, message="名称长度不能超过 256 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Size(min=0, max=256, message="链接长度不能超过 256 个字符")
	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	@Size(min=0, max=256, message="链接长度不能超过 256 个字符")
	public String getMobileLink() {
		return mobileLink;
	}

	public void setMobileLink(String mobileLink) {
		this.mobileLink = mobileLink;
	}
}