<% layout('/layouts/default.html', {title: '产品选择', libs: ['layout', 'zTree', 'dataGrid']}){ %>

<div class="ui-layout-center">
    <div class="main-content">
        <div class="box box-main">
            <div class="box-body pb0">
                <#form:form id="searchForm" model="${product}" action="${ctx}/product/product/listData" method="post" class="form-inline"
                data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                <div class="form-group">
                    <label class="control-label">${text('产品名称')}：</label>
                    <div class="control-inline">
                        <#form:input path="name" maxlength="40" class="form-control width-120"/>
                    </div>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
                    <button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
                </div>
            </#form:form>
            <div class="col-xs-10 p0 pr10">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
            <div class="col-xs-2 p0">
                <div id="selectData" class="tags-input"></div>
            </div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
    // 初始化布局
    // $('body').layout({
    //     west__size: window.lang == 'en' ? 225 : 200,
    //     onresize_end: function(){
    //         $('#dataGrid').dataGrid('resize');
    //     }
    // });
    // 加载用户列表
    var selectData = ${isNotBlank(selectData!) ? selectData! : "{\}"},
    selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
        searchForm: $("#searchForm"),
        columnModel: [
            {header:'${text("产品名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true},
            {header:'${text("描述")}', name:'description', index:'a.description', width:150, align:"left"},
            {header:'${text("分类")}', name:'classification.name', index:'a.classification_id', width:150, align:"center"},
            {header:'${text("搜索结果页封面")}', name:'searchCover', index:'a.search_cover', width:150, align:"center",formatter:function (val,obj,row,act) {
                    if (val){
                        return '<img width="50%" src="' + encodeURI(val) + '"/>';
                    }else{
                        return "缺失";
                    }

                }},
            {header:'${text("产品列表封面")}', name:'cover', index:'a.cover', width:150, align:"center",formatter:function (val,obj,row,act) {
                    if (val){
                        return '<img width="50%" src="' + encodeURI(val) + '"/>';
                    }else{
                        return "缺失";
                    }

                }},
            {header:'${text("创建时间")}', name:'createAt', index:'a.create_at', width:150, align:"center"},
            {header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
                    return JSON.stringify(row);
                }}
        ],
// 	autoGridWidthFix: 100,
        autoGridHeight: function(){
            var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 70;
            $('.tags-input').height($('.ui-jqgrid').height() - 10);
            return height;
        },
        showCheckbox: '${parameter.checkbox}' == 'true',
        multiboxonly: false, // 单击复选框时再多选
        ajaxSuccess: function(data){
            $.each(selectData, function(key, value){
                dataGrid.dataGrid('setSelectRow', key);
            });
            initSelectTag();
        },
        onSelectRow: function(id, isSelect, event){
            if ('${parameter.checkbox}' == 'true'){
                if(isSelect){
                    selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                }else{
                    delete selectData[id];
                }
            }else{
                selectData = {};
                selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
            }
            initSelectTag();
        },
        onSelectAll: function(ids, isSelect){
            if ('${parameter.checkbox}' == 'true'){
                for (var i=0; i<ids.length; i++){
                    if(isSelect){
                        selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
                    }else{
                        delete selectData[ids[i]];
                    }
                }
            }
            initSelectTag();
        },
        ondblClickRow: function(id, rownum, colnum, event){
            if ('${parameter.checkbox}' != 'true'){
                js.layer.$('#' + window.name).closest('.layui-layer')
                    .find(".layui-layer-btn0").trigger("click");
            }
            initSelectTag();
        }
    });
    function initSelectTag(){
        selectNum = 0;
        var html = [];
        $.each(selectData, function(key, value){
            selectNum ++;
            html.push('<span class="tag" id="'+key+'_tags-input"><span>'+value.name+' </span>'
                + '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
        });
        html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
        $('#selectData').empty().append(html.join(''));
    }
    function removeSelectTag(key){
        delete selectData[key];
        dataGrid.dataGrid('resetSelection', key);
        $('#selectNum').html(--selectNum);
        $('#'+key+'_tags-input').remove();
    }
    function getSelectData(){
        return selectData;
    }
</script>
