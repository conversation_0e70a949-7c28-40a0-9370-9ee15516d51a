package com.topdon.admin.vo;

import com.topdon.admin.entity.InformationGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class InformationGroupVo extends InformationGroup implements Comparable<InformationGroupVo> {
    private String productName;

    @Override
    public int compareTo(InformationGroupVo other) {
        int typeComparison = this.getType().compareTo(other.getType());

        if (typeComparison == 0) {
            int nameComparison = compareProductName(this.productName, other.productName);

            if (nameComparison == 0) {
                return Integer.compare(this.getSort(), other.getSort());
            }
            return nameComparison;
        }

        return typeComparison;
    }

    private int compareProductName(String name1, String name2) {
        if (name1 == null && name2 == null) {
            return 0;
        }
        if (name1 == null) {
            return -1;
        }
        if (name2 == null) {
            return 1;
        }
        return name1.compareTo(name2);
    }
}
