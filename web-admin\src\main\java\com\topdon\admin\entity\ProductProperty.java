package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品属性表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "product_property")
public class ProductProperty {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品id
     */
    @TableField(value = "product_id")
    private String productId;

    @TableField(value = "classification_compare_id")
    private Integer classificationCompareId;

    /**
     * 种类id
     */
    @TableField(value = "category_id")
    private Integer categoryId;

    /**
     * 值
     */
    @TableField(value = "val")
    private String val;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 草稿
     */
    @TableField(value = "draft")
    private Boolean draft;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "update_by")
    private Date updateBy;
}