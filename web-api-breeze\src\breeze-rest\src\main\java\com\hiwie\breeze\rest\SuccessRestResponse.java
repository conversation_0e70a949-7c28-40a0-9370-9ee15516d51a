package com.hiwie.breeze.rest;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SuccessRestResponse<T> extends AbstractRestResponse {

    public static final SuccessRestResponse<Boolean> EMPTY = new SuccessRestResponse<>(true);

    @JsonProperty
    private T success;

    private final Integer code = 2000;

    public Integer getCode() {
        return code;
    }

    public SuccessRestResponse(T success) {
        this.success = success;
    }

    public static <T> SuccessRestResponse<T> apply(T object) {
        return new SuccessRestResponse<>(object);
    }

    public T getSuccess() {
        return success;
    }

}
