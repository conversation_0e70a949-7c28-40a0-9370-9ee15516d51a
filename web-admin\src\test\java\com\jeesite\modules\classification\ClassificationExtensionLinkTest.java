package com.jeesite.modules.classification;

import com.jeesite.modules.classification.entity.ClassificationExtensionLink;
import com.jeesite.modules.classification.service.ClassificationExtensionLinkService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 分类拓展链接功能测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class ClassificationExtensionLinkTest {

    @Autowired
    private ClassificationExtensionLinkService classificationExtensionLinkService;

    @Test
    public void testSaveExtensionLinks() {
        String classificationCode = "TEST_CLASSIFICATION";
        
        // 创建测试数据
        List<ClassificationExtensionLink> extensionLinks = new ArrayList<>();
        
        ClassificationExtensionLink link1 = new ClassificationExtensionLink();
        link1.setClassificationCode(classificationCode);
        link1.setIconDefault("/images/icon1_default.png");
        link1.setIconHover("/images/icon1_hover.png");
        link1.setNavText("测试导航文案1");
        link1.setCategoryText("测试二级品类文案1");
        link1.setSortOrder(1);
        link1.setIsDisplay("1");
        link1.setJumpLink("https://example.com/link1");
        extensionLinks.add(link1);
        
        ClassificationExtensionLink link2 = new ClassificationExtensionLink();
        link2.setClassificationCode(classificationCode);
        link2.setIconDefault("/images/icon2_default.png");
        link2.setIconHover("/images/icon2_hover.png");
        link2.setNavText("测试导航文案2");
        link2.setCategoryText("测试二级品类文案2");
        link2.setSortOrder(2);
        link2.setIsDisplay("1");
        link2.setJumpLink("https://example.com/link2");
        extensionLinks.add(link2);
        
        // 保存拓展链接
        classificationExtensionLinkService.saveExtensionLinks(classificationCode, extensionLinks);
        
        // 查询验证
        List<ClassificationExtensionLink> savedLinks = classificationExtensionLinkService.findByClassificationCode(classificationCode);
        
        assert savedLinks != null;
        assert savedLinks.size() == 2;
        assert savedLinks.get(0).getSortOrder() == 1;
        assert savedLinks.get(1).getSortOrder() == 2;
        
        System.out.println("拓展链接功能测试通过！");
    }
    
    @Test
    public void testUpdateExtensionLinks() {
        String classificationCode = "TEST_CLASSIFICATION_UPDATE";
        
        // 先保存一些数据
        List<ClassificationExtensionLink> extensionLinks = new ArrayList<>();
        ClassificationExtensionLink link1 = new ClassificationExtensionLink();
        link1.setClassificationCode(classificationCode);
        link1.setNavText("原始文案");
        link1.setCategoryText("原始品类文案");
        link1.setSortOrder(1);
        link1.setIsDisplay("1");
        link1.setJumpLink("https://example.com/original");
        extensionLinks.add(link1);
        
        classificationExtensionLinkService.saveExtensionLinks(classificationCode, extensionLinks);
        
        // 更新数据
        List<ClassificationExtensionLink> updatedLinks = new ArrayList<>();
        ClassificationExtensionLink updatedLink = new ClassificationExtensionLink();
        updatedLink.setClassificationCode(classificationCode);
        updatedLink.setNavText("更新后文案");
        updatedLink.setCategoryText("更新后品类文案");
        updatedLink.setSortOrder(1);
        updatedLink.setIsDisplay("1");
        updatedLink.setJumpLink("https://example.com/updated");
        updatedLinks.add(updatedLink);
        
        classificationExtensionLinkService.saveExtensionLinks(classificationCode, updatedLinks);
        
        // 验证更新结果
        List<ClassificationExtensionLink> savedLinks = classificationExtensionLinkService.findByClassificationCode(classificationCode);
        
        assert savedLinks != null;
        assert savedLinks.size() == 1;
        assert "更新后文案".equals(savedLinks.get(0).getNavText());
        
        System.out.println("拓展链接更新功能测试通过！");
    }
}
