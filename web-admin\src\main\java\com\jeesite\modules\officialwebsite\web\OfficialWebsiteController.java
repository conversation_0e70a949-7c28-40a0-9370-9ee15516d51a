package com.jeesite.modules.officialwebsite.web;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.officialwebsite.entity.OfficialWebsite;
import com.jeesite.modules.officialwebsite.service.OfficialWebsiteService;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 官网站点管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-03
 */
@Controller
@RequestMapping(value = "${adminPath}/officialWebsite/officialWebsite")
public class OfficialWebsiteController extends BaseController {

    @Autowired
    private OfficialWebsiteService officialWebsiteService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public OfficialWebsite get(String code, boolean isNewRecord) {
        return officialWebsiteService.get(code, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("officialWebsite:officialWebsite:view")
    @RequestMapping(value = {"list", ""})
    public String list(OfficialWebsite officialWebsite, Model model) {
        model.addAttribute("officialWebsite", officialWebsite);
        return "modules/officialwebsite/officialWebsiteList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("officialWebsite:officialWebsite:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public List<OfficialWebsite> listData(OfficialWebsite officialWebsite) {
        if (StringUtils.isBlank(officialWebsite.getParentCode())) {
            officialWebsite.setParentCode(OfficialWebsite.ROOT_CODE);
        }
        if (StringUtils.isNotBlank(officialWebsite.getName())) {
            officialWebsite.setParentCode(null);
        }
        if (StringUtils.isNotBlank(officialWebsite.getUrl())) {
            officialWebsite.setParentCode(null);
        }
        if (StringUtils.isNotBlank(officialWebsite.getRemarks())) {
            officialWebsite.setParentCode(null);
        }
        List<OfficialWebsite> list = officialWebsiteService.findList(officialWebsite);
        return list;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("officialWebsite:officialWebsite:view")
    @RequestMapping(value = "form")
    public String form(OfficialWebsite officialWebsite, Model model) {
        // 创建并初始化下一个节点信息
        officialWebsite = createNextNode(officialWebsite);
        model.addAttribute("officialWebsite", officialWebsite);
        return "modules/officialwebsite/officialWebsiteForm";
    }

    /**
     * 创建并初始化下一个节点信息，如：排序号、默认值
     */
    @RequiresPermissions("officialWebsite:officialWebsite:edit")
    @RequestMapping(value = "createNextNode")
    @ResponseBody
    public OfficialWebsite createNextNode(OfficialWebsite officialWebsite) {
        if (StringUtils.isNotBlank(officialWebsite.getParentCode())) {
            officialWebsite.setParent(officialWebsiteService.get(officialWebsite.getParentCode()));
        }
        if (officialWebsite.getIsNewRecord()) {
            OfficialWebsite where = new OfficialWebsite();
            where.setParentCode(officialWebsite.getParentCode());
            OfficialWebsite last = officialWebsiteService.getLastByParentCode(where);
            // 获取到下级最后一个节点
            if (last != null) {
                officialWebsite.setTreeSort(last.getTreeSort() + 30);
                officialWebsite.setCode(IdGen.nextCode(last.getCode()));
            } else if (officialWebsite.getParent() != null) {
                officialWebsite.setCode(officialWebsite.getParent().getCode() + "001");
            }
        }
        // 以下设置表单默认数据
        if (officialWebsite.getTreeSort() == null) {
            officialWebsite.setTreeSort(OfficialWebsite.DEFAULT_TREE_SORT);
        }
        return officialWebsite;
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("officialWebsite:officialWebsite:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated OfficialWebsite officialWebsite, HttpServletRequest request) {
        officialWebsiteService.save(officialWebsite);
        return renderResult(Global.TRUE, text("保存站点成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("officialWebsite:officialWebsite:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(OfficialWebsite officialWebsite) {
        officialWebsiteService.delete(officialWebsite);
        return renderResult(Global.TRUE, text("删除站点成功！"));
    }

    /**
     * 获取树结构数据
     *
     * @param excludeCode 排除的Code
     * @param isShowCode  是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
     * @return
     */
    @RequiresPermissions("region:region:view")
    @RequestMapping(value = "treeData")
    @ResponseBody
    public List<Map<String, Object>> treeData(String excludeCode, String isShowCode) {
        List<Map<String, Object>> mapList = ListUtils.newArrayList();
        OfficialWebsite officialWebsite = new OfficialWebsite();
        officialWebsite.setParentCode("0");
        List<OfficialWebsite> list = officialWebsiteService.findList(officialWebsite);
        for (int i = 0; i < list.size(); i++) {
            OfficialWebsite e = list.get(i);
            // 过滤被排除的编码（包括所有子级）
            if (StringUtils.isNotBlank(excludeCode)) {
                if (e.getId().equals(excludeCode)) {
                    continue;
                }
                if (e.getParentCodes().contains("," + excludeCode + ",")) {
                    continue;
                }
            }
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("id", e.getId());
            map.put("pId", e.getParentCode());
            map.put("name", StringUtils.getTreeNodeName(isShowCode, e.getCode(), e.getName()));
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 修复表结构相关数据
     */
    @RequiresPermissions("officialWebsite:officialWebsite:edit")
    @RequestMapping(value = "fixTreeData")
    @ResponseBody
    public String fixTreeData(OfficialWebsite officialWebsite) {
        if (!UserUtils.getUser().isAdmin()) {
            return renderResult(Global.FALSE, "操作失败，只有管理员才能进行修复！");
        }
        officialWebsiteService.fixTreeData();
        return renderResult(Global.TRUE, "数据修复成功");
    }

}