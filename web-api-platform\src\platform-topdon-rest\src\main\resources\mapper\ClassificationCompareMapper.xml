<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.ClassificationCompareMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.ClassificationCompare">
    <!--@mbg.generated-->
    <!--@Table classification_compare-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="classification_code" jdbcType="VARCHAR" property="classificationCode" />
    <result column="draft" jdbcType="BOOLEAN" property="draft" />
    <result column="release_date" jdbcType="TIMESTAMP" property="releaseDate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, classification_code, draft, release_date, create_by, create_date, update_by, 
    update_date
  </sql>

  <select id="getCompareInfo" resultType="com.topdon.website.vo.ProductCompareInfoVo">
select
    if((select count(1) from product_property where product_id = (select id from product where name = #{productName,jdbcType=VARCHAR}) and draft = #{draft} limit 1)>0,true,false) compare,
          (select cc.classification_code from product_property pp left join classification_compare cc on cc.id = pp.classification_compare_id where pp.product_id = (select id from product where name = #{productName,jdbcType=VARCHAR}) and pp.draft = #{draft} and cc.draft = #{draft} limit 1) compare_classification_code
    </select>
</mapper>