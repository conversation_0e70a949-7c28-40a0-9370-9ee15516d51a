package com.topdon.website.services;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.util.StringUtil;
import com.hiwie.security.constant.CustomException;
import com.topdon.website.form.RmaOrderForm;
import com.topdon.website.helper.RequestUtil;
import com.topdon.website.model.HWFile;
import com.topdon.website.model.LogisticsInfoApiVo;
import com.topdon.website.model.RmaOrder;
import com.topdon.website.model.api.ZohoRmaOrder;
import com.topdon.website.repositories.RmaOrderRepository;
import com.topdon.website.repositories.RmaOrderStatusLogRepository;
import com.topdon.website.service.ProductService;
import com.topdon.website.utils.LockPool;
import com.zoho.desk.contact.Contact;
import com.zoho.desk.department.Department;
import com.zoho.desk.department.DepartmentAPI;
import com.zoho.desk.init.ZDesk;
import com.zoho.desk.ticket.Ticket;
import com.zoho.desk.upload.Upload;
import com.zoho.desk.upload.UploadAPI;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.topdon.website.constant.ErrorCode.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3 17:31
 */
@Service
@Slf4j
public class RmaOrderService {

    @Autowired
    private RmaOrderRepository rmaOrderRepository;
    @Autowired
    private RmaOrderStatusLogRepository rmaOrderStatusLogRepository;
    @Resource
    private ProductService productService;

    @Value("${zoho.config.userIdentifier}")
    private String userIdentifier;

    public AbstractEither<ErrorMessage, String> add(long userId, RmaOrderForm rmaOrderForm) {
        rmaOrderForm.setChannel("Offcial website form");
        Map<String, Object> jsonmap = Maps.newHashMap();
        Contact contact = new Contact();
        contact.setLastName(rmaOrderForm.getSellerName());
        contact.setEmail(rmaOrderForm.getEmail());
        jsonmap.put("contact", contact);

        Ticket ticket = new Ticket(new JSONObject(jsonmap));
        ticket.setSubject(Optional.ofNullable(rmaOrderForm.getTitle()).filter(StrUtil::isNotEmpty).orElseGet(() -> "Official website"));
        ticket.setDepartmentId("903889000000429131");
        ticket.setContactId("");
        ticket.setStatus("Open");
        ticket.setDescription(rmaOrderForm.getDescription());
        ticket.setCf("cf_sn", rmaOrderForm.getSn());
        ticket.setCf("cf_wen_ti_lei_bie", rmaOrderForm.getIssueType());
        ticket.setCf("cf_product_type", rmaOrderForm.getClassificationName());
        ticket.setCf("cf_chan_pin_xing_hao", rmaOrderForm.getProductName());
        ticket.setCf("cf_problem_description", rmaOrderForm.getDescription());
        ticket.setCf("cf_ping_tai_ding_dan_hao", rmaOrderForm.getOrderNo());
        ticket.setCf("cf_which_is_your_country", rmaOrderForm.getCountry());
        ticket.setCf("cf_shipping_address_city", rmaOrderForm.getCity());
        ticket.setCf("cf_shipping_address_state_or_region", rmaOrderForm.getStateRegion());
        ticket.setCf("cf_shipping_address", rmaOrderForm.getAddress1());
        ticket.setCf("cf_shipping_address_postal_code", rmaOrderForm.getPostalCode());
        ticket.setCf("cf_user_id", rmaOrderForm.getSellerName());
        ticket.setCf("cf_platform", rmaOrderForm.getPlatform());
        ticket.setCf("cf_jing_xiao_shang_ming_cheng", rmaOrderForm.getJingxiaoshang());
        ticket.setCf("cf_che_xing", rmaOrderForm.getChexing());
        ticket.setChannel(rmaOrderForm.getChannel());
        ticket.setEmail(rmaOrderForm.getEmail());
        ticket.setPhone(rmaOrderForm.getPhone());
        ticket.setUploads(rmaOrderForm.getUploads());

        Ticket response = ZDesk.getTicketAPIInstance(userIdentifier).createTicket(ticket);
        long id = rmaOrderRepository.add(response.getTicketNumber(), response.getId(), userId, rmaOrderForm);
        rmaOrderStatusLogRepository.add((int) id, response.getTicketNumber(), "Pending Review", Optional.ofNullable(rmaOrderForm.getCreateTime()).orElseGet(Date::new));
        return Right.apply(response.getTicketNumber());
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public AbstractEither<ErrorMessage, Long> add(ZohoRmaOrder zohoRmaOrder) {
        RmaOrderForm rmaOrderForm = new RmaOrderForm();
        rmaOrderForm.setCreateTime(zohoRmaOrder.getCreatedTime());
        rmaOrderForm.setSn(zohoRmaOrder.getCf_sn());
        rmaOrderForm.setIssueType(zohoRmaOrder.getCf_wen_ti_lei_bie());
        rmaOrderForm.setProductName(zohoRmaOrder.getCf_chan_pin_xing_hao());
        rmaOrderForm.setOrderNo(zohoRmaOrder.getCf_please_tell_us_your_order_number());
        rmaOrderForm.setJingxiaoshang(zohoRmaOrder.getCf_jing_xiao_shang_ming_cheng());
        rmaOrderForm.setPlatform(zohoRmaOrder.getCf_platform());
        rmaOrderForm.setCountry(zohoRmaOrder.getCf_which_is_your_country());
        rmaOrderForm.setStateRegion(zohoRmaOrder.getCf_shipping_address_state_or_region());
        rmaOrderForm.setCity(zohoRmaOrder.getCf_shipping_address_city());
        rmaOrderForm.setAddress1(zohoRmaOrder.getCf_your_address_1());
        rmaOrderForm.setPostalCode(zohoRmaOrder.getCf_shipping_address_postal_code());
        rmaOrderForm.setPhone(zohoRmaOrder.getPhone());
        rmaOrderForm.setEmail(zohoRmaOrder.getEmail());
        rmaOrderForm.setDescription(zohoRmaOrder.getCf_problem_description());
        synchronized (LockPool.getObject(zohoRmaOrder.getTicketNumber(), 10, TimeUnit.MINUTES)) {
            return Right.apply(rmaOrderRepository.getByTicketNumber(zohoRmaOrder.getTicketNumber()).map(RmaOrder::getId).map(Integer::longValue)
                    .getOrElse(() -> rmaOrderRepository.add(zohoRmaOrder.getTicketNumber(), zohoRmaOrder.getId(), 0L, rmaOrderForm)));
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public Integer update(ZohoRmaOrder zohoRmaOrder) {
        Optional.ofNullable(zohoRmaOrder.getCf_zhong_tai_rma_zhuang_tai()).filter(StringUtil::isNotEmpty).orElseThrow(() -> new CustomException(RMA_STATUS_NOT_NULL));
        Optional.ofNullable(zohoRmaOrder.getModifiedTime()).orElseThrow(() -> new CustomException(RMA_STATUS_TIME_NOT_NULL));
        synchronized (LockPool.getObject(zohoRmaOrder.getTicketNumber(), 10, TimeUnit.MINUTES)) {
            return rmaOrderRepository.getByTicketNumber(zohoRmaOrder.getTicketNumber()).map(o -> {
                if (StrUtil.isBlank(zohoRmaOrder.getCf_user_id())) {
                    zohoRmaOrder.setCf_user_id(o.getSellerName());
                }
                if (StrUtil.isBlank(zohoRmaOrder.getCf_tui_hui_gen_zong_hao())) {
                    zohoRmaOrder.setCf_tui_hui_gen_zong_hao(o.getTuiHuiHenZongHao());
                }
                if (StrUtil.isBlank(zohoRmaOrder.getCf_fa_huo_wu_liu_dan_hao())) {
                    zohoRmaOrder.setCf_fa_huo_wu_liu_dan_hao(o.getHuoWuLiuDanHao());
                }
                if (StrUtil.isBlank(zohoRmaOrder.getCf_ping_tai_ding_dan_hao())) {
                    zohoRmaOrder.setCf_ping_tai_ding_dan_hao(o.getOrderNo());
                }
                if (StrUtil.isBlank(zohoRmaOrder.getCf_chu_li_fang_an())) {
                    zohoRmaOrder.setCf_chu_li_fang_an(o.getChuLiFangAn());
                }
                int id = rmaOrderRepository.update(zohoRmaOrder);
                if (!Objects.equals(o.getTicketStatus(), zohoRmaOrder.getCf_zhong_tai_rma_zhuang_tai())) {
                    rmaOrderStatusLogRepository.add(o.getId(), zohoRmaOrder.getTicketNumber(), zohoRmaOrder.getCf_zhong_tai_rma_zhuang_tai(), zohoRmaOrder.getModifiedTime());
                }
                return id;
            }).orNull();
        }
    }

    public AbstractEither<ErrorMessage, Upload> uploadFile(HttpServletRequest request, MultipartFile file) {
        /*CreateTicketAttachmentFilter createTicketAttachmentFilter = new CreateTicketAttachmentFilter.Builder().setIsPublic(true).build();
        AttachmentAPI attachmentAPI = ZDesk.getAttachmentAPIInstance(userIdentifier);
        attachmentAPI.createTicketAttachment(file, "", createTicketAttachmentFilter);*/
        File file1 = null;
        try {
            file1 = File.createTempFile(FileUtil.mainName(file.getOriginalFilename()) + "_" + new Date().getTime(), "." + FileUtil.getSuffix(file.getOriginalFilename()));
            file.transferTo(file1);
            UploadAPI uploadAPI = ZDesk.getUploadAPIInstance(userIdentifier);
            Upload upload = uploadAPI.uploadFile(file1);
            return Right.apply(upload);
        } catch (IOException e) {
            log.error("uploadFile transferTo temp file error!", e);
            return Left.apply(HWFile.Errors.UPLOAD_FAILED);
        } finally {
            if (file1.exists()) {
                file1.delete();
            }
        }
    }

    public AbstractEither<ErrorMessage, List<Department>> getDepartments() {
        DepartmentAPI departmentAPI = ZDesk.getDepartmentAPIInstance(userIdentifier);
        List<Department> departments = departmentAPI.getDepartments(0, 20, null);
        return Right.apply(departments);
    }

    public AbstractEither<ErrorMessage, RmaOrder> getInfo(ZohoRmaOrder zohoRmaOrder) {
        if (StrUtil.isEmpty(zohoRmaOrder.getEmail()) || StrUtil.isEmpty(zohoRmaOrder.getTicketNumber())) {
            return Left.apply(RmaOrder.Errors.NOT_FOUND);
        }
        String email = Optional.ofNullable(zohoRmaOrder.getEmail()).orElseThrow(() -> new CustomException(RMA_EMAIL_NOT_NULL));
        String ticketNumber = Optional.ofNullable(zohoRmaOrder.getTicketNumber()).orElseThrow(() -> new CustomException(RMA_TICKET_NUMBER_NOT_NULL));
        RmaOrder rmaOrder = rmaOrderRepository.getByTicketNumber(ticketNumber).filter(o -> email.equals(o.getEmail())).map(o -> {
            o.setRmaOrderStatusLogList(rmaOrderStatusLogRepository.getByRmaOrderId(o.getId()));
            return o;
        }).orNull();
        if (null == rmaOrder) {
            return Left.apply(RmaOrder.Errors.NOT_FOUND);
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("trackNo", rmaOrder.getHuoWuLiuDanHao());

        AbstractEither<ErrorMessage, LogisticsInfoApiVo> infoApiVoAbstractEither = RequestUtil.postWithLogisticsInfo("/api/logistics/info/getInfoApi", params);
        infoApiVoAbstractEither.ifRight(infoApiVo -> {
            if (null != infoApiVo) {
                rmaOrder.setLogisticsInfoApiVo(infoApiVo.getData());
            }
        });
        productService.getProductInfoByName(rmaOrder.getProductName()).ifRight(productInfo -> {
            if (productInfo != null) {
                rmaOrder.setProductMobileCover(productInfo.getMobileCover());
            }
        });

        return Right.apply(rmaOrder);
    }
}
