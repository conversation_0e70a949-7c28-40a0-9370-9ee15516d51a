<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.MenuClickLogMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.MenuClickLog">
    <!--@mbg.generated-->
    <!--@Table menu_click_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="ip_location" jdbcType="VARCHAR" property="ipLocation" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="click_time" jdbcType="TIMESTAMP" property="clickTime" />
    <result column="user_agent" jdbcType="LONGVARCHAR" property="userAgent" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ip_address, ip_location, menu_name, product_name, click_time, user_agent
  </sql>
</mapper>