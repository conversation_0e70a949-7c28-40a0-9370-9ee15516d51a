<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.ClassificationExtensionLinkMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.ClassificationExtensionLink">
    <!--@mbg.generated-->
    <!--@Table classification_extension_link-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="classification_code" jdbcType="VARCHAR" property="classificationCode" />
    <result column="icon_default" jdbcType="VARCHAR" property="iconDefault" />
    <result column="icon_hover" jdbcType="VARCHAR" property="iconHover" />
    <result column="nav_text" jdbcType="VARCHAR" property="navText" />
    <result column="category_text" jdbcType="VARCHAR" property="categoryText" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="is_display" jdbcType="CHAR" property="isDisplay" />
    <result column="jump_link" jdbcType="VARCHAR" property="jumpLink" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, classification_code, icon_default, icon_hover, nav_text, category_text, sort_order, 
    is_display, jump_link, create_by, create_date, update_by, update_date, remarks, del_flag
  </sql>
</mapper>