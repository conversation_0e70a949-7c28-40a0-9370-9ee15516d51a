package com.topdon.website.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.Product;

import java.util.List;

public interface ProductService extends IService<Product>{


    List<Product> getCompareProductList(String classificationCode, boolean draft);

    AbstractEither<ErrorMessage, Product> getProductInfoByName(String name);
}
