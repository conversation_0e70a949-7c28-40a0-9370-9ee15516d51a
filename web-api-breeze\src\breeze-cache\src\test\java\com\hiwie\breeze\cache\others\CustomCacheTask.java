package com.hiwie.breeze.cache.others;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.None;
import com.hiwie.breeze.Some;
import com.hiwie.breeze.cache.AbstractCache;
import com.hiwie.breeze.json.Json;
import com.hiwie.breeze.util.StringUtil;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;

import java.time.Duration;
import java.util.function.Function;

public class CustomCacheTask extends AbstractCache {

    protected CustomCacheTask() {
        super(StringUtil.join("test",PREFIX_SEPARATOR,"custom"), 0, Some.apply(Duration.parse("PT3S")));
    }

    public Function<Jedis, AbstractOption<ErrorMessage>> set(String key, Object value) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            pipeline.set(the<PERSON><PERSON>, Json.writeValueAsString(value));
            if(expireSeconds > 0){
                pipeline.expire(theKey,expireSeconds);
            }
            pipeline.sync();
            return None.apply();
        };
    }

}
