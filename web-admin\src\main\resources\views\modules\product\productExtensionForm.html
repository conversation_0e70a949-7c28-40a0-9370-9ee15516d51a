<% layout('/layouts/default.html', {title: '产品参数管理', libs: ['validate','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(productExtension.isNewRecord ? '新增产品参数' : '编辑产品参数')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${productExtension}" action="${ctx}/product/productExtension/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id" />
				<#form:hidden path="content"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('分组名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('产品')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="testUser3" title="用户选择" path="product.id" labelPath="product.name"
								url="${ctx}/product/product/select" allowClear="false"
								checkbox="false" itemCode="id" itemName="name" class="required"/>
							</div>
						</div>
					</div>
				</div>

				<h4 class="form-unit">${text('参数列表')}</h4>
				<div class="ml10 mr10 table-form">
					<table id="paramDataGrid"></table>
					<% if (hasPermi('product:productExtension:edit')){ %>
					<a href="#" id="paramDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
					<% } %>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('product:productExtension:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>

	//初始化测试数据子表DataGrid对象
	$("#paramDataGrid").dataGrid({
		data: ${toJson(productExtension.params)},
		datatype: "local", // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度
		// 设置数据表格列
		columnModel: [
			{header:'${text("操作")}', name:'actions', width:40, align:"center", formatter: function(val, obj, row, act){
					var actions = [];
					if (val == 'new'){
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#paramDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
					}else{
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#paramDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}},
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("父表主键")}', name:'productExtension.id', editable:true, hidden:true},
			{header:'${text("参数名")}', name:'key', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'300', 'class':'form-control required'}},
			{header:'${text("参数值")}', name:'value', width:100, editable:true, edittype:'textarea', editoptions:{'maxlength':'400', 'class':'form-control required'}}
		],
		shrinkToFit: false,	// 是否按百分比自动调整列宽

		// 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#paramDataGridAddRowBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上 v4.1.7
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		// 编辑表格的提交数据参数
		editGridInputFormListName: 'params', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,productExtension.id,key,value,testDataChild_file__del', // 提交数据列表的属性字段

		// 加载成功后执行事件
		ajaxSuccess: function(data){
		}
	});

$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>