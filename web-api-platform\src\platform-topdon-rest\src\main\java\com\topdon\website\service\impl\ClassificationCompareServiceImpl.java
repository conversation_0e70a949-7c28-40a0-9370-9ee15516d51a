package com.topdon.website.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.entity.ClassificationCompare;
import com.topdon.website.mapper.ClassificationCompareMapper;
import com.topdon.website.service.ClassificationCompareService;
import com.topdon.website.vo.ProductCompareInfoVo;
import org.springframework.stereotype.Service;
@Service
public class ClassificationCompareServiceImpl extends ServiceImpl<ClassificationCompareMapper, ClassificationCompare> implements ClassificationCompareService{

    @Override
    public boolean existCompare(String code) {
        return lambdaQuery()
                .eq(ClassificationCompare::getClassificationCode,code)
                .eq(ClassificationCompare::getDraft,false)
                .exists();
    }

    public ProductCompareInfoVo getCompareInfo(String productName,boolean draft){
        return this.baseMapper.getCompareInfo(productName,draft);
    }
}
