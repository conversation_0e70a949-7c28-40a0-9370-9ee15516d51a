package com.topdon.website.controller;

import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.RecruitmentQueryForm;
import com.topdon.website.services.RecruitmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/recruitments")
public class RecruitmentsController extends ControllerSupport {

    private final RecruitmentService recruitmentService;

    @Autowired
    public RecruitmentsController(RecruitmentService recruitmentService) {
        this.recruitmentService = recruitmentService;
    }

    @GetMapping
    public AbstractRestResponse list(RecruitmentQueryForm queryForm, PaginationForm form) {
        return AbstractRestResponse.apply(recruitmentService.list(queryForm, form));
    }
}
