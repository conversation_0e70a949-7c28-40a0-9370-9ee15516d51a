<% layout('/layouts/default.html', {title: '配置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('配置管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('website:websiteSetting:edit')){ %>
					<a href="${ctx}/website/websiteSetting/form" class="btn btn-default btnTool" title="${text('新增配置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${websiteSetting}" action="${ctx}/website/websiteSetting/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('关键字')}：</label>
					<div class="control-inline">
						<#form:input path="key" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('显示值')}：</label>
					<div class="control-inline">
						<#form:input path="value" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("关键字")}', name:'key', index:'a.key', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/website/websiteSetting/form?id='+row.id+'" class="btnList" data-title="${text("编辑配置")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("显示值")}', name:'value', index:'a.value', width:150, align:"left"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('website:websiteSetting:edit')){ %>
				actions.push('<a href="${ctx}/website/websiteSetting/form?id='+row.id+'" class="btnList" title="${text("编辑配置")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/website/websiteSetting/delete?id='+row.id+'" class="btnList" title="${text("删除配置")}" data-confirm="${text("确认要删除该配置吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>