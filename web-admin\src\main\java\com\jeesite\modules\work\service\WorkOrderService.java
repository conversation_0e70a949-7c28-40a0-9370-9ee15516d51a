package com.jeesite.modules.work.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.work.entity.WorkOrder;
import com.jeesite.modules.work.dao.WorkOrderDao;

/**
 * 工单管理Service
 * <AUTHOR>
 * @version 2022-03-10
 */
@Service
@Transactional(readOnly=true)
public class WorkOrderService extends CrudService<WorkOrderDao, WorkOrder> {
	
	/**
	 * 获取单条数据
	 * @param workOrder
	 * @return
	 */
	@Override
	public WorkOrder get(WorkOrder workOrder) {
		return super.get(workOrder);
	}
	
	/**
	 * 查询分页数据
	 * @param workOrder 查询条件
	 * @param workOrder.page 分页对象
	 * @return
	 */
	@Override
	public Page<WorkOrder> findPage(WorkOrder workOrder) {
		return super.findPage(workOrder);
	}
	
	/**
	 * 查询列表数据
	 * @param workOrder
	 * @return
	 */
	@Override
	public List<WorkOrder> findList(WorkOrder workOrder) {
		return super.findList(workOrder);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param workOrder
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(WorkOrder workOrder) {
		super.save(workOrder);
	}
	
	/**
	 * 更新状态
	 * @param workOrder
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(WorkOrder workOrder) {
		super.updateStatus(workOrder);
	}
	
	/**
	 * 删除数据
	 * @param workOrder
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(WorkOrder workOrder) {
		super.delete(workOrder);
	}
	
}