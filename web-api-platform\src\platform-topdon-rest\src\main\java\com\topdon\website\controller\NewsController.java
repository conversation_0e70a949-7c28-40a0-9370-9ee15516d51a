package com.topdon.website.controller;

import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.NewsQueryForm;
import com.topdon.website.services.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/newses")
public class NewsController extends ControllerSupport {

    private final NewsService newsService;

    @Autowired
    public NewsController(NewsService newsService) {
        this.newsService = newsService;
    }

    @GetMapping
    public AbstractRestResponse list(NewsQueryForm queryForm, PaginationForm form) {
        return AbstractRestResponse.apply(newsService.list(queryForm, form));
    }

    @GetMapping("/count")
    public AbstractRestResponse count(NewsQueryForm queryForm) {
        return AbstractRestResponse.apply(newsService.count(queryForm));
    }
}
