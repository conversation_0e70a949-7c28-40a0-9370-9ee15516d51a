package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.model.WebsiteSetting;
import com.topdon.website.repositories.WebsiteSettingRepository;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class WebsiteSettingService {
    private final WebsiteSettingRepository websiteSettingRepository;

    @Inject
    public WebsiteSettingService(WebsiteSettingRepository websiteSettingRepository) {
        this.websiteSettingRepository = websiteSettingRepository;
    }

    public AbstractEither<ErrorMessage, WebsiteSetting> get(String key) {
        return websiteSettingRepository.option(key).toRight(WebsiteSetting.Errors.NOT_FOUND);
    }
}
