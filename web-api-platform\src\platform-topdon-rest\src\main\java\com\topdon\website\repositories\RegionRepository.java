package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.RegionMapper;
import com.topdon.website.model.Region;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class RegionRepository extends MysqlJDBCSupport {

    @Inject
    protected RegionRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<Region> list(String parentId) {
        @Language("SQL") String sql = "select r.code, r.name, r.parent_code,r2.name parent_name,r2.flag parent_flag, r.parent_codes, r.tree_names,r.create_date,r.flag from region r left join region r2 on r.parent_code = r2.code where r.parent_code = :parentId order by r.tree_sort";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("parentId", parentId);
        return list(sql, RegionMapper.DETAIL, params);
    }

    public AbstractOption<Region> get(String id) {
        @Language("SQL") String sql = "select r.code, r.name, r.parent_code,r2.name parent_name,r2.flag parent_flag, r.parent_codes, r.tree_names,r.create_date,r.flag from region r left join region r2 on r.parent_code = r2.code where r.code = :id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        return option(sql, RegionMapper.DETAIL, params);
    }

}
