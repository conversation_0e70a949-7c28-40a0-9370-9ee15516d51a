package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.form.OrderQueryForm;
import com.topdon.website.form.TopdonPageForm;
import com.topdon.website.services.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("orders")
public class OrdersController extends ControllerSupport {

    private final OrderService orderService;

    @Autowired
    public OrdersController(OrderService orderService) {
        this.orderService = orderService;
    }

    @GetMapping
    public AbstractRestResponse list(@HWSession Session session, TopdonPageForm pageForm, OrderQueryForm queryForm) {
        return AbstractRestResponse.apply(orderService.list(session, pageForm,queryForm));
    }

    @RequestMapping("/sync")
    public AbstractRestResponse sync() {
        return AbstractRestResponse.apply(orderService.sync());
    }
}
