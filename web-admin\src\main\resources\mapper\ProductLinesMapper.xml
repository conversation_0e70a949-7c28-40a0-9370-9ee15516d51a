<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.ProductLinesMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.ProductLines">
    <!--@mbg.generated-->
    <!--@Table product_lines-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, `type`, `name`, create_by, create_date, update_by, update_date
  </sql>
</mapper>