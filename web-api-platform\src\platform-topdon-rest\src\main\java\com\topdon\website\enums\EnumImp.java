package com.topdon.website.enums;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/8 14:29
 */
public class EnumImp {

    public enum DictType implements EnumInter {
        BUY_CHANNEL("buy_channel", "购买渠道");

        private String value;
        private String label;

        DictType(String value, String label) {
            this.value = value;
            this.label = label;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }
}
