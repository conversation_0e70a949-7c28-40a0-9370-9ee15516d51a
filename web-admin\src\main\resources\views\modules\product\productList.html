<% layout('/layouts/default.html', {title: '产品管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('产品管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('product:product:edit')){ %>
					<a href="${ctx}/product/product/form" class="btn btn-default btnTool" title="${text('新增产品')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${product}" action="${ctx}/product/product/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('产品名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="40" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("产品名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/product/product/form?id='+row.id+'" class="btnList" data-title="${text("编辑产品")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("描述")}', name:'description', index:'a.description', width:150, align:"left"},
		{header:'${text("新品")}', name:'newProduct', index:'a.new_product', width:60, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("是否停产")}', name:'discontinued', index:'a.discontinued', width:80, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("允许购买")}', name:'allowPurchase', index:'a.allow_purchase', width:80, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("分类")}', name:'classifications', width:150, align:"center",formatter:function (val,obj,row,act) {
				var r = '';
				if (undefined != val) {
					for (var i = 0; i < val.length; i++) {
						r = r + val[i].name;
						if (i < val.length - 1) {
							r = r + ','
						}
					}
				}
				return r;
		}},
		{header:'${text("排序")}', name:'sort', index:'a.sort', width:150, align:"center"},
		{header:'${text("搜索结果页封面")}', name:'searchCover', index:'a.search_cover', width:150, align:"left",formatter:function (val,obj,row,act) {
            if (val){
                return '<img width="50%" src="' + encodeURI(val) + '"/>';
            }else{
                return "缺失";
            }

            }},
		{header:'${text("产品列表封面")}', name:'cover', index:'a.cover', width:150, align:"left",formatter:function (val,obj,row,act) {
                if (val){
                    return '<img width="50%" src="' + encodeURI(val) + '"/>';
                }else{
                    return "缺失";
                }

            }},
		{header:'${text("搜索结果页展示")}', name:'searchView', index:'a.searchView', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("RMA工具展示")}', name:'rmaToolShow', index:'a.rmaToolShow', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("创建时间")}', name:'createDate', index:'a.createDate', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('product:product:edit')){ %>
				actions.push('<a href="${ctx}/product/product/form?id='+row.id+'" class="btnList" title="${text("编辑产品")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/product/product/disable?id='+row.id+'" class="btnList" title="${text("停用产品")}" data-confirm="${text("确认要停用该产品吗？")}"><i class="glyphicon glyphicon-ban-circle"></i></a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/product/product/enable?id='+row.id+'" class="btnList" title="${text("启用产品")}" data-confirm="${text("确认要启用该产品吗？")}"><i class="glyphicon glyphicon-ok-circle"></i></a>&nbsp;');
				}
				actions.push('<a href="${ctx}/product/product/delete?id='+row.id+'" class="btnList" title="${text("删除产品")}" data-confirm="${text("确认要删除该产品吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
</script>