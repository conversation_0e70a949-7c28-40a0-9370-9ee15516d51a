package com.jeesite.modules.footer.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.TreeService;
import com.jeesite.modules.footer.entity.FooterMenu;
import com.jeesite.modules.footer.dao.FooterMenuDao;

/**
 * 底部菜单Service
 * <AUTHOR>
 * @version 2022-04-25
 */
@Service
@Transactional(readOnly=true)
public class FooterMenuService extends TreeService<FooterMenuDao, FooterMenu> {
	
	/**
	 * 获取单条数据
	 * @param footerMenu
	 * @return
	 */
	@Override
	public FooterMenu get(FooterMenu footerMenu) {
		return super.get(footerMenu);
	}
	
	/**
	 * 查询分页数据
	 * @param footerMenu 查询条件
	 * @param footerMenu.page 分页对象
	 * @return
	 */
	@Override
	public Page<FooterMenu> findPage(FooterMenu footerMenu) {
		return super.findPage(footerMenu);
	}
	
	/**
	 * 查询列表数据
	 * @param footerMenu
	 * @return
	 */
	@Override
	public List<FooterMenu> findList(FooterMenu footerMenu) {
		return super.findList(footerMenu);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param footerMenu
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(FooterMenu footerMenu) {
		super.save(footerMenu);
	}
	
	/**
	 * 更新状态
	 * @param footerMenu
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(FooterMenu footerMenu) {
		super.updateStatus(footerMenu);
	}
	
	/**
	 * 删除数据
	 * @param footerMenu
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(FooterMenu footerMenu) {
		super.delete(footerMenu);
	}
	
}