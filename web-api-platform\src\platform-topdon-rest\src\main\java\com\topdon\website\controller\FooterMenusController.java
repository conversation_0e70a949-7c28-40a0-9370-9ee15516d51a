package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.FooterMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/footers")
public class FooterMenusController extends ControllerSupport {

    private final FooterMenuService footerMenuService;

    @Autowired
    public FooterMenusController(FooterMenuService footerMenuService) {
        this.footerMenuService = footerMenuService;
    }

    @GetMapping
    public AbstractRestResponse list() {
        return AbstractRestResponse.apply(footerMenuService.list());
    }
}
