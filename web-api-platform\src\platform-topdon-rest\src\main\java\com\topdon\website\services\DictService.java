package com.topdon.website.services;

import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.model.Dict;
import com.topdon.website.repositories.DictRepository;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/8 14:31
 */
@Named
public class DictService {

    private static Map<String, List<Dict>> dictMap = Maps.newConcurrentMap();

    @Autowired
    private DictRepository dictRepository;


    public AbstractEither<ErrorMessage, List<Dict>> getDictByType(String type) {
        return Right.apply(Optional.of(type).map(dictMap::get).orElseGet(() -> {
            List<Dict> dicts = dictRepository.getDictByType(type);
            dictMap.putIfAbsent(type, dicts);
            return dicts;
        }));
    }

    public void cleanType(String type) {
        dictMap.remove(type);
        this.getDictByType(type);
    }

    public static Optional<Map<String, String>> getDictMapByType(String type) {
        return Optional.ofNullable(dictMap.get(type)).map(l -> l.stream().collect(Collectors.toMap(Dict::getValue, Dict::getLabel)));
    }
}
