package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;

public class HWFile {

    public static final String ENTITY_NAME = "FILE";

    private long id;
    private String name;
    private String mime;
    private long size;
    private long createBy;
    private LocalDateTime createAt;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(long createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public String getMime() {
        return mime;
    }

    public void setMime(String mime) {
        this.mime = mime;
    }

    public static class Errors {

        public static final ErrorMessage UPLOAD_FAILED = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "UPLOAD_FAILED");

        public static final ErrorMessage INVALID_MIME = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "INVALID_MIME");

        public static final ErrorMessage INVALID_SUFFIX = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "INVALID_SUFFIX");

        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUND");

    }

}
