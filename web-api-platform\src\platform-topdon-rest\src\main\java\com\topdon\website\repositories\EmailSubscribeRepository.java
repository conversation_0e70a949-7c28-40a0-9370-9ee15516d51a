package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.form.SubscribeForm;
import com.topdon.website.model.Subscribe;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class EmailSubscribeRepository extends MysqlJDBCSupport {

    @Inject
    protected EmailSubscribeRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long create(SubscribeForm form) {
        @Language("SQL") String sql = "insert into email_subscribe(email,  create_at, `from`, site,copied_discount_code) values (:email,now(),:from,:site,:copied_discount_code)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("email", form.getEmail());
        params.addValue("from", form.getFrom().name());
        params.addValue("site", form.getSiteFrom().name());

        // 当【订阅来源】为 EU官网 或 AU官网，且【功能】为 弹窗 时，默认【复制折扣码】字段为“否”，其余情况为空
        if (form.getFrom() == Subscribe.SubscribeFrom.POP && (form.getSiteFrom() == Subscribe.SubscribeSiteFrom.AU ||
                form.getSiteFrom() == Subscribe.SubscribeSiteFrom.EU)) {
            params.addValue("copied_discount_code", 0);
        }else {
            params.addValue("copied_discount_code", null);
        }

        return autoIncreaseInsert(sql, params).longValue();
    }

    public Integer copiedDiscountCode(SubscribeForm form) {
        if ((form.getSiteFrom() != Subscribe.SubscribeSiteFrom.AU &&
                form.getSiteFrom() != Subscribe.SubscribeSiteFrom.EU) ||
                form.getFrom() != Subscribe.SubscribeFrom.POP) {
            return 0;
        }

        @Language("SQL") String sql = "update email_subscribe set copied_discount_code = 1 where email =:email and `from` = :from and site = :site";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("email", form.getEmail());
        params.addValue("from", form.getFrom().name());
        params.addValue("site", form.getSiteFrom().name());

        return update(sql, params);
    }

    public boolean isExist(SubscribeForm form) {
        @Language("SQL") String sql = "select count(1) from email_subscribe where email = :email and site = :site";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("email", form.getEmail());
        params.addValue("site", form.getSiteFrom().name());
        return object(sql, Integer.class, params) > 0;
    }


}