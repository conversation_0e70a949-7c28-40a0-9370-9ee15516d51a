package com.topdon.website.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.entity.ClassificationExtensionLink;
import com.topdon.website.mapper.ClassificationExtensionLinkMapper;
import com.topdon.website.service.ClassificationExtensionLinkService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClassificationExtensionLinkServiceImpl extends ServiceImpl<ClassificationExtensionLinkMapper, ClassificationExtensionLink> implements ClassificationExtensionLinkService{

    @Override
    public List<ClassificationExtensionLink> findByClassificationCode(String classificationCode) {
        return this.lambdaQuery()
                .eq(ClassificationExtensionLink::getClassificationCode, classificationCode)
                .eq(ClassificationExtensionLink::getIsDisplay, "1") // 只查询展示的链接
                .orderByAsc(ClassificationExtensionLink::getSortOrder)
                .orderByAsc(ClassificationExtensionLink::getId)
                .list();
    }

}
