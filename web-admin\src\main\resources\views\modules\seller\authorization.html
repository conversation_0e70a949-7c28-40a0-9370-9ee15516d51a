<% layout('/layouts/default.html', {title: '授权卖家管理', libs: ['dataGrid']}){ %>

<link rel="stylesheet" href="/admin/static/element-ui/index.css">
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/element-ui/index.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<script src="/admin/static/common/vue.js"></script>

<div class="main-content hidden" id="sellerAuthorizationDataContent">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('授权卖家管理')}
            </div>
            <div class="box-tools pull-right">
                <% if(hasPermi('seller:authorization:edit')){ %>
                <el-dropdown @command="(c)=>changeStatusBatch(c)">
                    <el-button type="">
                        <i class="fa fa-edit"></i>&nbsp;批量修改状态
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="1">正式</el-dropdown-item>
                        <el-dropdown-item :command="0">草稿</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <% } %>
                <el-button @click="hideQuery = !hideQuery" style="margin-left: 10px">
                    <i class="fa fa-filter"></i>&nbsp;{{ hideQuery ? '查询' : '隐藏' }}
                </el-button>
                <% if(hasPermi('seller:authorization:edit')){ %>
                <el-button @click="handleAdd">
                    <i class="fa fa-plus"></i>&nbsp;新增
                </el-button>
                <% } %>
                <el-button @click="exportData">
                    <i class="fa fa-navicon"></i>&nbsp;导出
                </el-button>
                <el-popover
                        placement="bottom"
                        trigger="hover">
                    <div>
                        <el-checkbox :indeterminate="showColumn.isIndeterminate" v-model="showColumn.checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                        <div style="margin: 15px 0;"></div>
                        <el-checkbox-group v-model="showColumn.checkedColumn" @change="handleCheckedColumnChange" style="width: 130px">
                            <el-checkbox v-for="column in showColumn.allColumn" :label="column" :key="column">{{column}}</el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <el-button slot="reference">
                        <i class="fa fa-navicon"></i>
                    </el-button>
                </el-popover>
            </div>
        </div>
        <div class="box-body">
            <div class="form-inline" v-if="!hideQuery">
                <div class="form-group">
                    <label class="control-label">授权证书编号：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.authCertNo"></el-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">被授权主体：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.authorizedEntity"></el-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">经销商层级：</label>
                    <div class="control-inline">
                        <el-select multiple v-model="queryData.dealerLevel" placeholder="请选择经销商层级" class="width-260">
                            <el-option :label="dealerLevel[key]" :value="Number(key)" v-for="key in Object.keys(dealerLevel)"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">发布状态：</label>
                    <div class="control-inline">
                        <el-select multiple v-model="queryData.status" placeholder="请选择发布状态" class="width-260">
                            <el-option :label="status[key]" :value="Number(key)" v-for="key in Object.keys(status)"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权类型：</label>
                    <div class="control-inline">
                        <el-select multiple v-model="queryData.authType" placeholder="请选择授权类型" class="width-260">
                            <el-option :label="type[key]" :value="Number(key)" v-for="key in Object.keys(type)"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权起始日期：</label>
                    <div class="control-inline">
                        <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="queryData.authStartDate"
                                type="date"
                                placeholder="选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权结束日期：</label>
                    <div class="control-inline">
                        <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="queryData.authEndDate"
                                type="date"
                                placeholder="选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权平台：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.authPlatform"></el-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权区域：</label>
                    <div class="control-inline">
                        <el-cascader
                                filterable
                                :show-all-levels="false"
                                class="width-260"
                                :options="regionTree"
                                v-model="queryData.regionList"
                                @change="(list)=>this.editData.regionList = list"
                                :props="{ multiple: true, checkStrictly: true,value:'id',label:'name',emitPath:false }"
                                clearable></el-cascader>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">授权品线：</label>
                    <div class="control-inline">
                        <el-cascader
                                filterable
                                class="width-260"
                                :options="productLinesTree"
                                v-model="queryData.productLinesList"
                                @change="(list)=>this.editData.productLinesList = list"
                                :props="{ multiple: true, checkStrictly: true,value:'id',label:'name',emitPath:false }"
                                clearable></el-cascader>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-sm" @click="queryData.current=1;initSellerAuthorizationData()">${text('查询')}
                    </button>
                    <button type="reset" class="btn btn-default btn-sm" @click="queryData ={};initSellerAuthorizationData()">
                        ${text('重置')}
                    </button>
                </div>
            </div>

            <el-dialog title="授权卖家信息" :visible.sync="showEdit" width="80%" top="5vh">
                <el-form :model="editData" ref="ruleForm" label-width="130px" :inline="true">
                    <div class="box-body">
                        <div class="form-unit">${text('基本信息')}</div>
                        <el-form-item label="授权证书编号"
                                      prop="authCertNo"
                                      :rules="[{ required: true, message: '请填写授权证书编号', trigger: 'blur' }]">
                            <el-input v-model="editData.authCertNo" class="width-260" disabled=""></el-input>
                        </el-form-item>
                        <el-form-item label="被授权主体"
                                      prop="authorizedEntity"
                                      :rules="[{ required: true, message: '请填写被授权主体', trigger: 'blur' }]">
                            <el-input v-model="editData.authorizedEntity" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="发布状态"
                                      prop="status"
                                      :rules="[{ required: true, message: '请选择发布状态', trigger: 'blur' }]">
                            <el-select v-model="editData.status" placeholder="请选择发布状态" class="width-260" @change="checkAuthPlatform">
                                <el-option :label="status[key]" :value="Number(key)" v-for="key in Object.keys(status)"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="授权类型"
                                      prop="authType"
                                      :rules="[{ required: true, message: '请选择数据类型', trigger: 'blur' }]">
                            <el-select v-model="editData.authType" placeholder="请选择授权类型" class="width-260">
                                <el-option :label="type[key]" :value="Number(key)" v-for="key in Object.keys(type)"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="授权区域">
                            <el-cascader
                                    filterable
                                    :show-all-levels="false"
                                    class="width-260"
                                    :options="regionTree"
                                    v-model="editData.regionList"
                                    @change="(list)=>this.editData.regionList = list"
                                    :props="{ multiple: true, checkStrictly: true,value:'id',label:'name',emitPath:false }"
                                    clearable></el-cascader>
                        </el-form-item>
                        <el-form-item label="授权期限"
                                      prop="authDate"
                                      :rules="[{ required: true, message: '请选择授权期限', trigger: 'blur' }]">
                            <el-date-picker
                                    value-format="yyyy-MM-dd"
                                    class="width-260"
                                    @blur="$forceUpdate()"
                                    v-model="editData.authDate"
                                    type="daterange"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item label="授权产品"
                                      prop="productLinesList"
                                      :rules="[{ required: true, message: '请选择授权产品', trigger: 'blur' }]">
                            <el-cascader
                                    filterable
                                    class="width-260"
                                    :options="productLinesTree"
                                    v-model="editData.productLinesList"
                                    @change="(list)=>this.editData.productLinesList = list"
                                    :props="{ multiple: true, checkStrictly: true,value:'id',label:'name',emitPath:false }"
                                    clearable></el-cascader>
                        </el-form-item>
                    </div>

                    <div class="box-body">
                        <div class="form-unit">${text('授权平台信息')}</div>
                        <el-table :data="editData.authorizedPlatforms">
                            <el-table-column
                                    prop="authPlatform">
                                <template slot="header" slot-scope="scope">
                                    <span style="color: #F56C6C" v-if="editData.status === 1">* </span> 授权平台
                                </template>
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.authPlatform" @change="checkAuthPlatform"></el-input>
                                    <div style="color: #F56C6C" v-if="scope.row.error">{{scope.row.error}}</div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="storeName"
                                label="店铺名称">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.storeName"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="storeLink"
                                label="店铺链接">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.storeLink"></el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                    label="操作">
                                <template slot-scope="scope">
                                    <el-link icon="fa fa-trash-o" type="danger" title="删除"
                                             v-if="editData.authorizedPlatforms.length > 1"
                                             @click="editData.authorizedPlatforms.splice(scope.$index,1)"></el-link>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button type="primary" icon="el-icon-plus" style="margin-top: 10px" @click="editData.authorizedPlatforms.push({})">增行</el-button>
                    </div>

                    <div class="box-body">
                        <div class="form-unit">${text('联系信息')}</div>

                        <el-form-item label="联系电话"
                                      prop="contactPhone">
                            <el-input v-model="editData.contactPhone" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="联系邮箱"
                                      prop="contactEmail">
                            <el-input v-model="editData.contactEmail" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="联系地址"
                                      prop="contactAddress">
                            <el-input v-model="editData.contactAddress" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="业务员"
                                      prop="salesRep">
                            <el-input v-model="editData.salesRep" class="width-260" ></el-input>
                        </el-form-item>
                    </div>

                    <div class="box-body">
                        <div class="form-unit">${text('经销商信息')}</div>

                        <el-form-item label="经销商层级"
                                      prop="dealerLevel">
                            <el-select v-model="editData.dealerLevel" placeholder="请选择经销商层级" class="width-260">
                                <el-option :label="dealerLevel[key]" :value="Number(key)" v-for="key in Object.keys(dealerLevel)"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="经销商编码"
                                      prop="dealerCode">
                            <el-input v-model="editData.dealerCode" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="经销商名称"
                                      prop="dealerName">
                            <el-input v-model="editData.dealerName" class="width-260" ></el-input>
                        </el-form-item>
                        <el-form-item label="所属上级经销商"
                                      prop="parentDealer">
                            <el-input v-model="editData.parentDealer" class="width-260" ></el-input>
                        </el-form-item>
                    </div>

                    <div class="box-body">
                        <div class="form-unit">${text('违规信息')}</div>
                        <el-table :data="editData.sellerAuthViolations">
                            <el-table-column
                                    label="违规日期">
                                <template slot-scope="scope">
                                    <el-date-picker
                                            value-format="yyyy-MM-dd"
                                            v-model="scope.row.date"
                                            type="date"
                                            placeholder="选择日期时间">
                                    </el-date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    label="违规信息">
                                <template slot-scope="scope">
                                    <el-input
                                            type="textarea"
                                            :rows="2"
                                            placeholder="不超过200字符"
                                            v-model="scope.row.message">
                                    </el-input>
                                    <div style="color: #8c939d;display: flex;justify-content: flex-end">字符:{{ scope.row.message?.length?? 0 }}</div>
                                    <div style="color: #F56C6C" v-if="scope.row.error">{{scope.row.error}}</div>
                                </template>
                            </el-table-column>

                            <el-table-column
                                    label="操作">
                                <template slot-scope="scope">
                                    <el-link icon="fa fa-trash-o" type="danger" title="删除"
                                             @click="editData.sellerAuthViolations.splice(scope.$index,1)"></el-link>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button type="primary" icon="el-icon-plus" style="margin-top: 10px" @click="editData.sellerAuthViolations.push({})">增行</el-button>
                    </div>

                    <div class="box-body">
                        <div class="form-unit">${text('其他')}</div>
                        <el-form-item prop="otherMsg"
                                      id="otherMsgForm"
                                      style="width: 100%"
                                      :rules="[{ min: 0, max: 500, message: '不超过500字符', trigger: 'blur' }]">
                            <el-input
                                    type="textarea"
                                    :rows="4"
                                    placeholder="不超过500字符"
                                    v-model="editData.otherMsg">
                            </el-input>
                            <div style="color: #8c939d;display: flex;justify-content: flex-end">字符:{{ editData.otherMsg?.length?? 0 }}</div>
                        </el-form-item>
                    </div>

                    <div style="height: 20px;display: flex;justify-content: space-around">
                        <el-form-item>
                            <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
                            <el-button @click="showEdit = false;">取消</el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </el-dialog>

            <el-table
                    ref="table"
                    :span-method="objectSpanMethod"
                    :data="tableData?.records"
                    style="width: 100%;margin-bottom: 20px;"
                    @selection-change="handleSelectionChange"
                    @sort-change="sortChange"
                    border>
                <el-table-column
                        type="selection"
                        fixed
                        width="55">
                </el-table-column>
                <el-table-column
                        fixed
                        sortable="custom"
                        width="130"
                        prop="authCertNo"
                        label="授权证书编号">
                </el-table-column>
                <el-table-column
                        fixed
                        width="130"
                        sortable="custom"
                        v-if="showColumn.checkedColumn.indexOf('被授权主体') >= 0"
                        prop="authorizedEntity"
                        label="被授权主体">
                </el-table-column>
                <el-table-column
                        prop="dealerLevel"
                        width="130"
                        sortable="custom"
                        v-if="showColumn.checkedColumn.indexOf('经销商层级') >= 0"
                        label="经销商层级">
                    <template slot-scope="scope">
                        {{ dealerLevel[scope.row.dealerLevel] }}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="status"
                        width="130"
                        sortable="custom"
                        v-if="showColumn.checkedColumn.indexOf('发布状态') >= 0"
                        label="发布状态">
                    <template slot-scope="scope">
                        {{ status[scope.row.status] }}
                    </template>
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="130"
                        v-if="showColumn.checkedColumn.indexOf('授权区域') >= 0"
                        prop="region"
                        label="授权区域">
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="130"
                        v-if="showColumn.checkedColumn.indexOf('授权品线/型号') >= 0"
                        prop="productLineName"
                        label="授权品线/型号">
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="130"
                        prop="authType"
                        v-if="showColumn.checkedColumn.indexOf('授权类型') >= 0"
                        label="授权类型">
                    <template slot-scope="scope">
                        {{ type[scope.row.authType] }}
                    </template>
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="150"
                        v-if="showColumn.checkedColumn.indexOf('授权起始日期') >= 0"
                        prop="authStartDate"
                        label="授权起始日期">
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="150"
                        v-if="showColumn.checkedColumn.indexOf('授权结束日期') >= 0"
                        prop="authEndDate"
                        label="授权结束日期">
                </el-table-column>
                <el-table-column
                        sortable="custom"
                        width="150"
                        v-if="showColumn.checkedColumn.indexOf('授权平台') >= 0"
                        prop="authPlatform"
                        label="授权平台">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('店铺名称') >= 0"
                        prop="storeName"
                        width="150"
                        label="店铺名称">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('店铺链接') >= 0"
                        prop="storeLink"
                        width="150"
                        label="店铺链接">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('联系电话') >= 0"
                        prop="contactPhone"
                        width="150"
                        label="联系电话">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('联系邮箱') >= 0"
                        prop="contactEmail"
                        width="150"
                        label="联系邮箱">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('联系地址') >= 0"
                        prop="contactAddress"
                        width="150"
                        label="联系地址">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('业务员') >= 0"
                        prop="salesRep"
                        width="130"
                        label="业务员">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('经销商编码') >= 0"
                        prop="dealerCode"
                        width="150"
                        label="经销商编码">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('经销商名称') >= 0"
                        prop="dealerName"
                        width="150"
                        label="经销商名称">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('所属上级经销商') >= 0"
                        prop="parentDealer"
                        width="150"
                        label="所属上级经销商">
                </el-table-column>

                <el-table-column
                        fixed="right"
                        label="操作">
                    <template slot-scope="scope">
                        <el-link icon="fa fa-pencil" type="success" title="编辑"
                                 @click="handleEdit(scope.row)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-trash-o" type="danger" title="删除"
                                 @click="handleDelete(scope.row.id)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-copy" type="primary" title="复制"
                                 @click="handleCopy(scope.row)"></el-link>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    background
                    layout="total, sizes, prev, pager, next"
                    :current-page.sync="queryData.current"
                    @current-change="initSellerAuthorizationData"
                    @size-change="initSellerAuthorizationData"
                    :page-size.sync="queryData.size"
                    :total="tableData.total ?? 0">
            </el-pagination>
        </div>
    </div>
</div>
<script>
    var ctx = '${ctx}'
</script>
<% } %>
<script>
    js.loading()

    new Vue({
        el: '#sellerAuthorizationDataContent',
        data: {
            multipleSelection:[],
            showColumn:{
                checkAll:true,
                isIndeterminate: true,
                allColumn:['被授权主体','经销商层级','发布状态','授权区域','授权品线/型号','授权类型','授权起始日期','授权结束日期','授权平台','店铺名称','店铺链接','联系电话','联系邮箱','联系地址','业务员','经销商编码','经销商名称','所属上级经销商',],
                checkedColumn:['被授权主体','经销商层级','发布状态','授权区域','授权品线/型号','授权类型','授权起始日期','授权结束日期','授权平台','店铺名称'],
            },
            expandRowKeys:[],
            editData: {},
            showEdit: false,
            tableData: { },
            queryData: {
                current:1,
                size:10
            },
            hideQuery: true,
            dealerLevel: {
                0: '一级',
                1: '二级',
                2: '三级'
            },
            status: {
                0: '草稿',
                1: '正式',
            },
            type: {
                0: '普通',
                1: '独家',
                2: '线上卖家',
            },
            regionTree:[],
            productLinesTree:[],
            sortMapping:{
                order:{
                    ascending:'asc',
                    descending:'desc'
                },
                prop:{
                    region:'r.name',
                    productLineName:'pl.name',
                    authPlatform:'ap.auth_platform',
                    authCertNo:'auth_cert_no',
                    authorizedEntity:'authorized_entity',
                    dealerLevel:'dealer_level',
                    authType:'auth_type',
                    authStartDate:'auth_start_date',
                    authEndDate:'auth_end_date',
                }
            }
        },
        mounted() {
            js.closeLoading()
            $('#sellerAuthorizationDataContent').removeClass('hidden')

            this.initSellerAuthorizationData()
            this.initSelectDownData()
        },
        methods: {
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            async sortChange(sort){
                if(sort.order){
                    this.queryData.order = this.sortMapping.order[sort.order]
                    this.queryData.orderProp = this.sortMapping.prop[sort.prop] ?? sort.prop
                }else {
                    this.queryData.order = null
                    this.queryData.orderProp = null
                }
                await this.initSellerAuthorizationData()
            },
            handleCheckAllChange(val) {
                this.showColumn.checkedColumn = val ? this.showColumn.allColumn : [];
                this.showColumn.isIndeterminate = false;
            },
            handleCheckedColumnChange(value) {
                let checkedCount = value.length;
                this.showColumn.checkAll = checkedCount === this.showColumn.allColumn.length;
                this.showColumn.isIndeterminate = checkedCount > 0 && checkedCount < this.showColumn.allColumn.length;
            },
            checkAuthPlatform(){
                if(this.editData.status === 1){
                    for (let authorizedPlatform of this.editData.authorizedPlatforms) {
                        if(!authorizedPlatform.authPlatform){
                            this.$set(authorizedPlatform,'error','请填写授权平台')
                            return false
                        }else {
                            this.$set(authorizedPlatform,'error',null)
                        }
                    }
                }
                return true
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {

                        if(!this.checkAuthPlatform()){
                            this.$message.error('信息填写错误')
                            return
                        }

                        for (let sellerAuthViolation of this.editData.sellerAuthViolations) {
                            if (sellerAuthViolation.message.length > 200) {
                                this.$set(sellerAuthViolation,'error','内容不能超200字符')
                                this.$message.error('信息填写错误')
                                return
                            }else {
                                this.$set(sellerAuthViolation,'error',null)
                            }
                        }

                        axios.post(ctx + '/seller/authorization/saveOrUpdate', {...this.editData,authStartDate:this.editData.authDate[0],authEndDate:this.editData.authDate[1]}).then(res => {
                            if(res.data){
                                js.showErrorMessage(res.data)
                                return
                            }
                            this.initSellerAuthorizationData()
                            this.$message({
                                type: 'success',
                                message: '保存成功!'
                            });
                            this.showEdit = false
                        })
                    } else {
                        console.log('error submit!!');
                        this.$message.error('信息未填写完整')
                        return false;
                    }
                });
            },
            async initSellerAuthorizationData() {
                let response = await axios.post(ctx + '/seller/authorization/page', this.queryData)
                this.tableData = response.data
            },
            async exportData(){
                try {
                    let response = await axios.post(
                        ctx + '/seller/authorization/exportData',
                        this.queryData,
                        {
                            responseType: 'arraybuffer',  // 设置响应类型为二进制数据
                        }
                    )

                    // 获取文件名，默认值为 'file.xlsx'
                    const contentDisposition = response.headers['content-disposition'];
                    let fileName = 'file.xlsx'; // 默认文件名

                    if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                        // 正则匹配 filename* 字段
                        const matches = /filename\*=([^;]*)/.exec(contentDisposition);
                        if (matches != null && matches[1]) {
                            // 提取并解码文件名
                            const encodedFileName = matches[1].split("'")[2]; // 去除编码部分 'zh_cn'
                            fileName = decodeURIComponent(encodedFileName); // 解码文件名
                        }
                    }

                    // 将返回的 arraybuffer 数据转化为 Blob
                    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

                    // 创建一个临时的 <a> 元素来触发下载
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob); // 创建 Blob 对象的 URL
                    link.download = fileName; // 指定下载文件的名称
                    link.click(); // 模拟点击以启动下载

                    // 清理 URL 对象
                    URL.revokeObjectURL(link.href);
                } catch (error) {
                    console.error('下载文件失败', error);
                }
            },
            async handleDelete(id) {
                try {
                    await this.$confirm('此操作将永久删除授权信息, 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })

                    let res = await axios.post(ctx + '/seller/authorization/delete/' + id)
                    if(res.data){
                        js.showErrorMessage(res.data)
                        return
                    }
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    await this.initSellerAuthorizationData()
                } catch (e) {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                }
            },
            async initSelectDownData(){
                let response = await axios.get(ctx + '/v1/region/tree')
                this.regionTree = response.data
                response = await axios.post(ctx + '/product/lines/tree',{})
                this.productLinesTree = response.data
            },
            async changeStatusBatch(status){
                if(!this.multipleSelection || this.multipleSelection.length === 0){
                    this.$message.error('请选择需要修改的数据')
                    return
                }

                let res = await axios.post(ctx + '/seller/authorization/changeStatus',{
                    authCertNoList: this.multipleSelection.map(item=>item.authCertNo),
                    status
                })
                if(res.data){
                    js.showErrorMessage(res.data)
                    return
                }
                this.$message({
                    type: 'success',
                    message: '批量修改成功!'
                });

                await this.initSellerAuthorizationData()
            },
            async handleAdd(){
                await this.initSelectDownData()

                this.showEdit = true;
                this.editData = {authCertNo:'保存时自动生成，不可修改'};
                this.editData.authorizedPlatforms = [{}]
                this.editData.sellerAuthViolations = []
            },
            async handleEdit(obj) {
                await this.initSelectDownData()

                let response = await axios.get(ctx + '/seller/authorization/detail/' + obj.id)
                this.editData = response.data
                this.editData.productLinesList = this.editData.productLines?.map(item=>item.id) ?? []
                this.editData.regionList = this.editData.regions?.map(item=>item.code)
                this.editData.authDate = [this.editData.authStartDate,this.editData.authEndDate]

                this.showEdit = true
            },
            async handleCopy(obj) {
                await this.handleEdit(obj)
                this.editData.authCertNo = '保存时自动生成，不可修改'
                if(this.editData.authorizedPlatforms){
                    this.editData.authorizedPlatforms.forEach(item=>item.id = null)
                }
                if(this.editData.sellerAuthProductLinesMappings){
                    this.editData.sellerAuthProductLinesMappings.forEach(item=>item.id = null)
                }
                this.editData.id = null
            },
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (['授权平台','店铺名称','店铺链接'].indexOf(column.label) === -1) {
                    if(rowIndex!==0){
                       if(row.authCertNo === this.tableData.records[rowIndex - 1].authCertNo){
                          return {
                              rowspan: 0,
                              colspan: 0
                          }
                       }
                    }

                    let rowspan = 1;
                    let index = 1;
                    while (this.tableData.records.length > rowIndex + index){
                        let next = this.tableData.records[rowIndex + index]
                        if(row.authCertNo === next.authCertNo){
                            rowspan++
                        }else {
                            break
                        }
                        index++
                    }
                    return {
                        rowspan: rowspan,
                        colspan: 1
                    };

                }
            },
        }
    })
</script>
<style>
#otherMsgForm{
    .el-form-item__content{
        width: 100%;
    }
}
</style>