package com.hiwie.breeze.util;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;

import java.time.Clock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class CryptographyUtil {

    public static final ErrorMessage ERROR_INVALID_TOKEN = new ErrorMessage("CRYPTOGRAPHY", "TOKEN", "INVALID");

    private static final Clock CLOCK = Clock.systemUTC();
    private final static Pattern PATTERN = Pattern.compile("^([a-f0-9]{64})\\.([0-9]+:[0-9]+)\\.([a-zA-Z0-9-]+)$");

    private CryptographyUtil() {
    }

    public static String sign(String message, String key) {
        return new HmacUtils(HmacAlgorithms.HMAC_SHA_256, key).hmacHex(message);
    }

    public static AbstractEither<ErrorMessage, String> getMessageWithoutCheck(String token) {
        Matcher matcher = PATTERN.matcher(token);
        if (matcher.find()) {
            return Right.apply(matcher.group(3));
        } else {
            return Left.apply(ERROR_INVALID_TOKEN);
        }
    }

    public static AbstractEither<ErrorMessage, String> getMessage(String token, String key) {
        if (token == null || key == null) {
            return Left.apply(ERROR_INVALID_TOKEN);
        }
        Matcher matcher = PATTERN.matcher(token);
        if (matcher.find()) {
            String encrypted = matcher.group(1);
            String nonce = matcher.group(2);
            String message = matcher.group(3);
            if (StringUtil.safeEqual(sign(nonce + message, key), encrypted)) {
                return Right.apply(message);
            } else {
                return Left.apply(ERROR_INVALID_TOKEN);
            }
        } else {
            return Left.apply(ERROR_INVALID_TOKEN);
        }
    }

    public static String signAsToken(String message, String key) {
        String nonce = String.join(":", String.valueOf(CLOCK.millis()), String.valueOf(System.nanoTime()));
        return String.join(".", sign(nonce + message, key), nonce, message);
    }

}