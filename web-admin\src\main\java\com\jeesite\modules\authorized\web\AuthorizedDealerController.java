package com.jeesite.modules.authorized.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.authorized.entity.AuthorizedDealer;
import com.jeesite.modules.authorized.service.AuthorizedDealerService;

/**
 * 授权经销商管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-07
 */
@Controller
@RequestMapping(value = "${adminPath}/authorized/authorizedDealer")
public class AuthorizedDealerController extends BaseController {

    @Autowired
    private AuthorizedDealerService authorizedDealerService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public AuthorizedDealer get(String id, boolean isNewRecord) {
        return authorizedDealerService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("authorized:authorizedDealer:view")
    @RequestMapping(value = {"list", ""})
    public String list(AuthorizedDealer authorizedDealer, Model model) {
        model.addAttribute("authorizedDealer", authorizedDealer);
        return "modules/authorized/authorizedDealerList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("authorized:authorizedDealer:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<AuthorizedDealer> listData(AuthorizedDealer authorizedDealer, HttpServletRequest request, HttpServletResponse response) {
        authorizedDealer.setPage(new Page<>(request, response));
        Page<AuthorizedDealer> page = authorizedDealerService.findPage(authorizedDealer);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("authorized:authorizedDealer:view")
    @RequestMapping(value = "form")
    public String form(AuthorizedDealer authorizedDealer, Model model) {
        model.addAttribute("authorizedDealer", authorizedDealer);
        return "modules/authorized/authorizedDealerForm";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("authorized:authorizedDealer:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated AuthorizedDealer authorizedDealer, HttpServletRequest request) {
        authorizedDealerService.save(authorizedDealer);
        return renderResult(Global.TRUE, text("保存经销商成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("authorized:authorizedDealer:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(AuthorizedDealer authorizedDealer) {
        authorizedDealerService.delete(authorizedDealer);
        return renderResult(Global.TRUE, text("删除经销商成功！"));
    }

}