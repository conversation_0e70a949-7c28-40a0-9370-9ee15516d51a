package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.form.RecruitmentQueryForm;
import com.topdon.website.mappers.RecruitmentMapper;
import com.topdon.website.model.Recruitment;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class RecruitmentRepository extends MysqlJDBCSupport {

    @Inject
    protected RecruitmentRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<Recruitment> list(RecruitmentQueryForm queryForm, PaginationForm form) {
        @Language("SQL") String sql = "select r.id, name, people, position, create_at, content, category, type from recruitment r where type = :type";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("type", queryForm.getType().name());

        sql += queryForm.getCategory().map(category -> {
            params.addValue("category", category.name());
            return " AND r.category = :category";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getPosition().map(position -> {
            params.addValue("position", position);
            return " AND r.position = :position";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getName().map(name -> {
            params.addValue("name", like(name.toUpperCase()));
            return " AND r.name like upper(:name)";
        }).getOrElse(StringUtil.EMPTY);

        return paged(sql, RecruitmentMapper.DETAIL, params, form);
    }

    public AbstractOption<Recruitment> get(String id) {
        @Language("SQL") String sql = "select r.id, name, people, position, create_at, content, category, type from recruitment r where r.id = :id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        return option(sql, RecruitmentMapper.DETAIL, params);
    }

    public List<String> groupPosition() {
        @Language("SQL") String sql = "select r.position from recruitment r group by r.position";
        return list(sql, String.class, new MapSqlParameterSource());
    }

}
