package com.jeesite.modules.product.service;

import java.util.List;
import java.util.Map;

import com.beust.jcommander.internal.Maps;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mapper.JsonMapper;
import com.jeesite.modules.product.entity.ProductExtension;
import com.jeesite.modules.product.entity.ProductExtensionDetail;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductCompare;
import com.jeesite.modules.product.dao.ProductCompareDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 产品对比管理Service
 * <AUTHOR>
 * @version 2022-04-21
 */
@Service
@Transactional(readOnly=true)
public class ProductCompareService extends CrudService<ProductCompareDao, ProductCompare> {
	
	/**
	 * 获取单条数据
	 * @param productCompare
	 * @return
	 */
	@Override
	public ProductCompare get(ProductCompare productCompare) {
		ProductCompare entity = super.get(productCompare);
		if (entity != null) {
			Map<String, String> map = JsonMapper.fromJson(entity.getParam(), Map.class);
			List<ProductExtensionDetail> details = Lists.newArrayList();
			int index = 1;
			for (String key : map.keySet()) {
				ProductExtensionDetail detail = new ProductExtensionDetail();
				detail.setKey(key);
				detail.setId(String.valueOf(index));
				detail.setValue(map.get(key));
				index++;
				details.add(detail);
			}
			entity.setParams(details);
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param productCompare 查询条件
	 * @return
	 */
	@Override
	public Page<ProductCompare> findPage(ProductCompare productCompare) {
		return super.findPage(productCompare);
	}
	
	/**
	 * 查询列表数据
	 * @param productCompare
	 * @return
	 */
	@Override
	public List<ProductCompare> findList(ProductCompare productCompare) {
		return super.findList(productCompare);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param productCompare
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ProductCompare productCompare) {
		Map<String, String> param = Maps.newLinkedHashMap();
		for (ProductExtensionDetail detail : productCompare.getParams()) {
			if (DataEntity.STATUS_DELETE.equals(detail.getStatus())){
				continue;
			}
			param.put(detail.getKey(), detail.getValue());
		}
		productCompare.setParam(JsonMapper.toJson(param));
		super.save(productCompare);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(productCompare, productCompare.getId(), "productCompare_image");
	}
	
	/**
	 * 更新状态
	 * @param productCompare
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ProductCompare productCompare) {
		super.updateStatus(productCompare);
	}
	
	/**
	 * 删除数据
	 * @param productCompare
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ProductCompare productCompare) {
		super.delete(productCompare);
	}
	
}