package com.jeesite.modules.authorized.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.region.entity.Region;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 授权经销商管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-07
 */
@Table(name = "authorized_dealer", alias = "a", label = "经销商信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "name", attrName = "name", label = "经销商名称", queryType = QueryType.LIKE),
        @Column(name = "region_id", attrName = "region.id", label = "地区", isQuery = false),
        @Column(name = "phone", attrName = "phone", label = "电话", isQuery = false),
        @Column(name = "email", attrName = "email", label = "邮箱", isQuery = false),
        @Column(name = "cover", attrName = "cover", label = "封面", isQuery = false),
        @Column(name = "link", attrName = "link", label = "官网链接", isQuery = false),
        @Column(name = "address", attrName = "address", label = "地址", isQuery = false),
        @Column(name = "sort", attrName = "sort", label = "排序", isQuery = false),
        @Column(name = "create_at", attrName = "createDate", label = "创建时间", isUpdate = false, isQuery = false, isUpdateForce = true),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Region.class, alias = "r",
                on = "r.code = a.region_id", attrName = "region",
                columns = {@Column(includeEntity = Region.class)})
}, orderBy = "r.name,a.name"
)
public class AuthorizedDealer extends DataEntity<AuthorizedDealer> {

    private static final long serialVersionUID = 1L;
    private String name;        // 经销商名称
    private Region region;        // 地区
    private String phone;        // 电话
    private String email;        // 邮箱
    private String cover;        // 封面
    private String link;        // 官网链接
    private String address;        // 地址
    private Integer sort;        // 地址

    public AuthorizedDealer() {
        this(null);
    }

    public AuthorizedDealer(String id) {
        super(id);
    }

    @NotBlank(message = "经销商名称不能为空")
    @Size(min = 0, max = 40, message = "经销商名称长度不能超过 40 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Region getRegion() {
        return region;
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    @Size(min = 0, max = 100, message = "电话长度不能超过 100 个字符")
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Size(min = 0, max = 40, message = "邮箱长度不能超过 40 个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = 256, message = "封面长度不能超过 256 个字符")
    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    @Size(min = 0, max = 256, message = "官网链接长度不能超过 256 个字符")
    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Size(min = 0, max = 256, message = "地址长度不能超过 256 个字符")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}