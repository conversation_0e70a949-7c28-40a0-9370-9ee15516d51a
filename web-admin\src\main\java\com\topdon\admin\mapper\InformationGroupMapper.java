package com.topdon.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topdon.admin.dto.InformationGroupTreeDTO;
import com.topdon.admin.entity.InformationGroup;
import com.topdon.admin.vo.InformationGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InformationGroupMapper extends BaseMapper<InformationGroup> {
    List<InformationGroupVo> getList(@Param("param") InformationGroupTreeDTO informationGroupTreeDTO);
}