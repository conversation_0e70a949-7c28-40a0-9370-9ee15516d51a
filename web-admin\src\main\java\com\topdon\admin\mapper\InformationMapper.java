package com.topdon.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.jeesite.common.entity.Page;
import com.topdon.admin.entity.Information;
import com.topdon.admin.vo.InformationVo;
import org.apache.ibatis.annotations.Param;

public interface InformationMapper extends BaseMapper<Information> {

    PageDTO<InformationVo> getPage(PageDTO<InformationVo> page,
                                   @Param("information") InformationVo information,
                                   @Param("pageVo") Page<InformationVo> pageVo);
}