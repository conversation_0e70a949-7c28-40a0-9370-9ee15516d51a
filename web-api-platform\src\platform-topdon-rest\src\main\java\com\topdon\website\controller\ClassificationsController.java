package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.ClassificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("classifications")
public class ClassificationsController extends ControllerSupport {
    private final ClassificationService classificationService;

    @Autowired
    public ClassificationsController(ClassificationService classificationService) {
        this.classificationService = classificationService;
    }

    @GetMapping
    public AbstractRestResponse list(String parentId) {
        return AbstractRestResponse.apply(classificationService.list(parentId));
    }


    @GetMapping("/menus")
    public AbstractRestResponse menu() {
        return AbstractRestResponse.apply(classificationService.menu());
    }
}
