package com.jeesite.modules.footer.web;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.modules.sys.utils.UserUtils;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.footer.entity.FooterMenu;
import com.jeesite.modules.footer.service.FooterMenuService;

/**
 * 底部菜单Controller
 * <AUTHOR>
 * @version 2022-04-25
 */
@Controller
@RequestMapping(value = "${adminPath}/footer/footerMenu")
public class FooterMenuController extends BaseController {

	@Autowired
	private FooterMenuService footerMenuService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public FooterMenu get(String code, boolean isNewRecord) {
		return footerMenuService.get(code, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("footer:footerMenu:view")
	@RequestMapping(value = {"list", ""})
	public String list(FooterMenu footerMenu, Model model) {
		model.addAttribute("footerMenu", footerMenu);
		return "modules/footer/footerMenuList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("footer:footerMenu:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public List<FooterMenu> listData(FooterMenu footerMenu) {
		if (StringUtils.isBlank(footerMenu.getParentCode())) {
			footerMenu.setParentCode(FooterMenu.ROOT_CODE);
		}
		if (StringUtils.isNotBlank(footerMenu.getName())){
			footerMenu.setParentCode(null);
		}
		if (StringUtils.isNotBlank(footerMenu.getLink())){
			footerMenu.setParentCode(null);
		}
		if (StringUtils.isNotBlank(footerMenu.getRemarks())){
			footerMenu.setParentCode(null);
		}
		List<FooterMenu> list = footerMenuService.findList(footerMenu);
		return list;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("footer:footerMenu:view")
	@RequestMapping(value = "form")
	public String form(FooterMenu footerMenu, Model model) {
		// 创建并初始化下一个节点信息
		footerMenu = createNextNode(footerMenu);
		model.addAttribute("footerMenu", footerMenu);
		return "modules/footer/footerMenuForm";
	}
	
	/**
	 * 创建并初始化下一个节点信息，如：排序号、默认值
	 */
	@RequiresPermissions("footer:footerMenu:edit")
	@RequestMapping(value = "createNextNode")
	@ResponseBody
	public FooterMenu createNextNode(FooterMenu footerMenu) {
		if (StringUtils.isNotBlank(footerMenu.getParentCode())){
			footerMenu.setParent(footerMenuService.get(footerMenu.getParentCode()));
		}
		if (footerMenu.getIsNewRecord()) {
			FooterMenu where = new FooterMenu();
			where.setParentCode(footerMenu.getParentCode());
			FooterMenu last = footerMenuService.getLastByParentCode(where);
			// 获取到下级最后一个节点
			if (last != null){
				footerMenu.setTreeSort(last.getTreeSort() + 30);
				footerMenu.setCode(IdGen.nextCode(last.getCode()));
			}else if (footerMenu.getParent() != null){
				footerMenu.setCode(footerMenu.getParent().getCode() + "001");
			}
		}
		// 以下设置表单默认数据
		if (footerMenu.getTreeSort() == null){
			footerMenu.setTreeSort(FooterMenu.DEFAULT_TREE_SORT);
		}
		return footerMenu;
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("footer:footerMenu:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated FooterMenu footerMenu) {
		footerMenuService.save(footerMenu);
		return renderResult(Global.TRUE, text("保存菜单成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("footer:footerMenu:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(FooterMenu footerMenu) {
		footerMenuService.delete(footerMenu);
		return renderResult(Global.TRUE, text("删除菜单成功！"));
	}
	
	/**
	 * 获取树结构数据
	 * @param excludeCode 排除的Code
	 * @param isShowCode 是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
	 * @return
	 */
	@RequiresPermissions("footer:footerMenu:view")
	@RequestMapping(value = "treeData")
	@ResponseBody
	public List<Map<String, Object>> treeData(String excludeCode, String isShowCode) {
		List<Map<String, Object>> mapList = ListUtils.newArrayList();
		List<FooterMenu> list = footerMenuService.findList(new FooterMenu());
		for (int i=0; i<list.size(); i++){
			FooterMenu e = list.get(i);
			// 过滤被排除的编码（包括所有子级）
			if (StringUtils.isNotBlank(excludeCode)){
				if (e.getId().equals(excludeCode)){
					continue;
				}
				if (e.getParentCodes().contains("," + excludeCode + ",")){
					continue;
				}
			}
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("id", e.getId());
			map.put("pId", e.getParentCode());
			map.put("name", StringUtils.getTreeNodeName(isShowCode, e.getCode(), e.getName()));
			mapList.add(map);
		}
		return mapList;
	}

	/**
	 * 修复表结构相关数据
	 */
	@RequiresPermissions("footer:footerMenu:edit")
	@RequestMapping(value = "fixTreeData")
	@ResponseBody
	public String fixTreeData(FooterMenu footerMenu){
		if (!UserUtils.getUser().isAdmin()){
			return renderResult(Global.FALSE, "操作失败，只有管理员才能进行修复！");
		}
		footerMenuService.fixTreeData();
		return renderResult(Global.TRUE, "数据修复成功");
	}
	
}