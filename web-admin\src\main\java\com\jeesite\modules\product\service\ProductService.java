package com.jeesite.modules.product.service;

import com.beust.jcommander.internal.Lists;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.classification.entity.Classification;
import com.jeesite.modules.classification.service.ClassificationService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.product.dao.ProductClassificationDao;
import com.jeesite.modules.product.dao.ProductDao;
import com.jeesite.modules.product.entity.Product;
import com.jeesite.modules.product.entity.ProductClassification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品管理Service
 * <AUTHOR>
 * @version 2022-03-12
 */
@Service
@Transactional(readOnly=true)
public class ProductService extends CrudService<ProductDao, Product> {

	@Autowired
	private ProductClassificationDao productClassificationDao;
	@Autowired
	private ClassificationService classificationService;

	/**
	 * 获取单条数据
	 * @param product
	 * @return
	 */
	@Override
	public Product get(Product product) {
		return super.get(product);
	}

	/**
	 * 查询分页数据
	 * @param product 查询条件
	 * @param product.page 分页对象
	 * @return
	 */
	@Override
	public Page<Product> findPage(Product product) {
		Page<Product> page = super.findPage(product);
		Optional.ofNullable(page.getList()).filter(l -> !CollectionUtils.isEmpty(l)).ifPresent(l -> {
			List<String> id = l.stream().map(BaseEntity::getId).collect(Collectors.toList());
			Map<String, List<Classification>> map = classificationService.getByProductId(id).stream().collect(Collectors.groupingBy(Classification::getProductId));
			l.stream().forEach(o -> o.setClassifications(map.get(o.getId())));
		});
		return page;
	}

	/**
	 * 查询列表数据
	 * @param product
	 * @return
	 */
	@Override
	public List<Product> findList(Product product) {
		return super.findList(product);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param product
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(Product product) {
		super.save(product);
		ProductClassification productClassification = new ProductClassification();
		productClassification.setProductId(product.getId());
		productClassificationDao.deleteByEntity(productClassification);
		Optional.ofNullable(product.getProductClassifications()).filter(o-> !CollectionUtils.isEmpty(o)).ifPresent(l->{
			l.stream().forEach(o->o.setProductId(product.getId()));
			productClassificationDao.insertBatch(l);
		});

		List<ProductClassification> productClassifications = product.getProductClassifications();
		// 保存上传图片
		FileUploadUtils.saveFileUpload(product, product.getId(), "product_search_image");
		FileUploadUtils.saveFileUpload(product, product.getId(), "product_cover_image");
		FileUploadUtils.saveFileUpload(product, product.getId(), "product_mobile_cover_image");
	}

	/**
	 * 更新状态
	 * @param product
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(Product product) {
		super.updateStatus(product);
	}

	/**
	 * 删除数据
	 * @param product
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(Product product) {
		super.delete(product);
	}

	@Override
	public Product get(String id, boolean isNewRecord) {
		Product product = super.get(id, isNewRecord);
		product.setClassifications(classificationService.getByProductId(Lists.newArrayList(product.getId())));
		return product;
	}

	public Product getByName(String productName) {
		Product product = new Product();
		product.setName(productName);
		List<Product> list = findList(product);
		if(list.isEmpty()){
			return null;
		}
		return list.get(0);
	}


    public Product getByEqName(String name) {
        return super.dao.getByEqName(name);
    }
}