package com.topdon.website.services;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.PutObjectRequest;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.topdon.website.SCConstants;
import com.topdon.website.model.HWFile;
import com.topdon.website.repositories.FileRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;

@Named
public class FileService {
    private final String root;
    private final String domain;
    private final String aliyunRoot;
    private final String endpoint;
    private final FileRepository fileRepository;
    private final OSS ossClient;
    private final String bucketName;
    private final String proxyEndpoint;
    private final TransactionTemplate transactionTemplate;

    @Inject
    public FileService(
            @Value("${file.root.path}") String root,
            @Value("${server.domain}") String domain,
            @Value("${file.client.aliyun.root}") String aliyunRoot,
            @Value("${file.client.aliyun.endpoint}") String endpoint,
            @Value("${file.client.aliyun.proxyEndpoint}") String proxyEndpoint,
            FileRepository fileRepository, OSS ossClient, @Value("${file.client.aliyun.bucketName}") String bucketName, TransactionTemplate transactionTemplate) {
        this.root = root;
        this.domain = domain;
        this.aliyunRoot = aliyunRoot;
        this.endpoint = endpoint;
        this.fileRepository = fileRepository;
        this.ossClient = ossClient;
        this.bucketName = bucketName;
        this.proxyEndpoint = Optional.ofNullable(proxyEndpoint).filter(StringUtils::isNotEmpty).orElse(bucketName + "." + endpoint);
        this.transactionTemplate = transactionTemplate;
    }

    public AbstractEither<ErrorMessage, String> uploadFile(File file) {
        ossClient.putObject(new PutObjectRequest(bucketName, aliyunRoot + "/information/" + file.getName(), file));
        return Right.apply("https://" + proxyEndpoint + "/" + aliyunRoot + "information/" + file.getName());
    }

    public AbstractEither<ErrorMessage, Long> uploadFile(MultipartFile file) throws IOException {
        if (file.getBytes().length > 10 * 1024 * 1024) {
            return Left.apply(SCConstants.SIZE_LIMIT);
        }
        Path path = Paths.get(root);
        File dir = new File(root);
        if (!(dir.exists() && dir.isDirectory())) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                return Left.apply(SCConstants.UPLOAD_ERROR);
            }
        }
        Path fullPath = Paths.get(path.toString(), file.getOriginalFilename());

        try {
            Files.write(fullPath, file.getBytes());
            long id = fileRepository.create(file.getOriginalFilename(), Files.probeContentType(fullPath), file.getBytes().length);
            return Right.apply(id);
        } catch (IOException e) {
            return Left.apply(SCConstants.UPLOAD_ERROR);
        }
    }

    public AbstractEither<ErrorMessage, String> upload(String group, String name, byte[] bytes) {
        return transactionTemplate.execute(status -> {
            long id = fileRepository.create(name, bytes.length);
            Path path = Paths.get(root, group);
            File dir = new File(path.toUri());
            if (!(dir.exists() && dir.isDirectory())) {
                try {
                    Files.createDirectories(path);
                } catch (IOException e) {
                    status.setRollbackOnly();
                    return Left.apply(HWFile.Errors.UPLOAD_FAILED);
                }
            }
            Path fullPath = Paths.get(path.toString(), String.valueOf(id));
            try {
                Files.write(fullPath, bytes);
            } catch (IOException e) {
                status.setRollbackOnly();
                return Left.apply(HWFile.Errors.UPLOAD_FAILED);
            }
            return Right.apply(domain + "/file/" + group + "/" + id + "/" + name);
        });
    }

    public AbstractEither<ErrorMessage, HWFile> get(long id) {
        return fileRepository.get(id).toRight(HWFile.Errors.NOT_FOUND);
    }


    public AbstractEither<ErrorMessage, byte[]> read(String group, long id) {
        try {
            return Right.apply(Files.readAllBytes(Paths.get(Paths.get(root, group).toString(), String.valueOf(id))));
        } catch (IOException e) {
            return Left.apply(HWFile.Errors.NOT_FOUND);
        }
    }

}
