<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.SellerAuthViolationsMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.SellerAuthViolations">
    <!--@mbg.generated-->
    <!--@Table seller_auth_violations-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_auth_id" jdbcType="INTEGER" property="sellerAuthId" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="date" jdbcType="DATE" property="date" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, seller_auth_id, message, `date`
  </sql>
</mapper>