package com.topdon.website.service;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.Information;
import com.topdon.website.form.InformationQueryForm;

import java.util.List;

public interface InformationService extends IService<Information>{


    AbstractEither<ErrorMessage, List<Tree<Integer>>> getList(InformationQueryForm form);

    AbstractEither<ErrorMessage, List<Dict>> search(InformationQueryForm form);
}
