package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.service.ProductPropertyService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/product/property")
public class ProductPropertyController {

    @Resource
    private ProductPropertyService productPropertyService;

    @GetMapping("/getCompareProductList")
    public AbstractRestResponse getCompareProductList(String classificationCode, @RequestParam(required = false) boolean draft) {
        return AbstractRestResponse.apply(productPropertyService.getCompareProductList(classificationCode, draft));
    }

    @GetMapping("/getList")
    public AbstractRestResponse getList(String classificationCode,String productId, @RequestParam(required = false) boolean draft) {
        return AbstractRestResponse.apply(productPropertyService.getList(classificationCode,productId, draft));
    }

}
