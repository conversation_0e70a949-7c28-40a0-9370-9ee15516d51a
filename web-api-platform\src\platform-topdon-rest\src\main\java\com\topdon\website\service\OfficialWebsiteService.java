package com.topdon.website.service;

import cn.hutool.core.lang.tree.Tree;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.OfficialWebsite;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface OfficialWebsiteService extends IService<OfficialWebsite>{


    AbstractEither<ErrorMessage, List<Tree<String>>> getList();
}
