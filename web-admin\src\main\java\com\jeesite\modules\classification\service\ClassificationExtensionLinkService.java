package com.jeesite.modules.classification.service;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.classification.dao.ClassificationExtensionLinkDao;
import com.jeesite.modules.classification.entity.ClassificationExtensionLink;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 分类拓展链接Service
 * <AUTHOR>
 * @version 2025-07-31
 */
@Service
@Transactional(readOnly=true)
public class ClassificationExtensionLinkService extends CrudService<ClassificationExtensionLinkDao, ClassificationExtensionLink> {
	
	/**
	 * 获取单条数据
	 * @param classificationExtensionLink
	 * @return
	 */
	@Override
	public ClassificationExtensionLink get(ClassificationExtensionLink classificationExtensionLink) {
		return super.get(classificationExtensionLink);
	}
	
	/**
	 * 查询分页数据
	 * @param classificationExtensionLink 查询条件
	 * @return
	 */
	@Override
	public Page<ClassificationExtensionLink> findPage(ClassificationExtensionLink classificationExtensionLink) {
		return super.findPage(classificationExtensionLink);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param classificationExtensionLink
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ClassificationExtensionLink classificationExtensionLink) {
		super.save(classificationExtensionLink);
	}
	
	/**
	 * 更新状态
	 * @param classificationExtensionLink
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ClassificationExtensionLink classificationExtensionLink) {
		super.updateStatus(classificationExtensionLink);
	}
	
	/**
	 * 删除数据
	 * @param classificationExtensionLink
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ClassificationExtensionLink classificationExtensionLink) {
		super.delete(classificationExtensionLink);
	}
	
	/**
	 * 根据分类编码查询拓展链接列表
	 * @param classificationCode 分类编码
	 * @return 拓展链接列表
	 */
	public List<ClassificationExtensionLink> findByClassificationCode(String classificationCode) {
		ClassificationExtensionLink t = new ClassificationExtensionLink();
		t.setClassificationCode(classificationCode);
		return dao.findList(t);
	}
	
	/**
	 * 保存分类的拓展链接列表
	 * @param classificationCode 分类编码
	 * @param extensionLinks 拓展链接列表
	 */
	@Transactional(readOnly=false)
	public void saveExtensionLinks(String classificationCode, List<ClassificationExtensionLink> extensionLinks) {
		// 先删除原有的拓展链接
		dao.deleteByClassificationCode(classificationCode);
		
		// 保存新的拓展链接
		if (extensionLinks != null && !extensionLinks.isEmpty()) {
			for (ClassificationExtensionLink link : extensionLinks) {
				link.setClassificationCode(classificationCode);
				link.setIsNewRecord(true);
				this.save(link);
			}
		}
	}
	
}
