package com.topdon.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.admin.dto.EmailSubscribeDeleteDto;
import com.topdon.admin.dto.EmailSubscribeListDto;
import com.topdon.admin.dto.EmailSubscribePageDto;
import com.topdon.admin.entity.EmailSubscribe;
import com.topdon.admin.mapper.EmailSubscribeMapper;
import com.topdon.admin.service.EmailSubscribeServiceV1;
import com.topdon.admin.vo.EmailSubscribeExcelVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EmailSubscribeServiceV1Impl extends ServiceImpl<EmailSubscribeMapper, EmailSubscribe> implements EmailSubscribeServiceV1 {


    @Override
    public List<EmailSubscribeExcelVo> getExcelList(EmailSubscribeListDto emailSubscribe) {
        return this.baseMapper.getExcelList(emailSubscribe);
    }

    @Override
    public PageDTO<EmailSubscribe> getPage(EmailSubscribePageDto emailSubscribe) {
        return this.baseMapper.getPage(new PageDTO<>(emailSubscribe.getPageNo(), emailSubscribe.getPageSize()), emailSubscribe);
    }

    @Override
    public String batchDelete(EmailSubscribeDeleteDto deleteDto) {
        this.removeByIds(deleteDto.getIds());
        return "";
    }

    @Override
    public String saveWithDuplicateCheck(EmailSubscribe emailSubscribe) {
        EmailSubscribe exist = this.baseMapper.selectBySiteAndEmail(emailSubscribe.getSite(), emailSubscribe.getEmail());
        if (exist != null && (emailSubscribe.getId() == null || !exist.getId().equals(emailSubscribe.getId()))) {
            return "duplicate";
        }
        if (emailSubscribe.getId() == null) {
            this.save(emailSubscribe);
        } else {
            this.lambdaUpdate()
                    .set(EmailSubscribe::getSite, emailSubscribe.getSite())
                    .set(EmailSubscribe::getEmail, emailSubscribe.getEmail())
                    .set(EmailSubscribe::getFrom, emailSubscribe.getFrom())
                    .set(EmailSubscribe::getCopiedDiscountCode, emailSubscribe.getCopiedDiscountCode())
                    .set(EmailSubscribe::getCreateAt, emailSubscribe.getCreateAt())
                    .eq(EmailSubscribe::getId, emailSubscribe.getId())
                    .update();
        }
        return "success";
    }
}
