<% layout('/layouts/default.html', {title: 'banner管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(banner.isNewRecord ? '新增banner' : '编辑banner')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${banner}" action="${ctx}/banner/banner/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('图片')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadImage" bizKey="${banner.id}" bizType="banner_image"
								uploadType="image" class="" readonly="false" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="image"/>
								<#form:hidden path="image" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('移动端图片')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="mobileUploadImage" bizKey="${banner.id}" bizType="banner_mobile_image"
								uploadType="image" class="" readonly="false" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="mobileImage"/>
								<#form:hidden path="mobileImage" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('跳转链接')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="link" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('移动端跳转链接')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="mobileLink" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('显示标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="title" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="desc" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('按钮文案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="buttonTitle" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('排序')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sort" maxlength="50" class="form-control digits required"/>
							</div>
						</div>
					</div>

				</div>

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('banner:banner:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>