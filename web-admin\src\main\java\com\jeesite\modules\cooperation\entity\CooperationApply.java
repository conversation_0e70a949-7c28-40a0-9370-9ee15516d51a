package com.jeesite.modules.cooperation.entity;

import javax.validation.constraints.Size;
import java.util.Date;

import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.region.entity.Region;

/**
 * 申请管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-08
 */
@Table(name = "cooperation_apply", alias = "a", label = "申请信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "type", attrName = "type", label = "申请类型", isUpdate = false),
        @Column(name = "phone_number", attrName = "phoneNumber", label = "手机号码", isUpdate = false, isQuery = false),
        @Column(name = "email", attrName = "email", label = "电子邮箱", isUpdate = false, isQuery = false),
        @Column(name = "first_name", attrName = "firstName", label = "名", isUpdate = false, isQuery = false),
        @Column(name = "last_name", attrName = "lastName", label = "姓", isUpdate = false, isQuery = false),
        @Column(name = "company_name", attrName = "companyName", label = "公司名称", isUpdate = false, queryType = QueryType.LIKE),
        @Column(name = "company_size", attrName = "companySize", label = "公司大小", isUpdate = false, isQuery = false, isUpdateForce = true),
        @Column(name = "website_url", attrName = "websiteUrl", label = "官网链接", isUpdate = false, isQuery = false),
        @Column(name = "country", attrName = "region.id", label = "区域", isUpdate = false, isQuery = false),
        @Column(name = "address", attrName = "address", label = "公司地址", isUpdate = false, isQuery = false),
        @Column(name = "brand", attrName = "brand", label = "品牌", isUpdate = false, isQuery = false),
        @Column(name = "message", attrName = "message", label = "备注说明", isUpdate = false, isQuery = false),
        @Column(name = "create_at", attrName = "createDate", label = "申请时间", isUpdate = false, isQuery = false, isUpdateForce = true),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Region.class, alias = "r",
                on = "r.code = a.country", attrName = "region",
                columns = {@Column(includeEntity = Region.class)})
}, orderBy = "a.id DESC"
)
public class CooperationApply extends DataEntity<CooperationApply> {

    private static final long serialVersionUID = 1L;
    private String type;        // 申请类型
    private String phoneNumber;        // 手机号码
    private String email;        // 电子邮箱
    private String firstName;        // 名
    private String lastName;        // 姓
    private String companyName;        // 公司名称
    private Long companySize;        // 公司大小
    private String websiteUrl;        // 官网链接
    private Region region;        // 区域
    private String address;        // 公司地址
    private String brand;        // 品牌
    private String message;        // 备注说明
    private Date createDate;        // 申请时间

    @ExcelFields({
            @ExcelField(title = "公司名称", attrName = "companyName", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "申请类型", attrName = "type", align = ExcelField.Align.CENTER, sort = 30),
            @ExcelField(title = "手机号码", attrName = "phoneNumber", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "电子邮箱", attrName = "email", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "姓", attrName = "lastName", align = ExcelField.Align.LEFT, sort = 50),
            @ExcelField(title = "名", attrName = "firstName", align = ExcelField.Align.CENTER, sort = 60),
            @ExcelField(title = "公司大小", attrName = "companySize", align = ExcelField.Align.CENTER, sort = 70),
            @ExcelField(title = "官网链接", attrName = "websiteUrl", align = ExcelField.Align.CENTER, sort = 80),
            @ExcelField(title = "区域", attrName = "region.name", align = ExcelField.Align.CENTER, sort = 90),
            @ExcelField(title = "公司地址", attrName = "address", align = ExcelField.Align.CENTER, sort = 100),
            @ExcelField(title = "品牌", attrName = "brand", align = ExcelField.Align.CENTER, sort = 110),
            @ExcelField(title = "备注说明", attrName = "message", align = ExcelField.Align.CENTER, sort = 120),
            @ExcelField(title = "申请时间", attrName = "createDate", align = ExcelField.Align.CENTER, sort = 130, dataFormat = "yyyy-MM-dd HH:mm:ss"),
    })
    public CooperationApply() {
        this(null);
    }

    public CooperationApply(String id) {
        super(id);
    }

    @Size(min = 0, max = 10, message = "申请类型长度不能超过 10 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Size(min = 0, max = 30, message = "电子邮箱长度不能超过 30 个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = 20, message = "手机号码长度不能超过 0 个字符")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Size(min = 0, max = 20, message = "名长度不能超过 20 个字符")
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    @Size(min = 0, max = 20, message = "姓长度不能超过 20 个字符")
    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Size(min = 0, max = 40, message = "公司名称长度不能超过 40 个字符")
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanySize() {
        return companySize;
    }

    public void setCompanySize(Long companySize) {
        this.companySize = companySize;
    }

    @Size(min = 0, max = 255, message = "官网链接长度不能超过 255 个字符")
    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public Region getRegion() {
        return region;
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    @Size(min = 0, max = 256, message = "公司地址长度不能超过 256 个字符")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Size(min = 0, max = 40, message = "品牌长度不能超过 40 个字符")
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Size(min = 0, max = 1024, message = "备注说明长度不能超过 1024 个字符")
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

}