package com.topdon.website.controller;

import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.AuthorizedDealerQueryForm;
import com.topdon.website.services.AuthorizedDealerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/authorized_dealers")
public class AuthorizedDealersController extends ControllerSupport {

    private final AuthorizedDealerService authorizedDealerService;

    @Autowired
    public AuthorizedDealersController(AuthorizedDealerService authorizedDealerService) {
        this.authorizedDealerService = authorizedDealerService;
    }

    @GetMapping
    public AbstractRestResponse list(AuthorizedDealerQueryForm queryForm, PaginationForm form) {
        return AbstractRestResponse.apply(authorizedDealerService.list(queryForm, form));
    }

    @GetMapping("/count")
    public AbstractRestResponse count(AuthorizedDealerQueryForm queryForm) {
        return AbstractRestResponse.apply(authorizedDealerService.count(queryForm));
    }
}
