package com.topdon.website.services;

import com.google.common.collect.Lists;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.json.Json;
import com.topdon.website.SCConstants;
import com.topdon.website.model.Product;
import com.topdon.website.repositories.ProductRepository;
import com.topdon.website.vo.ProductVo;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
public class ShopifyProductService {
    final String CONTENT_TYPE_TEXT_JSON = "text/json";
    static int time = 1;
    private List<Map<String, Object>> products = Lists.newArrayList();

    @Resource
    private ProductRepository productRepository;


    private Map getProduct(String id) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://topdon-it.myshopify.com/admin/api/2022-04/products/" + id + ".json";
        HttpGet get = new HttpGet(url);

        get.setHeader("Content-Type", "application/json;charset=UTF-8");
        get.setHeader("X-Shopify-Access-Token", "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");
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(get);
            HttpEntity entity = response.getEntity();

            return (Map<String, Object>) Json.MAPPER.readValue(entity.getContent(), Map.class).get("product");

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private AbstractEither<ErrorMessage, Map> findProduct(String productName,String draft) {
        return products.stream().filter(product -> {
            String name = (String) product.get("title");
            System.out.println(name);
            return name.equalsIgnoreCase(productName);
        }).findFirst().<AbstractEither<ErrorMessage, Map>>map(product -> {

            time = 1;
            Map pro = getProduct(String.valueOf(product.get("id")));
            if (pro != null) {
                List<ProductVo> byName = productRepository.getByName(productName,"false");
                if(!byName.isEmpty()){
                    ProductVo productVo = byName.get(0);
                    pro.put("compareClassificationCode", productVo.getCompareClassificationCode());
                    pro.put("compare", productVo.isCompare());
                    pro.put("productId", productVo.getId());
                }
                return Right.apply(pro);
            }
            return Left.apply(SCConstants.API_ERROR);
        }).orElseGet(() -> {
            if (time == 1) {
                time--;
                overview(productName,draft);
            }
            time = 1;
            return Left.apply(Product.Errors.NOT_FOUNT);
        });
    }

    public AbstractEither<ErrorMessage, Map> overview(String productName,String draft) {
        if (products.size() > 0 && time == 1) {
            return findProduct(productName,draft);
        }
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://topdon-it.myshopify.com/admin/api/2022-04/products.json?limit=250&status=active";
        HttpGet get = new HttpGet(url);
        get.setHeader("Content-Trype", "application/json;charset=UTF-8");
        get.setHeader("X-Shopify-Access-Token", "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");
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(get);
            HttpEntity entity = response.getEntity();
            products = (List<Map<String, Object>>) Json.MAPPER.readValue(entity.getContent(), Map.class).get("products");

            return findProduct(productName,draft);

        } catch (IOException e) {
            e.printStackTrace();
            return Left.apply(SCConstants.API_ERROR);
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
