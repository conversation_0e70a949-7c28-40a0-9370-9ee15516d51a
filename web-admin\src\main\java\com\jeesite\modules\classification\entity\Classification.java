package com.jeesite.modules.classification.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 分类管理Entity
 * <AUTHOR>
 * @version 2022-03-02
 */
@Table(name="classification", alias="a", label="分类信息", columns={
		@Column(name="code", attrName="code", label="编码", isPK=true),
		@Column(name="name", attrName="name", label="名称", queryType=QueryType.LIKE, isTreeName=true),
		@Column(name="contrast_group_name", attrName="contrastGroupName", label="对比分组名称", queryType=QueryType.LIKE,isQuery=true),
		@Column(name="banner_media", attrName="bannerMedia", label="banner图", isQuery=false),
		@Column(name="menu_media", attrName="menuMedia", label="导航图", isQuery=false),
		@Column(name="pro_media", attrName="proMedia", label="二级品类图", isQuery=false),
		@Column(name="nav_desc", attrName="navDesc", label="导航文案", isQuery=false),
		@Column(name="description", attrName="description", label="描述", isQuery=false),
		@Column(includeEntity=TreeEntity.class),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="remarks", attrName="remarks", label="备注", isQuery=false),
	}, orderBy="a.tree_sorts, a.code"
)
public class Classification extends TreeEntity<Classification> {
	
	private static final long serialVersionUID = 1L;
	private String code;		// 编码
	private String name;		// 名称
	private String contrastGroupName;		// 对比分组名称
	private String bannerMedia;		// banner图
	private String menuMedia;		// 导航图
	private String proMedia;
	private String navDesc;
	private String description;		// 描述

	private String productId;
	
	public Classification() {
		this(null);
	}

	public Classification(String id){
		super(id);
	}
	
	@Override
	public Classification getParent() {
		return parent;
	}

	@Override
	public void setParent(Classification parent) {
		this.parent = parent;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@NotBlank(message="名称不能为空")
	@Size(min=0, max=256, message="名称长度不能超过 256 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContrastGroupName() {
		return contrastGroupName;
	}

	public void setContrastGroupName(String contrastGroupName) {
		this.contrastGroupName = contrastGroupName;
	}

	@Size(min=0, max=1024, message="banner图长度不能超过 1024 个字符")
	public String getBannerMedia() {
		return bannerMedia;
	}

	public void setBannerMedia(String bannerMedia) {
		this.bannerMedia = bannerMedia;
	}
	
	@Size(min=0, max=1024, message="导航图长度不能超过 1024 个字符")
	public String getMenuMedia() {
		return menuMedia;
	}

	public void setMenuMedia(String menuMedia) {
		this.menuMedia = menuMedia;
	}

	@Size(min=0, max=1024, message="二级品类图长度不能超过 1024 个字符")
	public String getProMedia() {
		return proMedia;
	}

	public void setProMedia(String proMedia) {
		this.proMedia = proMedia;
	}

	public String getNavDesc() {
		return navDesc;
	}

	public void setNavDesc(String navDesc) {
		this.navDesc = navDesc;
	}

	@Size(min=0, max=1025, message="描述长度不能超过 1025 个字符")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}
}