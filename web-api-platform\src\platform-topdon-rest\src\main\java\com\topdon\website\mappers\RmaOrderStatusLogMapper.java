package com.topdon.website.mappers;

import cn.hutool.core.date.DateUtil;
import com.topdon.website.model.RmaOrderStatusLog;
import org.springframework.jdbc.core.RowMapper;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/6 18:26
 */
public class RmaOrderStatusLogMapper {

    public static final RowMapper<RmaOrderStatusLog> DETAIL = (rs, index) -> {
        RmaOrderStatusLog detail = new RmaOrderStatusLog();
        detail.setTicketNumber(rs.getString("ticket_number"));
        detail.setStatus(rs.getString("status"));
        detail.setStatusModifiedTime(DateUtil.parseDateTime(rs.getString("status_modified_time")));
        return detail;
    };
}
