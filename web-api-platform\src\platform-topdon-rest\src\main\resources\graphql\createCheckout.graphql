mutation checkoutCreate($input:CheckoutCreateInput!){
    checkoutCreate(input: $input) {
        checkout {
            id
            paymentDue{
                amount
            }
            requiresShipping
            shippingDiscountAllocations{
                allocatedAmount {
                    amount
                }
            }
            lineItemsSubtotalPrice{
                amount
            }
            subtotalPrice{
                amount
            }
            totalDuties{
                amount
            }
            totalPrice{
                amount
            }
            totalTax{
                amount
            }
            webUrl
        }
        checkoutUserErrors{
            field
            message
        }
    }
}
