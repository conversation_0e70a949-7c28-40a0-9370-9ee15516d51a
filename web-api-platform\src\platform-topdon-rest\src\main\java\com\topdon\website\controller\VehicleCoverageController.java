package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.VehicleCoverageQueryForm;
import com.topdon.website.services.TopdonInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("vehicle_coverage")
public class VehicleCoverageController extends ControllerSupport {

    private final TopdonInterfaceService topdonInterfaceService;

    @Autowired
    public VehicleCoverageController(TopdonInterfaceService topdonInterfaceService) {
        this.topdonInterfaceService = topdonInterfaceService;
    }

    @GetMapping("condition")
    public AbstractRestResponse condition(VehicleCoverageQueryForm queryForm) {
        return AbstractRestResponse.apply(topdonInterfaceService.condition(queryForm));
    }

    @GetMapping("list")
    public AbstractRestResponse list(VehicleCoverageQueryForm queryForm) {
        return AbstractRestResponse.apply(topdonInterfaceService.list(queryForm));
    }
}
