package com.topdon.website.configs;

import com.google.common.collect.Maps;
import org.mountcloud.graphql.GraphqlClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.Map;

@SpringBootConfiguration
public class GraphqlClientConfig {

    @Bean
    public GraphqlClient graphqlClient(@Value("${shopify.graphql.url}") String url, @Value("${shopify.admin.graphql.token}") String token) {
        GraphqlClient graphqlClient = GraphqlClient.buildGraphqlClient(url);
        Map<String, String> httpHeaders = Maps.newHashMap();
        httpHeaders.put("X-Shopify-Access-Token", token);
        graphqlClient.setHttpHeaders(httpHeaders);
        return graphqlClient;

    }

    @Bean(name = "graphqlFrontClient")
    public GraphqlClient graphqlFrontClient(@Value("${shopify.storefront.graphql.url}") String url, @Value("${shopify.storefront.graphql.token}") String token) {
        GraphqlClient graphqlClient = GraphqlClient.buildGraphqlClient(url);
        Map<String, String> httpHeaders = Maps.newHashMap();
        httpHeaders.put("X-Shopify-Storefront-Access-Token", token);
        graphqlClient.setHttpHeaders(httpHeaders);
        return graphqlClient;

    }
}
