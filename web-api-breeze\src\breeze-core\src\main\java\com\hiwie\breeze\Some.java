package com.hiwie.breeze;

import com.hiwie.breeze.util.AssertUtil;

/**
 * <AUTHOR>
 */
public class Some<T> extends AbstractOption<T> {

    private final T value;

    private Some(T value) {
        this.value = value;
    }

    public static <T> Some<T> apply(T value) {
        AssertUtil.notNull(value,"Some() value can NOT be null");
        return new Some<>(value);
    }

    @Override
    public T get() {
        return this.value;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

}
