package com.jeesite.modules.product.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductRelation;
import com.jeesite.modules.product.dao.ProductRelationDao;

/**
 * 产品推荐管理Service
 * <AUTHOR>
 * @version 2022-04-26
 */
@Service
@Transactional(readOnly=true)
public class ProductRelationService extends CrudService<ProductRelationDao, ProductRelation> {
	
	/**
	 * 获取单条数据
	 * @param productRelation
	 * @return
	 */
	@Override
	public ProductRelation get(ProductRelation productRelation) {
		return super.get(productRelation);
	}
	
	/**
	 * 查询分页数据
	 * @param productRelation 查询条件
	 * @param productRelation.page 分页对象
	 * @return
	 */
	@Override
	public Page<ProductRelation> findPage(ProductRelation productRelation) {
		return super.findPage(productRelation);
	}
	
	/**
	 * 查询列表数据
	 * @param productRelation
	 * @return
	 */
	@Override
	public List<ProductRelation> findList(ProductRelation productRelation) {
		return super.findList(productRelation);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param productRelation
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ProductRelation productRelation) {
		super.save(productRelation);
	}
	
	/**
	 * 更新状态
	 * @param productRelation
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ProductRelation productRelation) {
		super.updateStatus(productRelation);
	}
	
	/**
	 * 删除数据
	 * @param productRelation
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ProductRelation productRelation) {
		super.delete(productRelation);
	}
	
}