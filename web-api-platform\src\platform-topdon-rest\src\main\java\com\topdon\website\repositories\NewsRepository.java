package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.form.NewsQueryForm;
import com.topdon.website.mappers.NewsMapper;
import com.topdon.website.model.News;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class NewsRepository extends MysqlJDBCSupport {

    @Inject
    protected NewsRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long count(NewsQueryForm queryForm) {
        @Language("SQL") String sql = "select count(0) from news n where 1=1 and news_status = 10 and publish_at <= now()";
        MapSqlParameterSource param = new MapSqlParameterSource();
        sql += queryForm.getCategory().map(category -> {
            param.addValue("category", category.name());
            return " AND n.category = :category";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getName().map(name -> {
            param.addValue("name", like(name.toUpperCase()));
            return " AND upper(n.name) like :name";
        }).getOrElse(StringUtil.EMPTY);

        return object(sql, Long.class, param);
    }

    public List<News> list(NewsQueryForm queryForm, PaginationForm form) {
        @Language("SQL") String sql = "select n.id, name, cover, introduction, create_at, create_by, update_at, update_by, category, is_main,publish_at from news n where 1=1 and news_status = 10 and publish_at <= now()";
        MapSqlParameterSource param = new MapSqlParameterSource();
        sql += queryForm.getCategory().map(category -> {
            param.addValue("category", category.name());
            return " AND n.category = :category";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getName().map(name -> {
            param.addValue("name", like(name.toUpperCase()));
            return " AND upper(n.name) like :name";
        }).getOrElse(StringUtil.EMPTY);

        sql += " order by n.is_top desc,n.publish_at desc";

        return paged(sql, NewsMapper.LIST, param, form);
    }

    public AbstractOption<News> get(String id) {
        @Language("SQL") String sql = "select n.id, name,content, cover, introduction, create_at, create_by, update_at, update_by, category, is_main,publish_at from news n where n.id = :id";
        MapSqlParameterSource param = new MapSqlParameterSource("id", id);
        return option(sql, NewsMapper.DETAIL, param);
    }

}
