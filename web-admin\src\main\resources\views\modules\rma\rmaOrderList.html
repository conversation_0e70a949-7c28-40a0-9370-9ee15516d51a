<% layout('/layouts/default.html', {title: 'RMA工单管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('RMA工单管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('rma:rmaOrder:edit')){ %>
					<a href="${ctx}/rma/rmaOrder/form" class="btn btn-default btnTool" title="${text('新增RMA工单')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${rmaOrder}" action="${ctx}/rma/rmaOrder/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('工单号')}：</label>
					<div class="control-inline">
						<#form:input path="ticketNumber" maxlength="60" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用户ID')}：</label>
					<div class="control-inline">
						<#form:input path="userId" maxlength="60" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('sn')}：</label>
					<div class="control-inline">
						<#form:input path="sn" maxlength="60" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('产品型号')}：</label>
					<div class="control-inline">
						<#form:input path="productName" maxlength="128" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('问题类型')}：</label>
					<div class="control-inline">
						<#form:input path="issueType" maxlength="30" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('产品分类')}：</label>
					<div class="control-inline">
						<#form:input path="classificationId" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('问题描述')}：</label>
					<div class="control-inline">
						<#form:input path="description" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('Order Number')}：</label>
					<div class="control-inline">
						<#form:input path="orderNo" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('国家/地区')}：</label>
					<div class="control-inline">
						<#form:input path="country" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('省/州')}：</label>
					<div class="control-inline">
						<#form:input path="stateRegion" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('城市')}：</label>
					<div class="control-inline">
						<#form:input path="city" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('邮编')}：</label>
					<div class="control-inline">
						<#form:input path="postalCode" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('姓名')}：</label>
					<div class="control-inline">
						<#form:input path="sellerName" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('购买平台')}：</label>
					<div class="control-inline">
						<#form:input path="platform" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工单状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="ticketStatus" dictType="" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('退回跟踪号')}：</label>
					<div class="control-inline">
						<#form:input path="tuiHuiGenZongHao" maxlength="36" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('发货跟踪号')}：</label>
					<div class="control-inline">
						<#form:input path="huoWuLiuDanHao" maxlength="36" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工单创建时间')}：</label>
					<div class="control-inline">
						<#form:input path="createTime" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('经销商')}：</label>
					<div class="control-inline">
						<#form:input path="jingXiaoShangMingCheng" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('邮箱')}：</label>
					<div class="control-inline">
						<#form:input path="email" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("工单号")}', name:'ticketNumber', index:'a.ticket_number', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/rma/rmaOrder/form?id='+row.id+'" class="btnList" data-title="${text("查看RMA工单")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("用户ID")}', name:'userId', index:'a.user_id', width:150, align:"left"},
		{header:'${text("sn")}', name:'sn', index:'a.sn', width:150, align:"left"},
		{header:'${text("产品型号")}', name:'productName', index:'a.product_name', width:150, align:"left"},
		{header:'${text("问题类型")}', name:'issueType', index:'a.issue_type', width:150, align:"left"},
		{header:'${text("产品分类")}', name:'classificationId', index:'a.classification_id', width:150, align:"left"},
		{header:'${text("问题描述")}', name:'description', index:'a.description', width:150, align:"left"},
		{header:'${text("Order Number")}', name:'orderNo', index:'a.order_no', width:150, align:"left"},
		{header:'${text("国家/地区")}', name:'country', index:'a.country', width:150, align:"left"},
		{header:'${text("省/州")}', name:'stateRegion', index:'a.state_region', width:150, align:"left"},
		{header:'${text("城市")}', name:'city', index:'a.city', width:150, align:"left"},
		{header:'${text("地址")}', name:'address1', index:'a.address1', width:150, align:"left"},
		{header:'${text("邮编")}', name:'postalCode', index:'a.postal_code', width:150, align:"left"},
		{header:'${text("姓名")}', name:'sellerName', index:'a.seller_name', width:150, align:"left"},
		{header:'${text("来源")}', name:'channel', index:'a.channel', width:150, align:"left"},
		{header:'${text("购买平台")}', name:'platform', index:'a.platform', width:150, align:"left"},
		{header:'${text("工单状态")}', name:'ticketStatus', index:'a.ticket_status', width:150, align:"center"},
		{header:'${text("退回跟踪号")}', name:'tuiHuiGenZongHao', index:'a.tui_hui_gen_zong_hao', width:150, align:"left"},
		{header:'${text("发货跟踪号")}', name:'huoWuLiuDanHao', index:'a.huo_wu_liu_dan_hao', width:150, align:"left"},
		{header:'${text("处理方案")}', name:'chuLiFangAn', index:'a.chu_li_fang_an', width:150, align:"left"},
		{header:'${text("工单创建时间")}', name:'createTime', index:'a.create_time', width:150, align:"center"},
		{header:'${text("经销商")}', name:'jingXiaoShangMingCheng', index:'a.jing_xiao_shang_ming_cheng', width:150, align:"left"},
		{header:'${text("邮箱")}', name:'email', index:'a.email', width:150, align:"left"},
		{header:'${text("处理方案")}', name:'solution', index:'a.solution', width:150, align:"left"}
		// {header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
		// 	var actions = [];
		// 	<% if(hasPermi('rma:rmaOrder:edit')){ %>
		// 		actions.push('<a href="${ctx}/rma/rmaOrder/form?id='+row.id+'" class="btnList" title="${text("编辑RMA工单")}"><i class="fa fa-pencil"></i></a>&nbsp;');
		// 		actions.push('<a href="${ctx}/rma/rmaOrder/delete?id='+row.id+'" class="btnList" title="${text("删除RMA工单")}" data-confirm="${text("确认要删除该RMA工单吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
		// 	<% } %>
		// 	return actions.join('');
		// }}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>