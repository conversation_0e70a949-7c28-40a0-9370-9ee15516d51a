package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductOverview;
import com.jeesite.modules.product.service.ProductOverviewService;

/**
 * 产品详情管理Controller
 * <AUTHOR>
 * @version 2022-04-21
 */
@Controller
@RequestMapping(value = "${adminPath}/product/productOverview")
public class ProductOverviewController extends BaseController {

	@Autowired
	private ProductOverviewService productOverviewService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ProductOverview get(String id, boolean isNewRecord) {
		return productOverviewService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("product:productOverview:view")
	@RequestMapping(value = {"list", ""})
	public String list(ProductOverview productOverview, Model model) {
		model.addAttribute("productOverview", productOverview);
		return "modules/product/productOverviewList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("product:productOverview:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ProductOverview> listData(ProductOverview productOverview, HttpServletRequest request, HttpServletResponse response) {
		productOverview.setPage(new Page<>(request, response));
		Page<ProductOverview> page = productOverviewService.findPage(productOverview);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("product:productOverview:view")
	@RequestMapping(value = "form")
	public String form(ProductOverview productOverview, Model model) {
		model.addAttribute("productOverview", productOverview);
		return "modules/product/productOverviewForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("product:productOverview:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ProductOverview productOverview) {
		productOverviewService.save(productOverview);
		return renderResult(Global.TRUE, text("保存产品详情成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("product:productOverview:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ProductOverview productOverview) {
		productOverviewService.delete(productOverview);
		return renderResult(Global.TRUE, text("删除产品详情成功！"));
	}
	
}