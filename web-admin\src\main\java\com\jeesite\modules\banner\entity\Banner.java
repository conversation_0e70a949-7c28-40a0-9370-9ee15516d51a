package com.jeesite.modules.banner.entity;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * banner管理Entity
 * <AUTHOR>
 * @version 2022-04-23
 */
@Table(name="banner", alias="a", label="banner信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="image", attrName="image", label="图片", isQuery=false),
		@Column(name="mobile_image", attrName="mobileImage", label="移动端图片", isQuery=false),
		@Column(name="name", attrName="name", label="名称", queryType=QueryType.LIKE),
		@Column(name="link", attrName="link", label="跳转链接", isQuery=false),
		@Column(name="mobile_link", attrName="mobileLink", label="移动端跳转链接", isQuery=false),
		@Column(name="title", attrName="title", label="显示标题", isQuery=false),
		@Column(name="desc", attrName="desc", label="描述", isQuery=false),
		@Column(name="button_title", attrName="buttonTitle", label="按钮文案", isQuery=false),
		@Column(name="sort", attrName="sort", label="排序", isQuery=false),
		@Column(name="create_at", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
	}, orderBy="a.sort,a.id DESC"
)
public class Banner extends DataEntity<Banner> {

	private static final long serialVersionUID = 1L;
	private String image;		// 图片
	private String mobileImage;		// 图片
	private String name;		// 名称
	private String link;		// 跳转链接
	private String mobileLink;		// 跳转链接
	private String title;		// 显示标题
	private String desc;		// 描述
	private String buttonTitle;		// 按钮文案
	private Integer sort;		// 排序
	private Date createDate;		// 创建时间

	public Banner() {
		this(null);
	}

	public Banner(String id){
		super(id);
	}

	@Size(min=0, max=256, message="图片长度不能超过 256 个字符")
	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	@Size(min=0, max=256, message="图片长度不能超过 256 个字符")
	public String getMobileImage() {
		return mobileImage;
	}

	public void setMobileImage(String mobileImage) {
		this.mobileImage = mobileImage;
	}

	@Size(min=0, max=50, message="名称长度不能超过 50 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Size(min=0, max=256, message="跳转链接长度不能超过 256 个字符")
	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	@Size(min=0, max=256, message="显示标题长度不能超过 256 个字符")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Size(min=0, max=256, message="描述长度不能超过 256 个字符")
	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	@Size(min=0, max=50, message="按钮文案长度不能超过 50 个字符")
	public String getButtonTitle() {
		return buttonTitle;
	}

	public void setButtonTitle(String buttonTitle) {
		this.buttonTitle = buttonTitle;
	}

	@Min(value = 0, message = "排序不能为负数")
	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getMobileLink() {
		return mobileLink;
	}

	public void setMobileLink(String mobileLink) {
		this.mobileLink = mobileLink;
	}
}