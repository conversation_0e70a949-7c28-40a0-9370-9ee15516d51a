package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 16:51
 */
@Data
public class RmaOrder {
    private static final String ENTITY_NAME = "RMAORDER";

    private Integer id;
    private String ticketNumber;
    private String sn;
    private Integer productId;
    private String issueType;
    private String classificationId;
    private String productName;
    private String description;
    private String orderNo;
    private String country;
    private String stateRegion;
    private String city;
    private String address1;
    private String postalCode;
    private String sellerName;
    private String platform;
    private String channel;
    private String ticketStatus;
    private Date statusModifiedTime;
    private String tuiHuiHenZongHao;
    private String huoWuLiuDanHao;
    private String chuLiFangAn;
    private String jingXiaoShangMingCheng;
    private String email;
    private String phone;
    private String solution;

    private List<RmaOrderStatusLog> rmaOrderStatusLogList;

    private LogisticsInfoApiVo.LogisticsInfoApiVoData logisticsInfoApiVo;
    private String productMobileCover;

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUND");
    }
}
