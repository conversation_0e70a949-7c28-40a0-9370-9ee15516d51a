package com.topdon.website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topdon.website.entity.ClassificationCompare;
import com.topdon.website.vo.ProductCompareInfoVo;
import org.apache.ibatis.annotations.Param;

public interface ClassificationCompareMapper extends BaseMapper<ClassificationCompare> {
    ProductCompareInfoVo getCompareInfo(@Param("productName") String productName,@Param("draft") boolean draft);
}