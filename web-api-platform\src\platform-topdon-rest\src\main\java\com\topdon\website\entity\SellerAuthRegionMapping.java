package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 授权卖家授权区域映射
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "seller_auth_region_mapping")
public class SellerAuthRegionMapping {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "seller_auth_id")
    private Integer sellerAuthId;

    @TableField(value = "region_code")
    private String regionCode;
}