<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.MenuClickLogMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.MenuClickLog">
    <!--@mbg.generated-->
    <!--@Table menu_click_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="ip_location" jdbcType="VARCHAR" property="ipLocation" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="click_time" jdbcType="TIMESTAMP" property="clickTime" />
    <result column="user_agent" jdbcType="LONGVARCHAR" property="userAgent" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ip_address, ip_location, menu_name, product_name, click_time, user_agent
  </sql>

  <sql id="getListSql">
    select *
    from menu_click_log a
    <where>
      <if test="param.startDate != null and param.startDate != ''">
        and a.click_time &gt;= #{param.startDate}
      </if>
      <if test="param.endDate != null and param.endDate != ''">
        and a.click_time &lt;= #{param.endDate}
      </if>
    </where>
  </sql>

  <select id="getPage" resultMap="BaseResultMap">
    <include refid="getListSql">
    </include>
    <if test="param.orderBy != null and param.orderBy != ''">
      order by ${param.orderBy}
    </if>
  </select>

  <resultMap id="BaseExcelResultMap" type="com.topdon.admin.vo.MenuClickLogExcelVo" extends="BaseResultMap">
  </resultMap>

  <select id="getExcelList" resultMap="BaseExcelResultMap">
    <include refid="getListSql">
    </include>
  </select>
</mapper>