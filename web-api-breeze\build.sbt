import BuildSettings._
import Dependencies._
import sbt.Keys._

organization in ThisBuild := "com.hiwie"

name := "breeze"

version in ThisBuild := "1.0.0-SNAPSHOT"

scalaVersion := "2.12.6"

lazy val core = JavaProject("breeze-core").settings(
  libraryDependencies ++= Seq(
    logback,
    lomlok,
    guava,
    javax_servlet,
    commons_lang3,
    commons_text,
    commons_codec
  )
)

lazy val json = JavaProject("breeze-json").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    jackson_databind,
    jackson_datatype_jdk8,
    jackson_datatype_jsr310,
    jackson_core,
    spring_context,
    spring_context_support
  )
)

lazy val bpm = JavaProject("breeze-bpm").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    activiti_engine,
    activiti_modoler,
    activiti_spring,
    javax_servlet,
    spring_web
  )
)

lazy val import_module = JavaProject("breeze-import").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    poi,
    poi_ooxml,
    poi_ooxml_schemas,
    javax_servlet,
    spring_web
  )
)


lazy val rest = JavaProject("breeze-rest").dependsOn(core, json).settings(
  libraryDependencies ++= Seq(
    javax_servlet,
    javax_el,
    javax_el_impl,
    spring_web,
    spring_webmvc,
    spring_core,
    spring_beans,
    spring_context,
    spring_context_support
  )
)

lazy val unit_test: Project = JavaProject("breeze-unit-test").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    junit_jupiter,
    junit_vintage,
    junit_platform,
    spring_test,
    spring_web,
    spring_context,
    spring_beans,
    jsonpath,
    javax_servlet
  )
)

lazy val service = JavaProject("breeze-service").dependsOn(core, validator).settings(
  libraryDependencies ++= Seq(
    javax_inject,
    spring_aspects,
    spring_webflux,
    netty_http,
    spring_orm,
    aspectj_rt,
    aspectj_tools,
    aspectj_weaver,
    commons_beanutils,
  )
)

lazy val validator = JavaProject("breeze-validator").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    javax_validation,
    hibernate_validator
  )
)

lazy val application = JavaProject("breeze-application").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    spring_context,
    spring_context_support
  )
)

lazy val context = JavaProject("breeze-context").dependsOn(validator).settings(
  libraryDependencies ++= Seq(
    spring_context
  )
)

lazy val scheduler = JavaProject("breeze-scheduler").dependsOn(core).settings(
  libraryDependencies ++= Seq(
    spring_context_support,
    quartz
  )
)

lazy val repository = JavaProject("breeze-repository").dependsOn(core, validator).settings(
  libraryDependencies ++= Seq(
  )
)

lazy val jdbc = JavaProject("breeze-jdbc").dependsOn(core, repository).settings(
  libraryDependencies ++= Seq(
    spring_jdbc,
    javax_inject,
    intellij_annotation
  )
)

lazy val cache = JavaProject("breeze-cache").dependsOn(core, json, unit_test % Test).settings(
  libraryDependencies ++= Seq(
    jedis
  )
)

lazy val security = JavaProject("breeze-security").dependsOn(core, json, unit_test % Test).settings(
  libraryDependencies ++= Seq(
  )
)

lazy val security_core = JavaProject("breeze-security-core").dependsOn(core, json, unit_test % Test).settings(
  libraryDependencies ++= Seq(
  )
)

lazy val sbt_plugin = SbtPluginProject("sbt-breeze").settings(
  sbtPlugin := true
)