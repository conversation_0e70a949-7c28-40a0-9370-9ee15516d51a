package com.zoho.desk.ticket;

import cn.hutool.core.util.StrUtil;
import com.zoho.desk.agent.Agent;
import com.zoho.desk.contact.Contact;
import com.zoho.desk.department.Department;
import com.zoho.desk.exception.ZDeskException;
import com.zoho.desk.init.CommonUtil;
import com.zoho.desk.logger.ZDLogger;
import com.zoho.desk.product.Product;
import com.zoho.desk.team.Team;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/16 15:29
 */
public class Ticket {

    private Map<String, Object> data = new HashMap();
    private Set<String> update = new HashSet();
    private List<String> booleanValues = Arrays.asList("isTrashed", "isResponseOverdue", "isSpam", "isRead", "isDeleted", "isFollowing");

    public Ticket() {
    }

    public Ticket(JSONObject data) throws JSONException {
        this.setFieldValues(data);
    }

    public Date getModifiedTime() throws ZDeskException {
        try {
            String modifiedTimeValue = (String) this.data.get("modifiedTime");
            return modifiedTimeValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(modifiedTimeValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public String getSubCategory() {
        return (String) this.data.get("subCategory");
    }

    public void setSubCategory(String subCategory) {
        this.data.put("subCategory", subCategory);
        this.update.add("subCategory");
    }

    public Ticket.StatusType getStatusType() {
        String responseValue = (String) this.data.get("statusType");
        Ticket.StatusType value = null;
        if (responseValue == null) {
            return value;
        } else {
            byte var4 = -1;
            switch (responseValue.hashCode()) {
                case 2464362:
                    if (responseValue.equals("Open")) {
                        var4 = 2;
                    }
                    break;
                case 279361120:
                    if (responseValue.equals("On Hold")) {
                        var4 = 1;
                    }
                    break;
                case 2021313932:
                    if (responseValue.equals("Closed")) {
                        var4 = 0;
                    }
            }

            switch (var4) {
                case 0:
                    value = Ticket.StatusType.CLOSED;
                    break;
                case 1:
                    value = Ticket.StatusType.ONHOLD;
                    break;
                case 2:
                    value = Ticket.StatusType.OPEN;
            }

            return value;
        }
    }

    public String getSubject() {
        return (String) this.data.get("subject");
    }

    public void setSubject(String subject) {
        this.data.put("subject", subject);
        this.update.add("subject");
    }

    public String getDepartmentId() {
        return (String) this.data.get("departmentId");
    }

    public void setDepartmentId(String departmentId) {
        this.data.put("departmentId", departmentId);
        this.update.add("departmentId");
    }

    public Date getDueDate() throws ZDeskException {
        try {
            String dueDateValue = (String) this.data.get("dueDate");
            return dueDateValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(dueDateValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public void setDueDate(Date dueDate) {
        String time = null;
        if (dueDate != null) {
            time = (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).format(dueDate);
        }

        this.data.put("dueDate", time);
        this.update.add("dueDate");
    }

    public String getChannel() {
        return (String) this.data.get("channel");
    }

    public void setChannel(String channel) {
        this.data.put("channel", channel);
        this.update.add("channel");
    }

    public Date getOnholdTime() throws ZDeskException {
        try {
            String onholdTimeValue = (String) this.data.get("onholdTime");
            return onholdTimeValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(onholdTimeValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public Source getSource() {
        try {
            Map<String, Object> value = (Map) this.data.get("source");
            return value == null ? null : new Source(new JSONObject(value));
        } catch (JSONException var2) {
            ZDLogger.logError(var2);
            return null;
        }
    }

    public void setSource(Source source) {
        this.data.put("source", source);
        this.update.add("source");
    }

    public List<SharedDepartments> getSharedDepartments() {
        try {
            List<SharedDepartments> result = new ArrayList();
            List<Map<String, Object>> value = (List) this.data.get("sharedDepartments");
            if (value == null) {
                return null;
            } else {
                Iterator var3 = value.iterator();

                while (var3.hasNext()) {
                    Map<String, Object> subProps = (Map) var3.next();
                    result.add(new SharedDepartments(new JSONObject(subProps)));
                }

                return result;
            }
        } catch (JSONException var5) {
            ZDLogger.logError(var5);
            return null;
        }
    }

    public void setSharedDepartments(List<SharedDepartments> sharedDepartments) {
        this.data.put("sharedDepartments", sharedDepartments);
        this.update.add("sharedDepartments");
    }

    public String getResolution() {
        return (String) this.data.get("resolution");
    }

    public void setResolution(String resolution) {
        this.data.put("resolution", resolution);
        this.update.add("resolution");
    }

    public Date getClosedTime() throws ZDeskException {
        try {
            String closedTimeValue = (String) this.data.get("closedTime");
            return closedTimeValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(closedTimeValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public String getSharedCount() {
        return (String) this.data.get("sharedCount");
    }

    public String getApprovalCount() {
        return (String) this.data.get("approvalCount");
    }

    public Boolean getIsTrashed() {
        return (Boolean) this.data.get("isTrashed");
    }

    public Date getCreatedTime() throws ZDeskException {
        try {
            String createdTimeValue = (String) this.data.get("createdTime");
            return createdTimeValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(createdTimeValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public String getId() {
        return (String) this.data.get("id");
    }

    public Boolean getIsResponseOverdue() {
        return (Boolean) this.data.get("isResponseOverdue");
    }

    public Date getCustomerResponseTime() throws ZDeskException {
        try {
            String customerResponseTimeValue = (String) this.data.get("customerResponseTime");
            return customerResponseTimeValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(customerResponseTimeValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public String getProductId() {
        return (String) this.data.get("productId");
    }

    public void setProductId(String productId) {
        this.data.put("productId", productId);
        this.update.add("productId");
    }

    public String getContactId() {
        return (String) this.data.get("contactId");
    }

    public void setContactId(String contactId) {
        this.data.put("contactId", contactId);
        this.update.add("contactId");
    }

    public String getThreadCount() {
        return (String) this.data.get("threadCount");
    }

    public List<String> getSecondaryContacts() {
        return (List) this.data.get("secondaryContacts");
    }

    public void setSecondaryContacts(List<String> secondaryContacts) {
        this.data.put("secondaryContacts", secondaryContacts);
        this.update.add("secondaryContacts");
    }

    public String getPriority() {
        return (String) this.data.get("priority");
    }

    public void setPriority(String priority) {
        this.data.put("priority", priority);
        this.update.add("priority");
    }

    public String getClassification() {
        return (String) this.data.get("classification");
    }

    public void setClassification(String classification) {
        this.data.put("classification", classification);
        this.update.add("classification");
    }

    public String getCommentCount() {
        return (String) this.data.get("commentCount");
    }

    public String getAccountId() {
        return (String) this.data.get("accountId");
    }

    public String getTaskCount() {
        return (String) this.data.get("taskCount");
    }

    public String getPhone() {
        return (String) this.data.get("phone");
    }

    public void setPhone(String phone) {
        this.data.put("phone", phone);
        this.update.add("phone");
    }

    public String getWebUrl() {
        return (String) this.data.get("webUrl");
    }

    public void setWebUrl(String webUrl) {
        this.data.put("webUrl", webUrl);
        this.update.add("webUrl");
    }

    public Boolean getIsSpam() {
        return (Boolean) this.data.get("isSpam");
    }

    public String getStatus() {
        return (String) this.data.get("status");
    }

    public void setStatus(String status) {
        this.data.put("status", status);
        this.update.add("status");
    }

    public String getTicketNumber() {
        return (String) this.data.get("ticketNumber");
    }

    public String getSentiment() {
        return (String) this.data.get("sentiment");
    }

    public Boolean getIsRead() {
        return (Boolean) this.data.get("isRead");
    }

    public String getDescription() {
        return (String) this.data.get("description");
    }

    public void setDescription(String description) {
        this.data.put("description", description);
        this.update.add("description");
    }

    public String getTimeEntryCount() {
        return (String) this.data.get("timeEntryCount");
    }

    public ChannelRelatedInfo getChannelRelatedInfo() {
        try {
            Map<String, Object> value = (Map) this.data.get("channelRelatedInfo");
            return value == null ? null : new ChannelRelatedInfo(new JSONObject(value));
        } catch (JSONException var2) {
            ZDLogger.logError(var2);
            return null;
        }
    }

    public Date getResponseDueDate() throws ZDeskException {
        try {
            String responseDueDateValue = (String) this.data.get("responseDueDate");
            return responseDueDateValue != null ? (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).parse(responseDueDateValue) : null;
        } catch (ParseException var2) {
            throw new ZDeskException(var2);
        }
    }

    public void setResponseDueDate(Date responseDueDate) {
        String time = null;
        if (responseDueDate != null) {
            time = (new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")).format(responseDueDate);
        }

        this.data.put("responseDueDate", time);
        this.update.add("responseDueDate");
    }

    public Boolean getIsDeleted() {
        return (Boolean) this.data.get("isDeleted");
    }

    public String getModifiedBy() {
        return (String) this.data.get("modifiedBy");
    }

    public String getFollowerCount() {
        return (String) this.data.get("followerCount");
    }

    public String getEmail() {
        return (String) this.data.get("email");
    }

    public void setEmail(String email) {
        this.data.put("email", email);
        this.update.add("email");
    }

    public String getChannelCode() {
        return (String) this.data.get("channelCode");
    }

    public void setChannelCode(String channelCode) {
        this.data.put("channelCode", channelCode);
        this.update.add("channelCode");
    }

    public Long getCfFieldAsLong(String label) {
        try {
            return this.getCf(label) != null ? Long.valueOf(this.getCf(label)) : null;
        } catch (NumberFormatException var3) {
            ZDLogger.logError(label + " value is not a Number");
            return null;
        }
    }

    public Double getCfFieldAsDouble(String label) {
        try {
            return this.getCf(label) != null ? Double.valueOf(this.getCf(label)) : null;
        } catch (NumberFormatException var3) {
            ZDLogger.logError(label + " value is not a Number");
            return null;
        }
    }

    public Integer getCfFieldAsInteger(String label) {
        try {
            return this.getCf(label) != null ? Integer.valueOf(this.getCf(label)) : null;
        } catch (NumberFormatException var3) {
            ZDLogger.logError(label + " value is not a Number");
            return null;
        }
    }

    public Boolean getCfFieldAsBoolean(String label) {
        return this.getCf(label) != null ? Boolean.valueOf(this.getCf(label)) : null;
    }

    public List<String> getCfFieldAsList(String label) {
        return this.getCf(label) != null ? Arrays.asList(this.getCf(label).split(";")) : null;
    }

    public String getCf(String label) {
        if (this.data.containsKey("cf")) {
            Map<String, Object> cf = (Map) this.data.get("cf");
            if (cf.containsKey(label)) {
                if (cf.get(label) != null) {
                    return (String) cf.get(label);
                }

                return null;
            }
        }

        return null;
    }

    public void setCf(String key, String value) {
        Map<String, Object> cf = (Map) this.data.get("cf");
        if (cf == null) {
            cf = new HashMap();
        }

        ((Map) cf).put(key, value);
        this.data.put("cf", cf);
        this.update.add("cf");
    }

    public Boolean getIsFollowing() {
        return (Boolean) this.data.get("isFollowing");
    }

    public LastThread getLastThread() {
        try {
            Map<String, Object> value = (Map) this.data.get("lastThread");
            return value == null ? null : new LastThread(new JSONObject(value));
        } catch (JSONException var2) {
            ZDLogger.logError(var2);
            return null;
        }
    }

    public String getAssigneeId() {
        return (String) this.data.get("assigneeId");
    }

    public void setAssigneeId(String assigneeId) {
        this.data.put("assigneeId", assigneeId);
        this.update.add("assigneeId");
    }

    public List<String> getUploads() {
        return (List) this.data.get("uploads");
    }

    public void setUploads(List<String> uploads) {
        this.data.put("uploads", uploads);
        this.update.add("uploads");
    }

    public String getCreatedBy() {
        return (String) this.data.get("createdBy");
    }

    public String getTeamId() {
        return (String) this.data.get("teamId");
    }

    public void setTeamId(String teamId) {
        this.data.put("teamId", teamId);
        this.update.add("teamId");
    }

    public String getContractId() {
        return (String) this.data.get("contractId");
    }

    public String getTagCount() {
        return (String) this.data.get("tagCount");
    }

    public String getAttachmentCount() {
        return (String) this.data.get("attachmentCount");
    }

    public String getCategory() {
        return (String) this.data.get("category");
    }

    public void setCategory(String category) {
        this.data.put("category", category);
        this.update.add("category");
    }

    public Product getProduct() {
        return (Product) this.data.get("product");
    }

    public Contact getContact() {
        return (Contact) this.data.get("contact");
    }

    public Team getTeam() {
        return (Team) this.data.get("team");
    }

    public Agent getAssignee() {
        return (Agent) this.data.get("assignee");
    }

    public Department getDepartment() {
        return (Department) this.data.get("department");
    }

    private void setFieldValues(JSONObject ticketData) throws JSONException {
        Iterator itr = this.handleIncludeQueryParamsFields(ticketData).keys();

        while (itr.hasNext()) {
            String key = (String) itr.next();
            Object value = ticketData.get(key);
            if (ticketData.isNull(key)) {
                this.data.put(key, (Object) null);
            } else if (this.booleanValues.contains(key)) {
                this.data.put(key, ticketData.getBoolean(key));
            } else {
                if (value instanceof JSONObject) {
                    value = CommonUtil.toConvertJSONToMap((JSONObject) value);
                } else if (value instanceof JSONArray) {
                    value = CommonUtil.toConvertJSONToList((JSONArray) value);
                } else {
                    value = String.valueOf(value);
                }

                this.data.put(key, value);
            }
        }

    }

    private JSONObject handleIncludeQueryParamsFields(JSONObject ticketData) throws JSONException {
        if (ticketData.has("product")) {
            if (ticketData.isNull("product")) {
                this.data.put("product", (Object) null);
                ticketData.remove("product");
            } else {
                Product product = new Product(ticketData.getJSONObject("product"));
                this.data.put("product", product);
                ticketData.remove("product");
            }
        }

        if (ticketData.has("contact")) {
            if (ticketData.isNull("contact")) {
                this.data.put("contact", (Object) null);
                ticketData.remove("contact");
            } else {
                Contact contact = new Contact(ticketData.getJSONObject("contact"));
                this.data.put("contact", contact);
                this.update.add("contact");
                ticketData.remove("contact");
            }
        }

        if (ticketData.has("team")) {
            if (ticketData.isNull("team")) {
                this.data.put("team", (Object) null);
                ticketData.remove("team");
            } else {
                Team team = new Team(ticketData.getJSONObject("team"));
                this.data.put("team", team);
                ticketData.remove("team");
            }
        }

        if (ticketData.has("assignee")) {
            if (ticketData.isNull("assignee")) {
                this.data.put("assignee", (Object) null);
                ticketData.remove("assignee");
            } else {
                Agent assignee = new Agent(ticketData.getJSONObject("assignee"));
                this.data.put("assignee", assignee);
                ticketData.remove("assignee");
            }
        }

        if (ticketData.has("department")) {
            if (ticketData.isNull("department")) {
                this.data.put("department", (Object) null);
                ticketData.remove("department");
            } else {
                Department department = new Department(ticketData.getJSONObject("department"));
                this.data.put("department", department);
                ticketData.remove("department");
            }
        }

        return ticketData;
    }

    JSONObject getPayloadForUpdate() {
        Map<String, Object> payloadData = new HashMap();
        Iterator itr = this.update.iterator();

        while (itr.hasNext()) {
            String key = (String) itr.next();
            if (this.data.get(key) instanceof String) {
                Optional.ofNullable((String) this.data.get(key)).filter(StrUtil::isNotEmpty).ifPresent(o -> payloadData.put(key, o));
            } else {
                payloadData.put(key, this.data.get(key));
            }
        }

        return new JSONObject(payloadData);
    }

    @Override
    public String toString() {
        return this.data.toString();
    }

    public static enum StatusType {
        CLOSED("Closed"),
        ONHOLD("On Hold"),
        OPEN("Open");

        private String value;

        private StatusType(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }
}
