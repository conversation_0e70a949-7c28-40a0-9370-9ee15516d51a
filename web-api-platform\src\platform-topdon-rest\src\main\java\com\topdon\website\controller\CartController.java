package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.form.CartForm;
import com.topdon.website.form.SkuUpdateForm;
import com.topdon.website.services.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/cart")
public class CartController extends ControllerSupport {

    private final CartService cartService;

    @Autowired
    public CartController(CartService cartService) {
        this.cartService = cartService;
    }

    @PostMapping
    public AbstractRestResponse create(@HWSession Session session, CartForm cartForm) {
        return AbstractRestResponse.apply(cartService.create(session.getUserId(), cartForm));
    }

    @PostMapping("/update_sku")
    public AbstractRestResponse updateSku(@HWSession Session session, long id, SkuUpdateForm form) {
        return AbstractRestResponse.apply(cartService.updateSku(id, form));
    }

    @PostMapping("/delete")
    public AbstractRestResponse delete(@HWSession Session session, long id) {
        return AbstractRestResponse.apply(cartService.delete(session.getUserId(), id));
    }

    @PostMapping("/delete/multi")
    public AbstractRestResponse delete(@HWSession Session session, @RequestBody List<Long> cartIds) {
        return AbstractRestResponse.apply(cartService.delete(session.getUserId(), cartIds));
    }

}
