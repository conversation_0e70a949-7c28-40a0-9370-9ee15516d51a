package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.form.RmaOrderForm;
import com.topdon.website.model.api.ZohoRmaOrder;
import com.topdon.website.services.RmaOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3 17:30
 */
@RestController
@RequestMapping("/rmaOrder")
public class RmaOrderController extends ControllerSupport {

    @Autowired
    private RmaOrderService rmaOrderService;

    @PostMapping("/add")
    public AbstractRestResponse add(@HWSession Session session, RmaOrderForm rmaOrderForm) {
        return AbstractRestResponse.apply(rmaOrderService.add(session.getUserId(), rmaOrderForm));
    }

    @PostMapping("/uploadFile")
    public AbstractRestResponse uploadFile(@HWSession Session session, HttpServletRequest request, MultipartFile file) {
        return AbstractRestResponse.apply(rmaOrderService.uploadFile(request, file));
    }

    @PostMapping("/getDepartments")
    public AbstractRestResponse getDepartments(@HWSession Session session) {
        return AbstractRestResponse.apply(rmaOrderService.getDepartments());
    }

    @PostMapping("/getInfo")
    public AbstractRestResponse getInfo(ZohoRmaOrder zohoRmaOrder) {
        return AbstractRestResponse.apply(rmaOrderService.getInfo(zohoRmaOrder));
    }
}
