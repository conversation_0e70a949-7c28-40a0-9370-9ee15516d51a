<% layout('/layouts/default.html', {title: 'RMA工单管理', libs: ['validate','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(rmaOrder.isNewRecord ? '新增RMA工单' : '编辑RMA工单')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${rmaOrder}" action="${ctx}/rma/rmaOrder/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工单号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ticketNumber" maxlength="60" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('用户ID')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userId" maxlength="60" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('sn')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sn" maxlength="60" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('产品型号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="productName" maxlength="128" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('问题类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="issueType" maxlength="30" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('产品分类')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="classificationId" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('问题描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="description" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('Order Number')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="orderNo" maxlength="64" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('国家/地区')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="country" maxlength="20" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('省/州')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="stateRegion" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('城市')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="city" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="address1" maxlength="200" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('邮编')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="postalCode" maxlength="20" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('姓名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sellerName" maxlength="64" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工单状态')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ticketStatus" maxlength="20" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('来源')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="channel" maxlength="20" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('购买平台')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="platform" maxlength="20" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('退回跟踪号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="tuiHuiGenZongHao" maxlength="36" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('发货跟踪号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="huoWuLiuDanHao" maxlength="36" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('处理方案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="chuLiFangAn" maxlength="36" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工单创建时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="createTime" readonly="true" maxlength="20" class="form-control laydate"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('经销商')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="jingXiaoShangMingCheng" maxlength="200" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('邮箱')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="email" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('处理方案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="solution" rows="4" maxlength="1025" class="form-control"/>
							</div>
						</div>
					</div>
				</div>

				<h4 class="form-unit">${text('状态变更日志')}</h4>
				<div class="ml10 mr10 table-form">
					<table id="paramDataGrid"></table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('rma:rmaOrder:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	//初始化测试数据子表DataGrid对象
	$("#paramDataGrid").dataGrid({
		data: ${toJson(rmaOrder.params)},
		datatype: "local", // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度
		// 设置数据表格列
		columnModel: [
			{header:'${text("状态")}', name:'status', width:100},
			{header:'${text("变更时间")}', name:'statusModifiedTime', width:100}
		],
		shrinkToFit: false,	// 是否按百分比自动调整列宽

		// 加载成功后执行事件
		ajaxSuccess: function(data){
		}
	});

$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>