package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.form.CartForm;
import com.topdon.website.form.SkuUpdateForm;
import com.topdon.website.mappers.CartMapper;
import com.topdon.website.model.Cart;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.time.LocalDateTime;
import java.util.List;

@Named
public class CartRepository extends MysqlJDBCSupport {

    @Inject
    protected CartRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long create(long userId, CartForm cartForm) {
        @Language("SQL") String sql = "insert into cart(user_id, sn,sn_name, product_id, shopify_product_id, skuId, shopify_variant_id, count, price,msrp_price, create_at) VALUES (:userId,:sn,:snName,:productId,:shopifyProductId,:skuId,:shopifyVariantId,1,:price,:msrpPrice,:now)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("userId", userId);
        params.addValue("sn", cartForm.getSn());
        params.addValue("productId", cartForm.getProductId());
        params.addValue("shopifyProductId", cartForm.getShopifyProductId());
        params.addValue("skuId", cartForm.getSkuId());
        params.addValue("shopifyVariantId", cartForm.getShopifyVariantId());
        params.addValue("price", cartForm.getPrice());
        params.addValue("now", LocalDateTime.now());
        params.addValue("snName", cartForm.getSnName());
        params.addValue("msrpPrice", cartForm.getMsrpPrice());
        return autoIncreaseInsert(sql, params).longValue();
    }

    public List<Cart> list(long userId) {
        @Language("SQL") String sql = "select id, user_id, sn,sn_name, product_id, shopify_product_id, skuId, shopify_variant_id, count, price,msrp_price, create_at from cart where user_id = :userId";
        MapSqlParameterSource param = new MapSqlParameterSource("userId", userId);
        return list(sql, CartMapper.DETAIL, param);
    }

    public long count(long userId) {
        @Language("SQL") String sql = "select count(1) from cart where user_id = :userId";
        MapSqlParameterSource param = new MapSqlParameterSource("userId", userId);
        return object(sql, Long.class, param);
    }

    public int delete(long userId, long id) {
        @Language("SQL") String sql = "delete from cart where id = :id and user_id = :userId";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        params.addValue("userId", userId);
        return update(sql, params);
    }

    public int updateSku(long id, SkuUpdateForm form) {
        @Language("SQL") String sql = "update cart set skuId = :skuId,shopify_variant_id = :shopifyVariantId,price=:price,msrp_price=:msrpPrice where id = :id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        params.addValue("skuId", form.getSkuId());
        params.addValue("shopifyVariantId", form.getShopifyVariantId());
        params.addValue("price", form.getPrice());
        params.addValue("msrpPrice", form.getMsrpPrice());
        return update(sql, params);
    }

    public int increaseCount(long id) {
        @Language("SQL") String sql = "update cart set count = count + 1 where id = :id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        return update(sql, params);
    }

    public AbstractOption<Cart> get(long userId, long productId, long skuId) {
        @Language("SQL") String sql = "select id, user_id, sn,sn_name, product_id, shopify_product_id, skuId, shopify_variant_id, count, price,msrp_price, create_at from cart where product_id=:productId and skuId = :skuId and user_id = :userId";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("userId", userId);
        params.addValue("productId", productId);
        params.addValue("skuId", skuId);
        return option(sql, CartMapper.DETAIL, params);
    }

    public AbstractOption<Cart> get(long id) {
        @Language("SQL") String sql = "select id, user_id, sn,sn_name, product_id, shopify_product_id, skuId, shopify_variant_id, count, price,msrp_price, create_at from cart where id=:id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        return option(sql, CartMapper.DETAIL, params);
    }

    public boolean isExist(long userId, String sn, long productId) {
        @Language("SQL") String sql = "select count(1) from cart where sn=:sn and product_id=:productId and user_id = :userId";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("userId", userId);
        params.addValue("productId", productId);
        params.addValue("sn", sn);
        return object(sql, Integer.class, params) > 0;
    }
}
