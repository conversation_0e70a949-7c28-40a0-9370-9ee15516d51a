package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.model.Banner;
import com.topdon.website.repositories.BannerRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class BannerService {
    private final BannerRepository bannerRepository;

    @Inject
    public BannerService(BannerRepository bannerRepository) {
        this.bannerRepository = bannerRepository;
    }

    public AbstractEither<ErrorMessage, List<Banner>> list() {
        return Right.apply(bannerRepository.list());
    }
}
