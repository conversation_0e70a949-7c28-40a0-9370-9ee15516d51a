package com.topdon.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.topdon.admin.entity.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SellerAuthorizationDetailDTO extends SellerAuthorization {


    private List<Region> regions;
    private List<SellerAuthRegionMapping> sellerAuthRegionMappings;
    private List<SellerAuthProductLinesMapping> sellerAuthProductLinesMappings;
    private List<ProductLines> productLines;
    private List<SellerAuthViolationsDTO> sellerAuthViolations;
    private List<AuthorizedPlatforms> authorizedPlatforms;

    private List<Integer> productLinesList;
    private List<String> regionList;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Override
    public Date getAuthStartDate() {
        return super.getAuthStartDate();
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Override
    public Date getAuthEndDate() {
        return super.getAuthEndDate();
    }
}
