package com.jeesite.modules.classification.service;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.TreeService;
import com.jeesite.modules.classification.dao.ClassificationDao;
import com.jeesite.modules.classification.entity.Classification;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 分类管理Service
 * <AUTHOR>
 * @version 2022-03-02
 */
@Service
@Transactional(readOnly=true)
public class ClassificationService extends TreeService<ClassificationDao, Classification> {
	
	/**
	 * 获取单条数据
	 * @param classification
	 * @return
	 */
	@Override
	public Classification get(Classification classification) {
		return super.get(classification);
	}
	
	/**
	 * 查询分页数据
	 * @param classification 查询条件
	 * @param classification.page 分页对象
	 * @return
	 */
	@Override
	public Page<Classification> findPage(Classification classification) {
		return super.findPage(classification);
	}
	
	/**
	 * 查询列表数据
	 * @param classification
	 * @return
	 */
	@Override
	public List<Classification> findList(Classification classification) {
		return super.findList(classification);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param classification
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(Classification classification) {
		classification.setContrastGroupName(Optional.ofNullable(classification.getContrastGroupName()).map(String::trim).filter(Strings::isNotEmpty).orElseGet(() -> classification.getName()));
		super.save(classification);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(classification, classification.getId(), "classification_banner_image");
		FileUploadUtils.saveFileUpload(classification, classification.getId(), "classification_menu_image");
		FileUploadUtils.saveFileUpload(classification, classification.getId(), "classification_pro_image");
	}
	
	/**
	 * 更新状态
	 * @param classification
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(Classification classification) {
		super.updateStatus(classification);
	}
	
	/**
	 * 删除数据
	 * @param classification
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(Classification classification) {
		super.delete(classification);
	}

	public List<Classification> getByProductId(List<String> productIds) {
		return dao.getByProductId(new Classification(), productIds);
	}
}