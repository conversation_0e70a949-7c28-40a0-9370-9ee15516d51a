<?xml version='1.0' encoding='UTF-8'?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.hiwie</groupId>
    <artifactId>breeze_2.12</artifactId>
    <packaging>pom</packaging>
    <description>breeze</description>
    <version>1.0.0-SNAPSHOT</version>
    <name>breeze</name>
    <organization>
        <name>com.hiwie</name>
    </organization>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <modules>
        <module>src/breeze-core</module>
        <module>src/breeze-json</module>
        <module>src/breeze-bpm</module>
        <module>src/breeze-import</module>
        <module>src/breeze-rest</module>
        <module>src/breeze-unit-test</module>
        <module>src/breeze-validator</module>
        <module>src/breeze-service</module>
        <module>src/breeze-application</module>
        <module>src/breeze-context</module>
        <module>src/breeze-scheduler</module>
        <module>src/breeze-repository</module>
        <module>src/breeze-jdbc</module>
        <module>src/breeze-cache</module>
        <module>src/breeze-security</module>
        <module>src/breeze-security-core</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>