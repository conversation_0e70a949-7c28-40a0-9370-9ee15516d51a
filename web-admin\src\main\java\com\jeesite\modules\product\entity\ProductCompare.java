package com.jeesite.modules.product.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import java.util.List;

/**
 * 产品对比管理Entity
 *
 * <AUTHOR>
 * @version 2022-04-21
 */
@Table(name = "product_compare", alias = "a", label = "产品对比信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "product_id", attrName = "product.id", label = "产品"),
        @Column(name = "image", attrName = "image", label = "图片", isQuery = false),
        @Column(name = "name", attrName = "name", label = "名称", queryType = QueryType.LIKE),
        @Column(name = "type", attrName = "type", label = "类型", isQuery = false),
        @Column(name = "param", attrName = "param", label = "其他参数", isQuery = false),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(name = "name", label = "产品名称", queryType = QueryType.LIKE),})
}, orderBy = "a.id DESC"
)
public class ProductCompare extends DataEntity<ProductCompare> {

    private static final long serialVersionUID = 1L;
    private Product product;
    private String image;        // 图片
    private String name;        // 名称
    private String type;        // 类型
    private String param;        // 其他参数
    private List<ProductExtensionDetail> params = ListUtils.newLinkedList();

    public ProductCompare() {
        this(null);
    }

    public ProductCompare(String id) {
        super(id);
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    @NotBlank(message = "图片不能为空")
    @Size(min = 0, max = 256, message = "图片长度不能超过 256 个字符")
    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @NotBlank(message = "名称不能为空")
    @Size(min = 0, max = 56, message = "名称长度不能超过 56 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @NotBlank(message = "类型不能为空")
    @Size(min = 0, max = 50, message = "类型长度不能超过 50 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Size(min = 0, max = 1024, message = "其他参数长度不能超过 1024 个字符")
    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public List<ProductExtensionDetail> getParams() {
        return params;
    }

    public void setParams(List<ProductExtensionDetail> params) {
        this.params = params;
    }
}