package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "menu_click_log")
public class MenuClickLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "ip_address")
    private String ipAddress;

    @TableField(value = "ip_location")
    private String ipLocation;

    @TableField(value = "menu_name")
    private String menuName;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    @TableField(value = "click_time")
    private Date clickTime;

    @TableField(value = "user_agent")
    private String userAgent;
}