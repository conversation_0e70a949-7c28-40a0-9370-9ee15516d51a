package com.topdon.admin.controller;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.topdon.admin.dto.ProductLinesDTO;
import com.topdon.admin.entity.ProductLines;
import com.topdon.admin.entity.SellerAuthProductLinesMapping;
import com.topdon.admin.service.ProductLinesService;
import com.topdon.admin.service.SellerAuthProductLinesMappingService;
import com.topdon.admin.vo.ProductLinesExportVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("${adminPath}/product/lines")
public class ProductLinesController {

    @Resource
    private ProductLinesService productLinesService;
    @Resource
    private SellerAuthProductLinesMappingService sellerAuthProductLinesMappingService;

    @RequiresPermissions("product:lines:view")
    @GetMapping
    public String view(Model model) throws JsonProcessingException {
        return "modules/product/productLines";
    }

    @ResponseBody
    @PostMapping("/tree")
    public List<Tree<Integer>> tree(@RequestBody ProductLinesDTO productLinesDTO) {
        return productLinesService.getTree(productLinesDTO);
    }

    @RequiresPermissions("product:lines:edit")
    @ResponseBody
    @PostMapping("/saveOrUpdate")
    public String saveOrUpdate(@RequestBody ProductLines productLines) {
        Long count = productLinesService.lambdaQuery()
                .ne(productLines.getId() != null, ProductLines::getId, productLines.getId())
                .eq(ProductLines::getType, productLines.getType())
                .eq(ProductLines::getParentId, productLines.getParentId())
                .eq(ProductLines::getName, productLines.getName())
                .count();
        if (count > 0) {
            return "存在相同的品线/系列/型号名称";
        }
        productLinesService.saveOrUpdate(productLines);
        return null;
    }

    @RequiresPermissions("product:lines:edit")
    @ResponseBody
    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Integer id) {
        List<Integer> ids = productLinesService.lambdaQuery()
                .eq(ProductLines::getParentId, id)
                .list()
                .stream()
                .map(ProductLines::getId)
                .collect(Collectors.toList());

        ids.add(id);

        if(sellerAuthProductLinesMappingService.lambdaQuery()
                .in(SellerAuthProductLinesMapping::getProductLinesId,ids)
                .count()>0){
            return "该品线/系列/型号在授权卖家种被使用";
        }

        return productLinesService.delete(id);
    }


    /**
     * 导出用户数据
     */
    @RequiresPermissions("product:lines:view")
    @PostMapping(value = "/exportData")
    public void exportData(@RequestBody ProductLinesDTO productLinesDTO, HttpServletResponse response) {
        List<ProductLinesExportVo> list = productLinesService.exportData(productLinesDTO);
        String fileName = "授权产品" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try(ExcelExport ee = new ExcelExport("授权产品", ProductLinesExportVo.class)){
            ee.setDataList(list).write(response, fileName);
        }
    }
}
