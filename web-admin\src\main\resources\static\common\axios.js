axios.interceptors.response.use(function (response) {
    console.log('Response data:', response);
    if (response.data.result === 'login') {
        js.closeLoading()
        createLoginDiv()
        throw new Error(response.data.message)
    }else if(response.data.byteLength <= 200){
        const arrayBuffer = response.data;  // 这是从 axios 返回的 ArrayBuffer 数据
        const string = arrayBufferToString(arrayBuffer);
        let data = JSON.parse(string);
        if (data?.result === 'login') {
            js.closeLoading()
            createLoginDiv()
            throw new Error(data.message)
        }
    }
    js.closeLoading()
    return response;
}, function (error) {
    console.log("响应错误", error)
    js.showErrorMessage(error.message)
    js.closeLoading()
    return Promise.reject(error);
});

axios.interceptors.request.use(
    (config) => {
        js.loading()

        return config;
    },
    (error) => {
        js.closeLoading()
        console.error('请求错误:', error);
        return Promise.reject(error);
    }
);


function arrayBufferToString(buffer) {
    const decoder = new TextDecoder('utf-8');  // 使用 UTF-8 编码
    return decoder.decode(buffer);  // 转换为字符串
}

function createLoginDiv() {
    const mask = document.createElement('div');
    mask.id = 'loginDivMask'
    mask.style.position = 'fixed';
    mask.style.top = '0';
    mask.style.left = '0';
    mask.style.width = '100%';
    mask.style.height = '100%';
    mask.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';  // 半透明黑色
    mask.style.zIndex = '9999';  // 确保在最上层

    const alertBox = document.createElement('div');
    alertBox.style.position = 'absolute';
    alertBox.style.top = '50%';
    alertBox.style.left = '50%';
    alertBox.style.transform = 'translate(-50%, -50%)';  // 居中显示
    alertBox.style.backgroundColor = 'white';  // 白色背景
    alertBox.style.padding = '20px';
    alertBox.style.borderRadius = '10px';
    alertBox.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
    alertBox.style.textAlign = 'center';

    const message = document.createElement('p');
    message.textContent = '登录过期，请登录后返回继续操作';
    alertBox.appendChild(message);

    const loginButton = document.createElement('button');
    loginButton.textContent = '登录';
    loginButton.style.marginTop = '20px';
    loginButton.style.padding = '10px 20px';
    loginButton.style.fontSize = '16px';
    loginButton.style.cursor = 'pointer';
    loginButton.style.backgroundColor = '#4CAF50';  // 绿色背景
    loginButton.style.color = 'white';
    loginButton.style.border = 'none';
    loginButton.style.borderRadius = '5px';
    loginButton.style.transition = 'background-color 0.3s';

    loginButton.addEventListener('mouseover', () => {
        loginButton.style.backgroundColor = '#45a049';  // 鼠标悬停时改变按钮颜色
    });

    loginButton.addEventListener('mouseout', () => {
        loginButton.style.backgroundColor = '#4CAF50';  // 恢复原来的按钮颜色
    });

    loginButton.addEventListener('click', () => {
        window.open('/admin/a/login', '_blank');

        document.body.removeChild(document.getElementById('loginDivMask'))
        document.body.removeChild(document.getElementById('loginDivMask'))
    });

    alertBox.appendChild(loginButton);
    mask.appendChild(alertBox);

    document.body.appendChild(mask);
}