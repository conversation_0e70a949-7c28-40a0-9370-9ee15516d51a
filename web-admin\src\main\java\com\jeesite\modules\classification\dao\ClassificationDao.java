package com.jeesite.modules.classification.dao;

import com.jeesite.common.dao.TreeDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.jeesite.modules.classification.entity.Classification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分类管理DAO接口
 * <AUTHOR>
 * @version 2022-03-02
 */
@MyBatisDao
public interface ClassificationDao extends TreeDao<Classification> {

    public List<Classification> getByProductId(@Param("c") Classification classification, @Param("productIds") List<String> productIds);
}