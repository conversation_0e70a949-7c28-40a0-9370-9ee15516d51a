package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.repository.PaginationForm;
import com.topdon.website.form.NewsQueryForm;
import com.topdon.website.model.News;
import com.topdon.website.repositories.NewsRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class NewsService {

    private final NewsRepository newsRepository;

    @Inject
    public NewsService(NewsRepository newsRepository) {
        this.newsRepository = newsRepository;
    }

    public AbstractEither<ErrorMessage, Long> count(NewsQueryForm queryForm) {
        return Right.apply(newsRepository.count(queryForm));
    }

    public AbstractEither<ErrorMessage, List<News>> list(NewsQueryForm queryForm, PaginationForm form) {
        return Right.apply(newsRepository.list(queryForm, form));
    }

    public AbstractEither<ErrorMessage, News> get(String id) {
        return newsRepository.get(id).toRight(News.Errors.NOT_FOUND);
    }
}
