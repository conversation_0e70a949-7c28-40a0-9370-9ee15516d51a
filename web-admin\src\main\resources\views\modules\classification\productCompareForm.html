<% layout('/layouts/default.html', {title: '产品对比管理', libs: ['validate','dataGrid']}){ %>
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<div class="col-sm-8 hidden">
    <#form:listselect id="productSelect" title="用户选择" path="product.id" labelPath="product.name"
    url="${ctx}/product/product/select" allowClear="false"
    checkbox="false" itemCode="id" itemName="name" class="required"/>
</div>
<div class="main-content" id="compareMainContent">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i>编辑产品对比属性
            </div>
            <div class="box-tools pull-right">
                <div class="btn btn-info" @click="save(true)"><i class="fa fa-save"></i>保存草稿</div>
                <div class="btn btn-primary" :class="saveDraft? '':'disabled'" :title="saveDraft ? '':'先保存草稿后发布'" @click="save(false)"><i class="fa fa-check"></i>发布</div>
                <div class="btn btn-default" @click="$refs.fileInput.click()"><i class="fa fa-arrow-down"></i>导入Excel</div>
                <input
                        type="file"
                        id="file"
                        ref="fileInput"
                        @change="handleFileChange"
                        accept=".xlsx, .xls"
                        class="hidden"
                />
            </div>
        </div>
        <div class="box-body">
            <div style="width: 100%;overflow:auto;">
                <table id="compareTable" class="compare-table" style="border-collapse: collapse" :style="compareTableStyle">
                    <thead>
                    <tr class="table-header">
                        <th >Main Category</th>
                        <th >Subcategory</th>
                        <th v-for="(item,index) in productList" :key="item.name" style="width: 300px;cursor: move"
                            @contextmenu.prevent="showContextMenu($event,{menu:[{text:'删除',show:true,handle:()=>deleteProductItem(index)}]})"
                            draggable="true"
                            @dragstart="onProductDragStart(index)"
                            @dragover="(e)=>{ if (productDraggedIndex!=null)  e.preventDefault() }"
                            @drop="onProductDrop(index)"
                            v-text="item.name"></th>
                        <th style="text-align: left">
                            <div class="btn btn-default btn-mini" @click="()=>{$('#productSelectName').click()}"><i class="fa fa-plus"></i>添加产品</div>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(item,index) in mergeData" :key="item.category + item?.mainCategory?.category">
                        <td v-if="item.mainCategory" :rowspan="item.mainCategory.rowspan"
                            :style="item.mainCategory.select ? 'border: 2px solid orange':''"
                            @dragstart="onMainCategoryDragStart(item.mainCategory.category)"
                            @drop="onMainCategoryDrop(item.mainCategory.category)"
                            draggable="true"
                            @dragover="(e)=>{ if (mainCategoryDraggedIndex!=null) e.preventDefault() }"
                            @contextmenu.prevent="mainCategoryClick(item,index);showContextMenu($event,{menu:[{text:'添加子分类',show:true,handle:()=>addSubcategory(item,index)},
                            {text:'删除',show:true,handle:()=>deleteParentItem(item)}]})"
                            tabindex="0"
                            @keyup.esc="computeCategoryMergeData"
                            @click="mainCategoryClick(item,index)"
                            @dblclick="()=>{$set(mergeData,index,{...item,mainCategory:{...item.mainCategory,edit:true}});editInputText = item.mainCategory.category}"
                            style="width: 180px;outline: none;cursor: move">

                            <div v-if="item.mainCategory.edit">
                                <input type="text" style="border: 0;width: 100%" v-focus v-model="editInputText" @click.stop
                                       @keyup.enter="mainCategoryInputEnter(item,index)"
                                       @keyup.esc="$set(mergeData,index,{...item,mainCategory:{...item.mainCategory,edit:false}})">
                            </div>
                            <div v-else v-text="item.mainCategory.category"></div>
                        </td>
                        <td style="width: 400px;text-align: left;outline: none;cursor: move"
                            @dragstart="onSubcategoryDragStart(index,item)"
                            @drop="onSubcategoryDrop(index)"
                            draggable="true"
                            @dragover="(e)=>{ if (subcategoryDraggedIndex!=null&&draggedParentCategory===item.parentCategory) e.preventDefault() }"
                            tabindex="0"
                            :style="item.select ? 'border: 2px solid orange':''"
                            @keyup.esc="computeCategoryMergeData"
                            @click="subcategoryClick(item,index)"
                            @dblclick="()=>{$set(mergeData,index,{...item,edit:true});editInputText = item.category}"
                            @contextmenu.prevent="subcategoryClick(item,index);showContextMenu($event,{menu:[{text:'删除',show:true,handle:()=>deleteSubcategoryItem(item)}]})">


                            <div v-if="item.edit" >
                                <input type="text" style="border: 0;width: 100%" v-focus v-model="editInputText"
                                       @click.stop @keyup.enter="subcategoryInputEnter(item,index)"
                                       @keyup.esc="$set(mergeData,index,{...item,edit:false})">
                            </div>
                            <div v-else v-text="item.category"></div>
                        </td>

                        <td v-for="(product,index) in productList" :key="item.name" style="width: 300px;height: 0"
                            :style="product.categoryList.filter(c => c.category === item.category)[0]?.select ? 'border: 2px solid orange':''"
                            @click="productCategoryClick(product,index,item.category)"
                            @dblclick="productCategoryDbClick(product,index,item.category)">
                            <div v-if="product.categoryList.filter(c => c.category === item.category)[0]?.edit" style="height: 100%;width: 100%">
                                <textarea type="text" style="border: 0;width: 92%;height: 100%" v-focus v-model="editInputText"
                                          @click.stop
                                          @keyup.esc="resetProductSelect"></textarea>
                                <i class="fa fa-check" style="cursor: pointer" @click="productCategoryInputEnter(product,index,item.category)"></i>
                            </div>
                            <div v-else style="white-space: pre-line" v-text="product.categoryList.filter(c => c.category === item.category)[0]?.val"></div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="btn btn-default btn-mini" @click="mainCategoryAddClick"><i class="fa fa-plus"></i>添加主分类</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!-- 自定义的右键菜单 -->
                <ul v-if="contextMenu.isContextMenuVisible"
                    :style="{ top: contextMenu.menuPosition.y + 'px', left: contextMenu.menuPosition.x + 'px' }"
                    class="context-menu">
                    <li @click="item.handle()" v-for="item in contextMenu.menu" v-if="item.show">{{ item.text }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<script>
    var compareId = ${compareId}
    var ctx = '${ctx}'
</script>
<% } %>
<style>
    .compare-table {
        border-collapse: collapse;
        th, td {
            background-color: #ffffff;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #4471c5;
            color: white;
        }
        th:nth-child(1) {
            width: 150px;
        }
        th:nth-child(2) {
            width: 300px;
        }
    }
    .context-menu {
        position: absolute;
        list-style-type: none;
        padding: 5px;
        margin: 0;
        background-color: #fff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .context-menu li {
        padding: 8px 12px;
        cursor: pointer;
    }

    .context-menu li:hover {
        background-color: #eee;
    }
</style>
<script>

    new Vue({
        el:'#compareMainContent',
        data:{
            contextMenu:{
                isContextMenuVisible: false,
                menuPosition: { x: 0, y: 0 },
            },
            editInputText:'',
            mergeData:[],
            propertyCategory:{},
            productList:[],
            productDraggedIndex:null,
            subcategoryDraggedIndex:null,
            draggedParentCategory:'',
            mainCategoryDraggedIndex:null,
            saveDraft: false,
        },
        computed:{
            compareTableStyle(){
                let baseWidth = 600
                let width = baseWidth + (this.productList.length * 300)
                return 'width:'  + width + 'px'
            }
        },
        directives: {
            focus: {
                // 自定义指令：在元素插入到 DOM 时获取焦点
                inserted(el) {
                    el.focus();
                }
            }
        },
        watch: {
            mergeData(newValue, oldValue) {
                this.saveDraft = false
            },
            productList(newValue, oldValue) {
                this.saveDraft = false
            }
        },
        methods:{
            // 删除产品
            deleteProductItem(index){
                this.productList.splice(index, 1);
            },
            // 产品拖拽开始
            onProductDragStart(index){
                this.productDraggedIndex = index;
            },
            // 产品拖拽结束
            onProductDrop(index){
                // 实现拖放逻辑
                const draggedItem = this.productList[this.productDraggedIndex];
                this.productList.splice(this.productDraggedIndex, 1); // 移除拖动的项
                this.productList.splice(index, 0, draggedItem); // 在目标位置插入拖动的项
                this.productDraggedIndex = null; // 清除拖动状态
                // 设置产品属性顺序
                this.productList.forEach((item,index)=>{
                    item.categoryList.forEach(c=>{
                        c.sort = index
                    })
                })
            },
            // 主分类拖拽开始
            onMainCategoryDragStart(category){
                this.propertyCategory[0].forEach((item,index)=>{
                    if(item.category === category){
                        this.mainCategoryDraggedIndex = index;
                    }
                })
            },
            // 主分类拖拽结束
            onMainCategoryDrop(category){
                let index;
                this.propertyCategory[0].forEach((item,i)=>{
                    if(item.category === category){
                        index = i;
                    }
                })
                // 实现拖放逻辑
                const draggedItem = this.propertyCategory[0][this.mainCategoryDraggedIndex];
                this.propertyCategory[0].splice(this.mainCategoryDraggedIndex, 1); // 移除拖动的项
                this.propertyCategory[0].splice(index, 0, draggedItem); // 在目标位置插入拖动的项
                this.mainCategoryDraggedIndex = null; // 清除拖动状态
                this.computeCategoryMergeData()
            },
            // 子分类拖拽开始
            onSubcategoryDragStart(index,item){
                this.draggedParentCategory = item.parentCategory
                this.subcategoryDraggedIndex = index;
            },
            // 子分类拖拽结束
            onSubcategoryDrop(index){
                // 实现拖放逻辑
                const draggedItem = this.propertyCategory[1][this.subcategoryDraggedIndex];
                this.propertyCategory[1].splice(this.subcategoryDraggedIndex, 1); // 移除拖动的项
                this.propertyCategory[1].splice(index, 0, draggedItem); // 在目标位置插入拖动的项
                this.subcategoryDraggedIndex = null; // 清除拖动状态
                this.computeCategoryMergeData()
            },
            // 产品属性回车
            productCategoryInputEnter(product,index,category){
                product.categoryList.forEach(item=>{
                    if(item.category === category){
                        item.val = this.editInputText
                    }
                })
                this.$set(this.productList,index,{...product})
                this.resetProductSelect()
            },
            // 产品属性单击
            productCategoryClick(product,index,category){
                this.resetProductSelect()
                this.computeCategoryMergeData()
                product.categoryList.forEach(item=>{
                    item.select = item.category === category
                })
                this.$set(this.productList,index,{...product})
            },
            // 产品属性双击单击
            productCategoryDbClick(product,index,category){
                this.computeCategoryMergeData()
                product.categoryList.forEach(item=>{
                    if(item.category === category){
                        item.edit = item.category === category
                        this.editInputText = item.val
                    }else{
                        item.edit = false
                    }
                })
                this.$set(this.productList,index,{...product})
            },
            // 重置产品属性选择
            resetProductSelect(){
                this.productList.forEach((product,index)=>{
                    product.categoryList.forEach(item=>{
                        item.select = false
                        item.edit = false
                    })
                    this.$set(this.productList,index,{...product})
                })
            },
            // 删除子分类
            deleteSubcategoryItem(item1){
                this.propertyCategory[1] = this.propertyCategory[1].filter(item=>item.category !== item1.category && item.parentCategory !== item1.parentCategory)
                this.productList.forEach(item=>{
                    item.categoryList.forEach(c=>{
                        if(c.category === item1.category && c.parentCategory === item1.parentCategory){
                            c.val = ''
                        }
                    })
                })
                this.computeCategoryMergeData()
            },
            // 删除父分类
            deleteParentItem(item1){
                this.propertyCategory[0] = this.propertyCategory[0].filter(item=>item.category !== item1.parentCategory)
                this.propertyCategory[1] = this.propertyCategory[1].filter(item=> {
                    let r = item.parentCategory !== item1.parentCategory
                    if(!r){
                        this.productList.forEach(p=>{
                            p.categoryList.forEach(c=>{
                                if(c.category === item.category){
                                    c.val = ''
                                }
                            })
                        })
                    }
                    return r
                })
                this.computeCategoryMergeData()
            },
            // 展示右键菜单
            showContextMenu(event,item) {
                this.contextMenu.menu = item.menu
                this.contextMenu.isContextMenuVisible = true;
                this.contextMenu.menuPosition = {
                    x: event.pageX,
                    y: event.pageY
                };

                // 点击其他地方时关闭菜单
                document.addEventListener('click', ()=>{
                    this.contextMenu.isContextMenuVisible = false;
                    document.removeEventListener('click', this);
                });
            },
            // 导入文件
            async handleFileChange(event){
                let file = event.target.files[0];
                if (!file) return;

                const formData = new FormData();
                formData.append('file', file);
                formData.append('compareId',compareId)

                try {

                    let response = await axios.post(ctx + '/product/property/importExcel', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    })

                    if (response.data) {
                        js.showErrorMessage(response.data)
                    } else {
                        js.showMessage('导入成功')
                    }
                    this.clearFileInput()
                    await this.fetchData()
                    this.saveDraft = true
                } catch (error) {

                    this.clearFileInput()
                    console.error("There was an error!", error);
                }

            },
            clearFileInput() {
                let fileInput = this.$refs.fileInput
                if (fileInput.value) {
                    fileInput.value.value = '';
                }
                fileInput.value = null;
            },
            // 保存
            async save(draft) {
                if (!draft && !this.saveDraft){
                    return
                }
                let propertyCategories = []
                let productProperties = []
                this.propertyCategory[0].forEach(item => propertyCategories.push(item))
                this.propertyCategory[1].forEach(item => propertyCategories.push(item))
                for (const propertyCategory of propertyCategories) {
                    if (!propertyCategory.category) {
                        js.showErrorMessage('属性值不能为空')
                        return
                    }
                }
                this.productList.forEach((item,i) => {
                    item.categoryList.forEach((item1) => {
                        productProperties.push({
                            sort:i,
                            category: item1.category,
                            val: item1.val,
                            productId: item.productId,
                            id: item.id
                        })
                    })
                })

                try {
                    let saveResult = await axios.post(ctx + '/product/property/save', {
                        compareId: compareId,
                        draft,
                        propertyCategories,
                        productProperties
                    })

                    if (saveResult.data?.message) {
                        js.showMessage(saveResult.data?.message)
                        return
                    } else {
                        js.showMessage(draft ? '保存成功' : '发布成功')
                    }
                    await this.fetchData()
                    this.saveDraft = true
                } catch (error) {

                    console.error("There was an error!", error);
                }
            },
            // 子分类输入框确认事件
            subcategoryInputEnter(item,index){
                let parentCategory = item.parentCategory
                let category = item.category
                if(item.category !== this.editInputText && this.propertyCategory[1].filter(item => item.category === this.editInputText).length){
                    js.showErrorMessage('子分类已存在')
                    return
                }
                console.log(this.productList)
                this.productList.forEach(product => {
                    if (product) {
                        product.categoryList.filter(item1 => item1.category === item.category).forEach(item1 => item1.category = this.editInputText)
                    }
                })
                this.propertyCategory[1].filter(item => parentCategory === item.parentCategory && item.category === category).forEach(item1=>item1.category = this.editInputText)
                this.computeCategoryMergeData()
            },
            // 添加子分类
            addSubcategory(item, index) {
                let mainCategory = item.mainCategory
                this.propertyCategory[1].push({category: 'New Subcategory' + (this.propertyCategory[1].filter(item => item.category.startsWith('New Subcategory')).length+1), parentCategory: mainCategory.category})
                this.computeCategoryMergeData()
            },
            // 主分类输入框确认事件
            mainCategoryInputEnter(item,index){
                let mainCategory = item.mainCategory
                if(mainCategory.category !== this.editInputText && this.propertyCategory[0].filter(item => item.category === this.editInputText).length){
                    js.showErrorMessage('主分类已存在')
                    return
                }
                this.propertyCategory[1].filter(item1 => mainCategory.category === item1.parentCategory).forEach(item1=>item1.parentCategory = this.editInputText)
                this.propertyCategory[0].filter(item1 => mainCategory.category === item1.category)[0].category = this.editInputText
                this.computeCategoryMergeData()
            },
            // 子分类单击
            subcategoryClick(item,index){
                this.resetProductSelect()
                this.computeCategoryMergeData();
                if(item.mainCategory){
                    item.mainCategory.select = false
                    item.mainCategory.edit = false
                }
                this.$set(this.mergeData,index,{...item,select:true})
            },
            // 主分类单击
            mainCategoryClick(item,index){
                this.resetProductSelect()
                this.computeCategoryMergeData()
                this.$set(this.mergeData, index, {...item, select: false,edit: false,mainCategory: {...item.mainCategory, select: true}})
            },
            // 添加主分类
            mainCategoryAddClick() {
                if (!this.propertyCategory[0]) {
                    this.propertyCategory[0] = []
                }
                if (!this.propertyCategory[1]) {
                    this.propertyCategory[1] = []
                }
                let category = 'New Main Category' + (this.propertyCategory[0].filter(item => item.category.startsWith('New Main Category')).length + 1);
                this.propertyCategory[0].push({category: category})
                this.propertyCategory[1].push({
                    category: 'New Subcategory' + (this.propertyCategory[1].filter(item => item.category.startsWith('New Subcategory')).length + 1),
                    parentCategory: category
                })
                this.computeCategoryMergeData()
            },
            // 计算合并表格数据
            computeCategoryMergeData(){
                let subcategoryIndex = 0
                this.mergeData = []
                let parentCategoryLen = this.propertyCategory[0].length
                this.propertyCategory[0].forEach((pItem,pIndex) => {
                    pItem.sort = pIndex
                    // 子分类
                    let subcategory = this.propertyCategory[1].filter(subItem => subItem.parentCategory === pItem.category)
                    if(subcategory.length === 0){
                        subcategory = [{
                            parentCategory: pItem.category,
                            category:'',
                            draft:true,
                            sort:0,
                            classificationCompareId:compareId,

                        }]
                    }
                    let subcategoryLen = subcategory.length
                    subcategory.forEach((item, index) => {
                        item.sort = subcategoryIndex
                        subcategoryIndex++
                        // 如果是第一个子分类 添加父分类信息
                        item.isFirst = index === 0
                        item.isLast = index === subcategoryLen - 1
                        if (index === 0) {
                            item.mainCategory = pItem
                            item.mainCategory.rowspan = subcategory.length
                            item.mainCategory.isFirst = pIndex === 0;
                            item.mainCategory.isLast = pIndex === parentCategoryLen - 1;
                        }else {
                            item.mainCategory = null
                        }
                        this.mergeData.push(item)
                    })
                })
                this.propertyCategory[1].sort((a, b) => a.sort - b.sort)
                this.mergeData.forEach((item,index)=>{
                    let mainCategory = null
                    if(item.mainCategory){
                        mainCategory = {...item.mainCategory,select:false}
                    }
                    this.$set(this.mergeData,index,{...item,select:false,mainCategory})
                })
            },
            async fetchData(){

                try {
                    let response = await axios.get(ctx + '/property/category/list/' + compareId, {
                        params: {
                            draft: true
                        }
                    })

                    this.propertyCategory = response.data
                    await this.getProductList(this.propertyCategory[1].map(item => item.id))
                    this.computeCategoryMergeData()
                } catch (error) {

                    console.error("There was an error!", error);
                }
            },
            async getProductList(categoryIds){
                try {

                    let that = this
                    let response = await axios.post(ctx + '/product/property/listByCategoryId', {
                        categoryId: categoryIds,
                        compareId: compareId,
                        draft: true
                    })

                    that.productList = []
                    response.data.forEach(item => {
                        let productInfo = {
                            productId: item.productId,
                            name: item.productName,
                            categoryList: item.productProperties,
                            sort: item.sort
                        }
                        that.productList.push(productInfo)
                    })
                    that.productList = this.productList.sort((a, b) => a.sort - b.sort)
                } catch (error) {

                    console.error("There was an error!", error);
                }
            }
        },
        mounted(){
            this.fetchData()
            let that = this
            $('#productSelectCode').on('change', function() {
                var id = $(this).val();

                setTimeout(function () {
                    var name = $('#productSelectName').val();

                    if(!name){
                        return
                    }
                    if(that.productList.filter(item => item.name === name && item.productId === id).length){
                        js.showErrorMessage('产品已存在')
                        return
                    }

                    let categoryList = that.propertyCategory[1].map(item=>{
                        return {category:item.category,val:''}
                    })

                    let productInfo = {
                        productId: id,
                        name: name,
                        categoryList: categoryList,
                        sort: that.productList.length
                    }
                    that.productList.push(productInfo)
                },100)
            });
        },
    })
</script>