package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.repository.PaginationForm;
import com.topdon.website.form.AuthorizedDealerQueryForm;
import com.topdon.website.model.AuthorizedDealer;
import com.topdon.website.model.Region;
import com.topdon.website.repositories.AuthorizedDealerRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class AuthorizedDealerService {

    private final AuthorizedDealerRepository authorizedDealerRepository;
    private final RegionService regionService;

    @Inject
    public AuthorizedDealerService(AuthorizedDealerRepository authorizedDealerRepository, RegionService regionService) {
        this.authorizedDealerRepository = authorizedDealerRepository;
        this.regionService = regionService;
    }

    public AbstractEither<ErrorMessage, Long> count(AuthorizedDealerQueryForm queryForm) {
        return Right.apply(authorizedDealerRepository.count(queryForm));
    }

    public AbstractEither<ErrorMessage, List<AuthorizedDealer>> list(AuthorizedDealerQueryForm queryForm, PaginationForm form) {
        List<AuthorizedDealer> dealers = authorizedDealerRepository.list(queryForm, form);
        dealers.forEach(authorizedDealer -> authorizedDealer.setCountry(regionService.get(authorizedDealer.getCountry().getId()).fold(errorMessage -> new Region(), region -> region)));
        return Right.apply(dealers);
    }
}
