import com.hiwie.breeze.sbt.Breeze._

organization in ThisBuild := "com.david"

version in ThisBuild := "1.0.0-SNAPSHOT"

name := "platform"

scalaVersion := "2.12.6"
test in ThisBuild := {}
//
//lazy val internal_rest = BreezeProject("platform-internal-rest").enablePlugins(BreezePlatform).enablePlugins(JettyPlugin).settings(
//  webappWebInfClasses := true,
//  libraryDependencies ++= Seq(
//    "com.alwutech.activity" % "security-rest" % "1.0.0-SNAPSHOT",
//    "mysql" % "mysql-connector-java" % "5.1.49",
//    "com.alwutech.activity" % "game-rest" % "1.0.0-SNAPSHOT",
//    hikari_cp
//  )
//)

lazy val topdon_new_rest = BreezeProject("platform-topdon-rest").enablePlugins(BreezePlatform).enablePlugins(JettyPlugin).settings(
  webappWebInfClasses := true,
  libraryDependencies ++= Seq(
    "com.alwutech.activity" % "security-rest" % "1.0.0-SNAPSHOT",
    "mysql" % "mysql-connector-java" % "8.0.30",
    "com.topdon" % "website-rest" % "1.0.0-SNAPSHOT",
    "com.aliyun.oss" % "aliyun-sdk-oss" % "3.8.0",
    hikari_cp
  )
)
//
//lazy val function_rest = BreezeProject("platform-function-rest").enablePlugins(BreezePlatform).enablePlugins(JettyPlugin).settings(
//  webappWebInfClasses := true,
//  libraryDependencies ++= Seq(
//    "mysql" % "mysql-connector-java" % "8.0.25",
//    "com.pa" % "function-rest" % "1.0.0-SNAPSHOT",
//    hikari_cp
//  )
//)
//
//lazy val city_rest = BreezeProject("platform-city-rest").enablePlugins(BreezePlatform).enablePlugins(JettyPlugin).settings(
//  webappWebInfClasses := true,
//  libraryDependencies ++= Seq(
//    "mysql" % "mysql-connector-java" % "8.0.25",
//    "com.pa" % "city-rest" % "1.0.0-SNAPSHOT",
//    hikari_cp
//  )
//)
