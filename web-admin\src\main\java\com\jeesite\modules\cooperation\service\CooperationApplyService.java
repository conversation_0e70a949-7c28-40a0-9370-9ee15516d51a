package com.jeesite.modules.cooperation.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.cooperation.entity.CooperationApply;
import com.jeesite.modules.cooperation.dao.CooperationApplyDao;

/**
 * 申请管理Service
 * <AUTHOR>
 * @version 2022-03-08
 */
@Service
@Transactional(readOnly=true)
public class CooperationApplyService extends CrudService<CooperationApplyDao, CooperationApply> {
	
	/**
	 * 获取单条数据
	 * @param cooperationApply
	 * @return
	 */
	@Override
	public CooperationApply get(CooperationApply cooperationApply) {
		return super.get(cooperationApply);
	}
	
	/**
	 * 查询分页数据
	 * @param cooperationApply 查询条件
	 * @param cooperationApply.page 分页对象
	 * @return
	 */
	@Override
	public Page<CooperationApply> findPage(CooperationApply cooperationApply) {
		return super.findPage(cooperationApply);
	}
	
	/**
	 * 查询列表数据
	 * @param cooperationApply
	 * @return
	 */
	@Override
	public List<CooperationApply> findList(CooperationApply cooperationApply) {
		return super.findList(cooperationApply);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param cooperationApply
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(CooperationApply cooperationApply) {
		super.save(cooperationApply);
	}
	
	/**
	 * 更新状态
	 * @param cooperationApply
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(CooperationApply cooperationApply) {
		super.updateStatus(cooperationApply);
	}
	
	/**
	 * 删除数据
	 * @param cooperationApply
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(CooperationApply cooperationApply) {
		super.delete(cooperationApply);
	}
	
}