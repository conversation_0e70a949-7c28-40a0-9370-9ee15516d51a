<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="jeesite-web" />
        <module name="breeze-core" />
        <module name="breeze-application" />
        <module name="breeze-validator" />
        <module name="breeze-json" />
        <module name="breeze-import" />
        <module name="platform-topdon-rest" />
        <module name="breeze-rest" />
        <module name="breeze-context" />
        <module name="breeze-security" />
        <module name="breeze-service" />
        <module name="breeze-unit-test" />
        <module name="breeze-security-core" />
        <module name="breeze-cache" />
        <module name="breeze-repository" />
        <module name="breeze-jdbc" />
        <module name="breeze-bpm" />
        <module name="breeze-scheduler" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="jeesite-module-core" target="1.8" />
      <module name="jeesite-module-template" target="1.8" />
      <module name="jeesite-parent" target="1.8" />
      <module name="security-api" target="11" />
      <module name="security-core" target="11" />
      <module name="security-management-rest" target="11" />
      <module name="security-rest" target="11" />
      <module name="security-service" target="11" />
      <module name="security-spring-integration" target="11" />
      <module name="security-test" target="11" />
      <module name="security_2.12" target="11" />
      <module name="website-api" target="11" />
      <module name="website-core" target="11" />
      <module name="website-rest" target="11" />
      <module name="website-service" target="11" />
      <module name="website_2.12" target="11" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="breeze-application" options="" />
      <module name="breeze-bpm" options="" />
      <module name="breeze-cache" options="" />
      <module name="breeze-context" options="" />
      <module name="breeze-core" options="" />
      <module name="breeze-dependencies" options="" />
      <module name="breeze-import" options="" />
      <module name="breeze-jdbc" options="" />
      <module name="breeze-json" options="" />
      <module name="breeze-repository" options="" />
      <module name="breeze-rest" options="" />
      <module name="breeze-scheduler" options="" />
      <module name="breeze-security" options="" />
      <module name="breeze-security-core" options="" />
      <module name="breeze-service" options="" />
      <module name="breeze-unit-test" options="" />
      <module name="breeze-validator" options="" />
      <module name="jeesite-module-core" options="-parameters" />
      <module name="jeesite-module-template" options="-parameters" />
      <module name="jeesite-parent" options="-parameters" />
      <module name="jeesite-web" options="-parameters" />
      <module name="platform-topdon-rest" options="" />
      <module name="security-api" options="" />
      <module name="security-core" options="" />
      <module name="security-management-rest" options="" />
      <module name="security-rest" options="" />
      <module name="security-service" options="" />
      <module name="security-spring-integration" options="" />
      <module name="security-test" options="" />
      <module name="website-api" options="" />
      <module name="website-core" options="" />
      <module name="website-rest" options="" />
      <module name="website-service" options="" />
    </option>
  </component>
</project>