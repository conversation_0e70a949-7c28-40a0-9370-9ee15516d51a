package com.topdon.website.services;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.security.models.Session;
import com.topdon.website.form.CartForm;
import com.topdon.website.helper.ShopifyGraphQLClient;
import com.topdon.website.helper.ShopifyGraphQLClientService;
import com.topdon.website.helper.SignUtil;
import com.topdon.website.model.Cart;
import com.topdon.website.model.ShopifyCustomer;
import com.topdon.website.model.graphql.model.CartCreate;
import com.topdon.website.model.graphql.model.CartCreateResponse;
import com.topdon.website.model.graphql.model.Checkout;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Named
public class CartShopifyService {

    private final CartService cartService;
    private final ShopifyGraphQLClientService shopifyGraphQLClientService;

    @Inject
    public CartShopifyService(CartService cartService, ShopifyGraphQLClientService shopifyGraphQLClientService) {
        this.cartService = cartService;
        this.shopifyGraphQLClientService = shopifyGraphQLClientService;
    }

    public AbstractEither<ErrorMessage, CartCreate> checkoutSingle(Session session, CartForm cartForm) {
        ArrayList<CartCreate.LinesDTO> lines = new ArrayList<>();
        CartCreate.LinesDTO linesDTO = new CartCreate.LinesDTO();
        linesDTO.setQuantity(1);
        linesDTO.setMerchandiseId("gid://shopify/ProductVariant/" + cartForm.getShopifyVariantId());

        if(StrUtil.isNotBlank(cartForm.getSn())){
            ArrayList<CartCreate.LinesDTO.AttributesDTO> attributes = new ArrayList<>();
            CartCreate.LinesDTO.AttributesDTO attributesDTO = new CartCreate.LinesDTO.AttributesDTO();
            attributesDTO.setKey("sn");
            attributesDTO.setValue(cartForm.getSn());
            attributes.add(attributesDTO);
            linesDTO.setAttributes(attributes);
        }

        lines.add(linesDTO);

        return checkoutToShopify(session, lines);
    }

    public AbstractEither<ErrorMessage, CartCreate> checkout(Session session, List<Long> cartIds) {
        if (cartIds.isEmpty()) {
            return Left.apply(Checkout.Errors.AT_LEAST_ONE);
        }

        List<Long> newCartIds = cartIds.stream().filter(cartId -> cartService.get(cartId).fold(errorMessage -> false, cart -> true)).collect(Collectors.toList());
        if (cartIds.size() != newCartIds.size()) {
            return Left.apply(Cart.Errors.NOT_FOUND);
        }

        List<CartCreate.LinesDTO> lines = Lists.newArrayListWithCapacity(cartIds.size());
        cartIds.forEach(id -> cartService.get(id).map(cart -> {
            CartCreate.LinesDTO linesDTO = new CartCreate.LinesDTO();
            linesDTO.setMerchandiseId(cart.getShopifyHQLVariantId());
            linesDTO.setQuantity(cart.getCount());
            linesDTO.setAttributes(new ArrayList<>(){{
                CartCreate.LinesDTO.AttributesDTO attributesDTO = new CartCreate.LinesDTO.AttributesDTO();
                attributesDTO.setKey("sn");
                attributesDTO.setValue(cart.getSn());
                add(attributesDTO);
            }});
            lines.add(linesDTO);
            return cart;
        }));
        return checkoutToShopify(session, lines).ifRight(checkout -> {
            cartService.delete(session.getUserId(), newCartIds);
        });
    }

    private AbstractEither<ErrorMessage, CartCreate> checkoutToShopify(Session session, List<CartCreate.LinesDTO> lines) {
        ShopifyGraphQLClient client = new ShopifyGraphQLClient("topdon-it.myshopify.com", "76721c798b82d8f117d677b7d5c67ba3", "2025-01");

        CartCreate cartCreate = new CartCreate();
        cartCreate.setLines(lines);
        CartCreate.CartBuyerIdentityInput buyerIdentity = new CartCreate.CartBuyerIdentityInput();
        buyerIdentity.setEmail(session.getUserCode());
        cartCreate.setBuyerIdentity(buyerIdentity);
        CartCreateResponse cartCreate1 = client.createCart(cartCreate);

        if (cartCreate1 == null) {
            return Left.apply(Checkout.Errors.VARIANT_DISABLED);
        }
        String multipassToken = SignUtil.multipassEncode(ShopifyCustomer.apply(session.getUserCode(), session.getUserId()), "e7fd39c2fa1a2f5ebea6033b64ca2ca7");
        return client.customerAccessTokenCreateWithMultipass(multipassToken).fold(Left::apply, customerAccessToken ->
                client.cartBuyerIdentityUpdate(cartCreate1.getCart().getId(), customerAccessToken.getAccessToken(),session.getUserCode()).fold(
                        Left::apply,
                        checkout1 -> Right.apply(cartCreate1.getCart())));

    }


    public AbstractEither<ErrorMessage, Checkout> applyDiscountCode(String id, String code) {
        ShopifyGraphQLClient client = shopifyGraphQLClientService.getShopifyGraphQLClient("topdon-it.myshopify.com", "76721c798b82d8f117d677b7d5c67ba3");
        return client.applyDiscountCode(id, code);
    }
}
