package com.hiwie.breeze.cache.tests;

import com.google.common.collect.Lists;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.cache.Cache;
import com.hiwie.breeze.cache.Redeemer;
import com.hiwie.breeze.cache.others.RedeemableContent;
import com.hiwie.breeze.util.StringUtil;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import redis.clients.jedis.JedisPool;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
@DisplayName("Redeemer test")
@RunWith(JUnitPlatform.class)
class RedeemerTest {

    private static Cache cache;

    private static Redeemer<RedeemableContent> redeemer;

    private static int TRIES = 1;

    @BeforeAll
    static void init() {
        JedisPool pool = new JedisPool(new GenericObjectPoolConfig(), "dev.11ejia.com", 6379, 2000, "ahuwqoiye12c7b9484c2b94", 0, "breeze_test");
        cache = new Cache(pool);
//        redeemer = new Redeemer<>("test.redeemer", 0, Duration.parse("PT5S"), TRIES, RedeemableContent.class);
    }

    @Test
    @DisplayName("standard")
    void standard() {
        String key = StringUtil.secureRandom(64, StringUtil.Encoder.HEX);

        RedeemableContent content = new RedeemableContent();
        content.setStringList(Lists.newArrayList("a", "b", "c"));
        content.setInteger(1);

        AbstractEither<ErrorMessage, String> posted = cache.apply(redeemer.post(key, content));
        assertTrue(posted.isRight());
        AbstractEither<ErrorMessage, RedeemableContent> redeemed = cache.apply(redeemer.redeem(posted.right().get()));
        String error = redeemed.fold(ErrorMessage::toString, ignore -> StringUtil.EMPTY);
        assertTrue(redeemed.isRight(), error);
        assertEquals(content, redeemed.right().get());
    }

    @Test
    @DisplayName("limit by tries")
    void limitByTries() {
        String key = StringUtil.secureRandom(64, StringUtil.Encoder.HEX);
        RedeemableContent content = new RedeemableContent();
        content.setStringList(Lists.newArrayList("a", "b", "c"));
        content.setInteger(2);
        AbstractEither<ErrorMessage, String> posted = cache.apply(redeemer.post(key, content));
        assertTrue(posted.isRight());
        for (int i = 0; i < TRIES; i++) {
            assertTrue(cache.apply(redeemer.redeem(StringUtil.join(StringUtil.repeat("a", 64), posted.right().get().substring(64)))).isLeft());
        }
        AbstractEither<ErrorMessage, RedeemableContent> redeemed = cache.apply(redeemer.redeem(posted.right().get()));
        assertTrue(redeemed.isLeft());
    }

}