package com.jeesite.modules.email.web;

import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.email.entity.EmailSubscribe;
import com.jeesite.modules.email.service.EmailSubscribeService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订阅管理Controller
 * <AUTHOR>
 * @version 2023-02-14
 */
@Controller
@RequestMapping(value = "${adminPath}/email/emailSubscribe")
public class EmailSubscribeController extends BaseController {

	@Autowired
	private EmailSubscribeService emailSubscribeService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public EmailSubscribe get(String id, boolean isNewRecord) {
		return emailSubscribeService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("email:emailSubscribe:view")
	@RequestMapping(value = {"list", ""})
	public String list(EmailSubscribe emailSubscribe, Model model) {
		model.addAttribute("emailSubscribe", emailSubscribe);
		return "modules/email/emailSubscribeList";
	}



	/**
	 * 导出用户数据
	 */
	@RequiresPermissions("email:emailSubscribe:view")
	@RequestMapping(value = "exportData")
	public void exportData(EmailSubscribe emailSubscribe, HttpServletResponse response) {
		List<EmailSubscribe> list = emailSubscribeService.findList(emailSubscribe);
		String fileName = "订阅数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("申请数据", EmailSubscribe.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}


	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("email:emailSubscribe:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<EmailSubscribe> listData(EmailSubscribe emailSubscribe, HttpServletRequest request, HttpServletResponse response) {
		emailSubscribe.setPage(new Page<>(request, response));
		Page<EmailSubscribe> page = emailSubscribeService.findPage(emailSubscribe);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("email:emailSubscribe:view")
	@RequestMapping(value = "form")
	public String form(EmailSubscribe emailSubscribe, Model model) {
		model.addAttribute("emailSubscribe", emailSubscribe);
		return "modules/email/emailSubscribeForm";
	}

	
}