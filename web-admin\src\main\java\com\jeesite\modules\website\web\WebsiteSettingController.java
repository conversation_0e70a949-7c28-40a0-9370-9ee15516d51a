package com.jeesite.modules.website.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.website.entity.WebsiteSetting;
import com.jeesite.modules.website.service.WebsiteSettingService;

/**
 * 网站配置Controller
 * <AUTHOR>
 * @version 2023-02-16
 */
@Controller
@RequestMapping(value = "${adminPath}/website/websiteSetting")
public class WebsiteSettingController extends BaseController {

	@Autowired
	private WebsiteSettingService websiteSettingService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public WebsiteSetting get(String id, boolean isNewRecord) {
		return websiteSettingService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("website:websiteSetting:view")
	@RequestMapping(value = {"list", ""})
	public String list(WebsiteSetting websiteSetting, Model model) {
		model.addAttribute("websiteSetting", websiteSetting);
		return "modules/website/websiteSettingList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("website:websiteSetting:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<WebsiteSetting> listData(WebsiteSetting websiteSetting, HttpServletRequest request, HttpServletResponse response) {
		websiteSetting.setPage(new Page<>(request, response));
		Page<WebsiteSetting> page = websiteSettingService.findPage(websiteSetting);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("website:websiteSetting:view")
	@RequestMapping(value = "form")
	public String form(WebsiteSetting websiteSetting, Model model) {
		model.addAttribute("websiteSetting", websiteSetting);
		return "modules/website/websiteSettingForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("website:websiteSetting:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated WebsiteSetting websiteSetting) {
		websiteSettingService.save(websiteSetting);
		return renderResult(Global.TRUE, text("保存配置成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("website:websiteSetting:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(WebsiteSetting websiteSetting) {
		websiteSettingService.delete(websiteSetting);
		return renderResult(Global.TRUE, text("删除配置成功！"));
	}
	
}