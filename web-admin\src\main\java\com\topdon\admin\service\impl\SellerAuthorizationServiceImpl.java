package com.topdon.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeesite.modules.sys.utils.UserUtils;
import com.topdon.admin.dto.SellerAuthViolationsDTO;
import com.topdon.admin.dto.SellerAuthorizationDTO;
import com.topdon.admin.dto.SellerAuthorizationDetailDTO;
import com.topdon.admin.dto.SellerAuthorizationStatusChangeDTO;
import com.topdon.admin.entity.*;
import com.topdon.admin.mapper.SellerAuthorizationMapper;
import com.topdon.admin.service.*;
import com.topdon.admin.vo.SellerAuthorizationExportVo;
import com.topdon.admin.vo.SellerAuthorizationVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SellerAuthorizationServiceImpl extends ServiceImpl<SellerAuthorizationMapper, SellerAuthorization> implements SellerAuthorizationService {

    @Resource
    private AuthorizedPlatformsService authorizedPlatformsService;
    @Resource
    private SellerAuthProductLinesMappingService sellerAuthProductLinesMappingService;
    @Resource
    private RegionServiceV1 regionServiceV1;
    @Resource
    private SellerAuthRegionMappingService sellerAuthRegionMappingService;
    @Resource
    private ProductLinesService productLinesService;
    @Resource
    private SellerAuthViolationsService sellerAuthViolationsService;

    private void getProductLinesName(Integer id, List<String> names) {
        ProductLines productLines = productLinesService.getById(id);
        if (productLines == null) {
            return;
        }
        names.add(0,productLines.getName());
        if (productLines.getParentId() != null && productLines.getParentId() != 0) {
            getProductLinesName(productLines.getParentId(), names);
        }
    }

    private List<SellerAuthorizationVo> appendData(List<SellerAuthorizationVo> sellerAuthorizationVos){
        List<SellerAuthorizationVo> list = new ArrayList<>();

        for (SellerAuthorizationVo record : sellerAuthorizationVos) {
            record.setSellerAuthProductLinesMappings(
                    sellerAuthProductLinesMappingService.lambdaQuery()
                            .eq(SellerAuthProductLinesMapping::getSellerAuthId, record.getId())
                            .list()
            );
            String region = sellerAuthRegionMappingService.lambdaQuery()
                    .eq(SellerAuthRegionMapping::getSellerAuthId, record.getId())
                    .list()
                    .stream()
                    .map(item -> regionServiceV1.getById(item.getRegionCode()))
                    .filter(Objects::nonNull)
                    .map(Region::getName)
                    .collect(Collectors.joining(", "));
            record.setRegion(region);
            String productLines = record.getSellerAuthProductLinesMappings().stream()
                    .map(item -> {
                        ArrayList<String> names = new ArrayList<>();
                        getProductLinesName(item.getProductLinesId(), names);
                        return String.join("/", names);
                    }).collect(Collectors.joining(", "));
            record.setProductLineName(productLines);

            List<AuthorizedPlatforms> authorizedPlatforms = authorizedPlatformsService.lambdaQuery()
                    .eq(AuthorizedPlatforms::getSellerAuthId, record.getId())
                    .list();
            if (!authorizedPlatforms.isEmpty()) {
                for (AuthorizedPlatforms authorizedPlatform : authorizedPlatforms) {
                    SellerAuthorizationVo target = new SellerAuthorizationVo();
                    BeanUtils.copyProperties(record, target);
                    target.setAuthPlatform(authorizedPlatform.getAuthPlatform());
                    target.setStoreLink(authorizedPlatform.getStoreLink());
                    target.setStoreName(authorizedPlatform.getStoreName());
                    list.add(target);
                }
            } else {
                list.add(record);
            }
        }

        return list;
    }

    @Override
    public IPage<SellerAuthorizationVo> getPage(SellerAuthorizationDTO sellerAuthorizationDTO) {
        IPage<SellerAuthorizationVo> page = this.baseMapper.getPage(new PageDTO<>(sellerAuthorizationDTO.getCurrent(), sellerAuthorizationDTO.getSize()), sellerAuthorizationDTO);
        List<SellerAuthorizationVo> list = appendData(page.getRecords());

        if (!list.isEmpty()) {
            page.getRecords().clear();
            page.getRecords().addAll(list);
        }
        return page;
    }

    @Override
    public SellerAuthorizationDetailDTO detail(Integer id) {
        SellerAuthorization authorization = this.getById(id);
        if (authorization == null) {
            return null;
        }

        SellerAuthorizationDetailDTO sellerAuthorizationDetailDTO = new SellerAuthorizationDetailDTO();
        BeanUtils.copyProperties(authorization, sellerAuthorizationDetailDTO);

        List<SellerAuthRegionMapping> list = sellerAuthRegionMappingService
                .lambdaQuery().eq(SellerAuthRegionMapping::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                .list();
        if (!list.isEmpty()) {
            sellerAuthorizationDetailDTO.setSellerAuthRegionMappings(list);
            sellerAuthorizationDetailDTO.setRegions(
                    regionServiceV1.lambdaQuery()
                            .in(Region::getCode, list.stream().map(SellerAuthRegionMapping::getRegionCode).collect(Collectors.toList()))
                            .list()
            );
        }

        List<SellerAuthProductLinesMapping> list1 = sellerAuthProductLinesMappingService
                .lambdaQuery().eq(SellerAuthProductLinesMapping::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                .list();
        if (!list1.isEmpty()) {
            sellerAuthorizationDetailDTO.setSellerAuthProductLinesMappings(list1);
            sellerAuthorizationDetailDTO.setProductLines(
                    productLinesService.listByIds(list1.stream().map(SellerAuthProductLinesMapping::getProductLinesId).collect(Collectors.toList()))
            );
        }

        sellerAuthorizationDetailDTO.setSellerAuthViolations(
                sellerAuthViolationsService.lambdaQuery().eq(SellerAuthViolations::getSellerAuthId, sellerAuthorizationDetailDTO.getId()).list()
                        .stream()
                        .map(item->{
                            SellerAuthViolationsDTO sellerAuthViolationsDTO = new SellerAuthViolationsDTO();
                            BeanUtils.copyProperties(item,sellerAuthViolationsDTO);
                            return sellerAuthViolationsDTO;
                        }).collect(Collectors.toList())
        );

        sellerAuthorizationDetailDTO.setAuthorizedPlatforms(
                authorizedPlatformsService.lambdaQuery()
                        .eq(AuthorizedPlatforms::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                        .list()
        );

        return sellerAuthorizationDetailDTO;
    }

    @Override
    public List<SellerAuthorizationExportVo> exportData(SellerAuthorizationDTO sellerAuthorizationDTO) {
        List<SellerAuthorizationVo> list = this.baseMapper.getList(sellerAuthorizationDTO);
        List<SellerAuthorizationVo> list1 = appendData(list);

        return list1.stream()
                .map(item -> {
                    SellerAuthorizationExportVo sellerAuthorizationExportVo = new SellerAuthorizationExportVo();
                    BeanUtils.copyProperties(item, sellerAuthorizationExportVo);
                    return sellerAuthorizationExportVo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void changeStatus(SellerAuthorizationStatusChangeDTO sellerAuthorizationStatusChangeDTO) {
        this.lambdaUpdate()
                .in(SellerAuthorization::getAuthCertNo,sellerAuthorizationStatusChangeDTO.getAuthCertNoList())
                .set(SellerAuthorization::getStatus,sellerAuthorizationStatusChangeDTO.getStatus())
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdate(SellerAuthorization entity) {
        SellerAuthorizationDetailDTO sellerAuthorizationDetailDTO = (SellerAuthorizationDetailDTO) entity;

        if (sellerAuthorizationDetailDTO.getId() != null) {
            sellerAuthProductLinesMappingService.lambdaUpdate()
                    .eq(SellerAuthProductLinesMapping::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                    .remove();
            sellerAuthRegionMappingService.lambdaUpdate()
                    .eq(SellerAuthRegionMapping::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                    .remove();
            List<Integer> authPlatformsIds = sellerAuthorizationDetailDTO.getAuthorizedPlatforms().stream()
                    .map(AuthorizedPlatforms::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!authPlatformsIds.isEmpty()) {
                authorizedPlatformsService.lambdaUpdate()
                        .eq(AuthorizedPlatforms::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                        .notIn(AuthorizedPlatforms::getId, authPlatformsIds)
                        .remove();
            }
            List<Integer> authViolationsIds = sellerAuthorizationDetailDTO.getSellerAuthViolations().stream()
                    .map(SellerAuthViolations::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!authViolationsIds.isEmpty()) {
                sellerAuthViolationsService.lambdaUpdate()
                        .eq(SellerAuthViolations::getSellerAuthId, sellerAuthorizationDetailDTO.getId())
                        .notIn(SellerAuthViolations::getId, authViolationsIds)
                        .remove();
            }
            entity.setUpdateDate(new Date());
            entity.setUpdateBy(UserUtils.getLoginInfo().getId());
        } else {
            String year = String.valueOf(DateUtil.thisYear()).substring(2, 4);
            for (int i = 0; i < 10; i++) {
                if (i == 9) {
                    throw new RuntimeException("生成证书编号出错，请稍后再试");
                }
                String randomNumbers = RandomUtil.randomNumbers(8);
                String certNo = "TP" + year + randomNumbers;
                if (this.lambdaQuery()
                        .eq(SellerAuthorization::getAuthCertNo, certNo)
                        .count() > 0) {
                    continue;
                }
                entity.setAuthCertNo(certNo);
                break;
            }

            entity.setCreateDate(new Date());
            entity.setCreateBy(UserUtils.getLoginInfo().getId());
        }

        boolean b = super.saveOrUpdate(entity);

        Integer id = entity.getId();
        List<SellerAuthProductLinesMapping> productLinesMappingList = sellerAuthorizationDetailDTO.getProductLinesList()
                .stream()
                .map(item ->
                        new SellerAuthProductLinesMapping(null, id, item)
                ).collect(Collectors.toList());
        if (!productLinesMappingList.isEmpty()) {
            sellerAuthProductLinesMappingService.saveBatch(productLinesMappingList);
        }

        if (CollUtil.isNotEmpty(sellerAuthorizationDetailDTO.getRegionList())) {
            List<SellerAuthRegionMapping> regionMappingList = sellerAuthorizationDetailDTO.getRegionList()
                    .stream()
                    .map(item -> new SellerAuthRegionMapping(null, id, item)).collect(Collectors.toList());
            if (!regionMappingList.isEmpty()) {
                sellerAuthRegionMappingService.saveBatch(regionMappingList);
            }
        }

        for (AuthorizedPlatforms authorizedPlatform : sellerAuthorizationDetailDTO.getAuthorizedPlatforms()) {
            authorizedPlatform.setSellerAuthId(id);
            authorizedPlatformsService.saveOrUpdate(authorizedPlatform);
        }
        for (SellerAuthViolations sellerAuthViolations : sellerAuthorizationDetailDTO.getSellerAuthViolations()) {
            sellerAuthViolations.setSellerAuthId(id);
            sellerAuthViolationsService.saveOrUpdate(sellerAuthViolations);
        }

        return b;
    }
}
