package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.model.Region;
import com.topdon.website.repositories.RegionRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class RegionService {

    private final RegionRepository regionRepository;

    @Inject
    public RegionService(RegionRepository regionRepository) {
        this.regionRepository = regionRepository;
    }

    public AbstractEither<ErrorMessage, List<Region>> list(String parentId) {
        return Right.apply(regionRepository.list(parentId));
    }

    public AbstractEither<ErrorMessage, Region> get(String id) {
        return regionRepository.get(id).toRight(Region.Errors.NOT_FOUND);
    }
}
