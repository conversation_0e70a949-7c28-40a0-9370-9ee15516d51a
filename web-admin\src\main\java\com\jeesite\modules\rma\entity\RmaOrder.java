package com.jeesite.modules.rma.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.product.entity.ProductExtensionDetail;

/**
 * RMA工单Entity
 * <AUTHOR>
 * @version 2024-07-10
 */
@Table(name="rma_order", alias="a", label="RMA工单信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="ticket_number", attrName="ticketNumber", label="ticket_number"),
		@Column(name="ticket_id", attrName="ticketId", label="user_id"),
		@Column(name="user_id", attrName="userId", label="user_id"),
		@Column(name="sn", attrName="sn", label="sn"),
		@Column(name="product_id", attrName="productId", label="product_id", isUpdateForce=true),
		@Column(name="product_name", attrName="productName", label="product_name", queryType=QueryType.LIKE),
		@Column(name="issue_type", attrName="issueType", label="issue_type"),
		@Column(name="classification_id", attrName="classificationId", label="classification_id"),
		@Column(name="description", attrName="description", label="description"),
		@Column(name="order_no", attrName="orderNo", label="order_no"),
		@Column(name="country", attrName="country", label="country"),
		@Column(name="state_region", attrName="stateRegion", label="state_region"),
		@Column(name="city", attrName="city", label="city"),
		@Column(name="address1", attrName="address1", label="address1"),
		@Column(name="postal_code", attrName="postalCode", label="postal_code"),
		@Column(name="seller_name", attrName="sellerName", label="seller_name", queryType=QueryType.LIKE),
		@Column(name="channel", attrName="channel", label="channel"),
		@Column(name="platform", attrName="platform", label="platform"),
		@Column(name="tui_hui_gen_zong_hao", attrName="tuiHuiGenZongHao", label="tui_hui_gen_zong_hao"),
		@Column(name="huo_wu_liu_dan_hao", attrName="huoWuLiuDanHao", label="huo_wu_liu_dan_hao"),
		@Column(name="chu_li_fang_an", attrName="chuLiFangAn", label="chu_li_fang_an"),
		@Column(name="send_zoho", attrName="sendZoho", label="send_zoho"),
		@Column(name="create_time", attrName="createTime", label="create_time", isUpdateForce=true),
		@Column(name="jing_xiao_shang_ming_cheng", attrName="jingXiaoShangMingCheng", label="jing_xiao_shang_ming_cheng"),
		@Column(name="update_time", attrName="updateTime", label="update_time", isUpdateForce=true),
		@Column(name="email", attrName="email", label="email"),
		@Column(name="solution", attrName="solution", label="solution"),
		@Column(name="ticket_status", attrName="ticketStatus", label="ticket_status"),
	}, orderBy="a.id DESC"
)
public class RmaOrder extends DataEntity<RmaOrder> {
	
	private static final long serialVersionUID = 1L;
	private String ticketNumber;		// ticket_number
	private String ticketId;		// ticket_number
	private String userId;		// user_id
	private String sn;		// sn
	private Long productId;		// product_id
	private String productName;		// product_name
	private String issueType;		// issue_type
	private String classificationId;		// classification_id
	private String description;		// description
	private String orderNo;		// order_no
	private String country;		// country
	private String stateRegion;		// state_region
	private String city;		// city
	private String address1;		// address1
	private String postalCode;		// postal_code
	private String sellerName;		// seller_name
	private String channel;		// channel
	private String platform;		// platform
	private String tuiHuiGenZongHao;		// tui_hui_gen_zong_hao
	private String huoWuLiuDanHao;		// huo_wu_liu_dan_hao
	private String chuLiFangAn;		// chu_li_fang_an
	private String sendZoho;		// send_zoho
	private Date createTime;		// create_time
	private String jingXiaoShangMingCheng;		// jing_xiao_shang_ming_cheng
	private Date updateTime;		// update_time
	private String email;		// email
	private String solution;		// solution
	private String ticketStatus;

	private List<RmaOrderStatusLog> params = ListUtils.newLinkedList();
	
	public RmaOrder() {
		this(null);
	}

	public RmaOrder(String id){
		super(id);
	}

	@Size(min=0, max=60, message="ticket_number长度不能超过 60 个字符")
	public String getTicketNumber() {
		return ticketNumber;
	}

	public void setTicketNumber(String ticketNumber) {
		this.ticketNumber = ticketNumber;
	}

	@Size(min=0, max=30, message="ticket_number长度不能超过 30 个字符")
	public String getTicketId() {
		return ticketId;
	}

	public void setTicketId(String ticketId) {
		this.ticketId = ticketId;
	}

	@Size(min=0, max=60, message="user_id长度不能超过 60 个字符")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	@Size(min=0, max=60, message="sn长度不能超过 60 个字符")
	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}
	
	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}
	
	@Size(min=0, max=128, message="product_name长度不能超过 128 个字符")
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	@Size(min=0, max=30, message="issue_type长度不能超过 30 个字符")
	public String getIssueType() {
		return issueType;
	}

	public void setIssueType(String issueType) {
		this.issueType = issueType;
	}
	
	@Size(min=0, max=50, message="classification_id长度不能超过 50 个字符")
	public String getClassificationId() {
		return classificationId;
	}

	public void setClassificationId(String classificationId) {
		this.classificationId = classificationId;
	}
	
	@Size(min=0, max=500, message="description长度不能超过 500 个字符")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
	@Size(min=0, max=64, message="order_no长度不能超过 64 个字符")
	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	
	@Size(min=0, max=20, message="country长度不能超过 20 个字符")
	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}
	
	@Size(min=0, max=50, message="state_region长度不能超过 50 个字符")
	public String getStateRegion() {
		return stateRegion;
	}

	public void setStateRegion(String stateRegion) {
		this.stateRegion = stateRegion;
	}
	
	@Size(min=0, max=50, message="city长度不能超过 50 个字符")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}
	
	@Size(min=0, max=200, message="address1长度不能超过 200 个字符")
	public String getAddress1() {
		return address1;
	}

	public void setAddress1(String address1) {
		this.address1 = address1;
	}
	
	@Size(min=0, max=20, message="postal_code长度不能超过 20 个字符")
	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}
	
	@Size(min=0, max=64, message="seller_name长度不能超过 64 个字符")
	public String getSellerName() {
		return sellerName;
	}

	public void setSellerName(String sellerName) {
		this.sellerName = sellerName;
	}
	
	@Size(min=0, max=20, message="channel长度不能超过 20 个字符")
	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	@Size(min=0, max=20, message="platform长度不能超过 20 个字符")
	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}
	
	@Size(min=0, max=36, message="tui_hui_gen_zong_hao长度不能超过 36 个字符")
	public String getTuiHuiGenZongHao() {
		return tuiHuiGenZongHao;
	}

	public void setTuiHuiGenZongHao(String tuiHuiGenZongHao) {
		this.tuiHuiGenZongHao = tuiHuiGenZongHao;
	}
	
	@Size(min=0, max=36, message="huo_wu_liu_dan_hao长度不能超过 36 个字符")
	public String getHuoWuLiuDanHao() {
		return huoWuLiuDanHao;
	}

	public void setHuoWuLiuDanHao(String huoWuLiuDanHao) {
		this.huoWuLiuDanHao = huoWuLiuDanHao;
	}
	
	@Size(min=0, max=36, message="chu_li_fang_an长度不能超过 36 个字符")
	public String getChuLiFangAn() {
		return chuLiFangAn;
	}

	public void setChuLiFangAn(String chuLiFangAn) {
		this.chuLiFangAn = chuLiFangAn;
	}
	
	@Size(min=0, max=1, message="send_zoho长度不能超过 1 个字符")
	public String getSendZoho() {
		return sendZoho;
	}

	public void setSendZoho(String sendZoho) {
		this.sendZoho = sendZoho;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	@Size(min=0, max=200, message="jing_xiao_shang_ming_cheng长度不能超过 200 个字符")
	public String getJingXiaoShangMingCheng() {
		return jingXiaoShangMingCheng;
	}

	public void setJingXiaoShangMingCheng(String jingXiaoShangMingCheng) {
		this.jingXiaoShangMingCheng = jingXiaoShangMingCheng;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	@Size(min=0, max=50, message="email长度不能超过 50 个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	@Size(min=0, max=500, message="solution长度不能超过 500 个字符")
	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	public String getTicketStatus() {
		return ticketStatus;
	}

	public void setTicketStatus(String ticketStatus) {
		this.ticketStatus = ticketStatus;
	}

	public List<RmaOrderStatusLog> getParams() {
		return params;
	}

	public void setParams(List<RmaOrderStatusLog> params) {
		this.params = params;
	}
}