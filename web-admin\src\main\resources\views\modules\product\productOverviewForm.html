<% layout('/layouts/default.html', {title: '产品详情管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(productOverview.isNewRecord ? '新增产品详情' : '编辑产品详情')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${productOverview}" action="${ctx}/product/productOverview/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('产品')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="product" title="产品选择" path="product.id" labelPath="product.name"
								url="${ctx}/product/product/select" allowClear="false"
								checkbox="false" class="required" itemCode="id" itemName="name"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('显示类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="type" dictType="overview_type" blankOption="true" class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="desc" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('图片')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadImage" bizKey="${productOverview.id}" bizType="productOverview_image"
								uploadType="image" class="" readonly="false" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="image"/>
								<#form:hidden path="image" maxlength="1024" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="title" maxlength="56" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('背景色')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="background" maxlength="50" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('标题色')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="titleColor" maxlength="25" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('描述颜色')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="descColor" maxlength="25" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('product:productOverview:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>