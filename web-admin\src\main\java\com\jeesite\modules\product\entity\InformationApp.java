package com.jeesite.modules.product.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * 产品资料管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Data
public class InformationApp implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String download;
    private String uploadId;
    private String icon;
    private String version;
    private String platform;
    private String status;

    @JsonIgnore
    private String information_app_icon__del;

    @JsonIgnore
    private String information_app_file__del;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getUploadId() {
        return uploadId;
    }

    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInformation_app_icon__del() {
        return information_app_icon__del;
    }

    public void setInformation_app_icon__del(String information_app_icon__del) {
        this.information_app_icon__del = information_app_icon__del;
    }

    public String getInformation_app_file__del() {
        return this.information_app_file__del;
    }

    public void setInformation_app_file__del(String information_app_file__del) {
        this.information_app_file__del = information_app_file__del;
    }
}