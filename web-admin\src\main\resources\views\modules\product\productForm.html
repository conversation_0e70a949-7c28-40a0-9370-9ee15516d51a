<% layout('/layouts/default.html', {title: '产品管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(product.isNewRecord ? '新增产品' : '编辑产品')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${product}" action="${ctx}/product/product/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('产品名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="40" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="description" maxlength="1024" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('分类')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="classifications" title="${text('分类')}"
								value="${@ListUtils.extractToString(product.classifications!, 'code', ',')}"
								labelValue="${@ListUtils.extractToString(product.classifications!, 'name', ',')}"
								url="${ctx}/classification/classification/treeData"
								class="" allowClear="true" canSelectRoot="true" canSelectParent="false" checkbox="true" />
								<#form:hidden name="classificationJson"/>
							</div>
						</div>
					</div>

				</div>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('排序')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sort" maxlength="40" class="form-control digits"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('新品')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="newProduct" dictType="sys_yes_no" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('停产')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="discontinued" dictType="sys_yes_no" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('搜索结果页展示')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="searchView" dictType="sys_yes_no" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('RMA工具展示')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="rmaToolShow" dictType="sys_yes_no" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('允许购买')}：<i class="fa icon-question hide"></i>
							</label>
							<div class="col-sm-8">
								<#form:select path="allowPurchase" dictType="sys_yes_no" class="form-control" defaultValue="0"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-12 ml10 mr10" style="text-align: left">
								<span class="required hide">*</span> ${text('搜索结果页封面')}：
							</label>
							<div class="col-sm-12">
								<#form:fileupload id="uploadSearchImage" bizKey="${product.id}" bizType="product_search_image"
								uploadType="image" class="" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="searchCover"/>
								<#form:hidden path="searchCover" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-12 ml10 mr10" style="text-align: left;">
								<span class="required hide">*</span> ${text('产品列表封面')}：
							</label>
							<div class="col-sm-12">
								<#form:fileupload id="uploadMenuImage" bizKey="${product.id}" bizType="product_cover_image"
								uploadType="image" class="" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="cover"/>
								<#form:hidden path="cover" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-12 ml10 mr10" style="text-align: left;padding: 5px">
								<span class="required hide">*</span> ${text('移动端展示封面')}：
							</label>
							<div class="col-sm-12">
								<#form:fileupload id="uploadMobileImage" bizKey="${product.id}" bizType="product_mobile_cover_image"
								uploadType="image" class="" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="mobileCover"/>
								<#form:hidden path="mobileCover" maxlength="256" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('子站点跳转链接')}</div>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('US官网')}：</label>
							<div class="col-sm-8">
								<#form:input path="urlUs" maxlength="512" class="form-control" placeholder="https://www.topdon.us"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('EU官网')}：</label>
							<div class="col-sm-8">
								<#form:input path="urlEu" maxlength="512" class="form-control" placeholder="https://eu.topdon.com"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('AU官网')}：</label>
							<div class="col-sm-8">
								<#form:input path="urlAu" maxlength="512" class="form-control" placeholder="https://au.topdon.com"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('product:product:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	function toggleWebsiteInputs() {
		var allowPurchase = $("select[name='allowPurchase']").val();
		var disabled = allowPurchase === "0"; // "0"表示“否”
		$("input[name='urlUs'], input[name='urlEu'], input[name='urlAu']").prop("disabled", disabled);
	}

	$(function() {
		// 初始化设置一次
		toggleWebsiteInputs();

		// 监听 select 的变化
		$("select[name='allowPurchase']").on("change", function() {
			toggleWebsiteInputs();
		});
	});

$("#inputForm").validate({
	submitHandler: function(form){
		var classificationsCode = $('#classificationsCode').val().split(',');
		$("#classificationJson").val(JSON.stringify(classificationsCode));
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>