package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.topdon.website.form.SubscribeForm;
import com.topdon.website.model.Subscribe;
import com.topdon.website.repositories.EmailSubscribeRepository;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class EmailSubscribeService {
    private final EmailSubscribeRepository emailSubscribeRepository;

    @Inject
    public EmailSubscribeService(EmailSubscribeRepository emailSubscribeRepository) {
        this.emailSubscribeRepository = emailSubscribeRepository;
    }

    public AbstractEither<ErrorMessage, Long> create(SubscribeForm form) {
        if (form.getEmail() == null || form.getEmail().trim().isEmpty()) {
            return Left.apply(Subscribe.Errors.EMPTY_EMAIL);
        }
        if (!emailSubscribeRepository.isExist(form)) {
            return Right.apply(emailSubscribeRepository.create(form));
        }
        return Left.apply(Subscribe.Errors.EXISTS);
    }

    public AbstractEither<ErrorMessage, Integer> copiedDiscountCode(SubscribeForm form) {
        return Right.apply(emailSubscribeRepository.copiedDiscountCode(form));
    }
}
