package com.hiwie.breeze.cache;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.None;
import com.hiwie.breeze.Some;
import com.hiwie.breeze.util.StringUtil;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;

import java.time.Duration;
import java.util.function.Function;

/**
 * Counter
 *
 * <AUTHOR>
 */
public class Counter extends AbstractCache {

    private final static ErrorMessage ERROR_COUNTER_LIMIT_REACHED = new ErrorMessage("CACHE", "COUNTER", "LIMIT_REACHED");
    private final boolean refreshExpireTimeAfterUpdate;
    private final long NO_TIMER = -1L;
    private final long limit;

    public Counter(String prefix, int db, AbstractOption<Duration> expireDuration, boolean refreshExpireTimeAfterUpdate, long limit) {
        super(StringUtil.join("COUNTER", PREFIX_SEPARATOR, prefix), db, expireDuration);
        this.refreshExpireTimeAfterUpdate = refreshExpireTimeAfterUpdate;
        this.limit = limit;
    }

    public Function<Jedis, AbstractOption<ErrorMessage>> isLimitReached(String key) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            Response<String> response = pipeline.get(theKey);
            pipeline.sync();
            long dbCounts;
            if (StringUtil.isEmpty(response.get())) {
                dbCounts = 0L;
            } else {
                dbCounts = Long.parseLong(response.get());
            }
            if (limit > 0 && dbCounts >= limit) {
                jedis.del(theKey);
                return Some.apply(ERROR_COUNTER_LIMIT_REACHED);
            }
            return None.apply();
        };
    }



    public Function<Jedis,String> set(String key,String value) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            Response<String> response = pipeline.set(theKey,value);
            pipeline.sync();
            if (StringUtil.isEmpty(response.get())) {
                return "ERROR";
            }
            return response.get();
        };
    }

    public Function<Jedis, Long> get(String key) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            Response<String> response = pipeline.get(theKey);
            pipeline.sync();
            if (StringUtil.isEmpty(response.get())) {
                return 0L;
            }
            return Long.parseLong(response.get());
        };
    }

    private Function<Jedis, Long> exec(String key, Command command) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            Response<Long> countResponse;
            if (command == Command.decrease) {
                countResponse = pipeline.decr(theKey);
            } else if (command == Command.increase) {
                countResponse = pipeline.incr(theKey);
            } else {
                throw new IllegalArgumentException("unknown counter command detected.");
            }
            if (refreshExpireTimeAfterUpdate && expireSeconds > 0) {
                pipeline.expire(theKey, expireSeconds);
            }
            Response<Long> ttlResponse = pipeline.ttl(theKey);
            pipeline.sync();
            if (ttlResponse.get() == NO_TIMER && expireSeconds > 0) {
                jedis.expire(theKey, expireSeconds);
            }
            return countResponse.get();
        };
    }

    public Function<Jedis, Long> increase(String key) {
        return exec(key, Command.increase);
    }

    public Function<Jedis, Long> decrease(String key) {
        return exec(key, Command.decrease);
    }

    private enum Command {
        /**
         * 加减 1
         */
        increase, decrease
    }

}