package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductRelation;
import com.jeesite.modules.product.service.ProductRelationService;

/**
 * 产品推荐管理Controller
 * <AUTHOR>
 * @version 2022-04-26
 */
@Controller
@RequestMapping(value = "${adminPath}/product/productRelation")
public class ProductRelationController extends BaseController {

	@Autowired
	private ProductRelationService productRelationService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ProductRelation get(String id, boolean isNewRecord) {
		return productRelationService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("product:productRelation:view")
	@RequestMapping(value = {"list", ""})
	public String list(ProductRelation productRelation, Model model) {
		model.addAttribute("productRelation", productRelation);
		return "modules/product/productRelationList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("product:productRelation:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ProductRelation> listData(ProductRelation productRelation, HttpServletRequest request, HttpServletResponse response) {
		productRelation.setPage(new Page<>(request, response));
		Page<ProductRelation> page = productRelationService.findPage(productRelation);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("product:productRelation:view")
	@RequestMapping(value = "form")
	public String form(ProductRelation productRelation, Model model) {
		model.addAttribute("productRelation", productRelation);
		return "modules/product/productRelationForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("product:productRelation:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ProductRelation productRelation) {
		productRelationService.save(productRelation);
		return renderResult(Global.TRUE, text("保存产品推荐成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("product:productRelation:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ProductRelation productRelation) {
		productRelationService.delete(productRelation);
		return renderResult(Global.TRUE, text("删除产品推荐成功！"));
	}
	
}