package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.security.forms.SessionForm;
import com.hiwie.security.services.SessionService;
import com.topdon.website.form.TopdonUserQuickLoginForm;
import com.topdon.website.form.TopdonUserSigninForm;
import com.topdon.website.helper.RequestUtil;
import com.topdon.website.helper.SignUtil;
import com.topdon.website.model.SecurityInfo;
import com.topdon.website.model.ShopifyCustomer;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class TopdonUserService {
    private final SessionService sessionService;

    @Inject
    public TopdonUserService(SessionService sessionService) {
        this.sessionService = sessionService;
    }

    /**
     * 处理用户登录请求。
     *
     * @param signinForm  登录表单数据
     * @param sessionForm 会话表单数据
     * @return AbstractEither<ErrorMessage, SecurityInfo> 包含登录结果的Either对象
     */
    public AbstractEither<ErrorMessage, SecurityInfo> signin(TopdonUserSigninForm signinForm, SessionForm sessionForm) {
        return RequestUtil.signin(signinForm.getUsername(), signinForm.getPassword(), signinForm.getApp()).fold(
                Left::apply,
                topdonResponse -> sessionService.create(topdonResponse.getUser_id(), signinForm.getUsername(), sessionForm).map(sid -> {
                    // 根据应用类型设置密钥
                    String encode = "0f15637f47c2b35e4f8dbbc1fe1c0ecc";
                    if ("global".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "e7fd39c2fa1a2f5ebea6033b64ca2ca7";
                    }
                    if ("evc".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "928261380e6f99d1cff007ce2c9deb1f";
                    }
                    if ("la".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "ef3dae95bd749fa49d47c2e026e0f8fe";
                    } else if ("au".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "00d6dcb1574ddf9d9bbb91671c10d9eb";
                    }
                    // 生成多重密码令牌
                    String multipassToken = SignUtil.multipassEncode(ShopifyCustomer.apply(signinForm.getUsername(), topdonResponse.getUser_id(), signinForm.getRedirectUrl()), encode);
                    SecurityInfo info = new SecurityInfo();
                    info.setMultipassToken(multipassToken);
                    info.setRedirectUrl("https://www.topdon.com/account/login/multipass/" + multipassToken);
                    info.setTokenType(topdonResponse.getToken_type());
                    info.setAccessToken(topdonResponse.getAccess_token());
                    info.setRefreshToken(topdonResponse.getRefresh_token());
                    info.setSid(sid);
                    return info;
                }));
    }


    public AbstractEither<ErrorMessage, SecurityInfo> quickLogin(TopdonUserQuickLoginForm signinForm, SessionForm sessionForm) {
        return RequestUtil.quickLogin(signinForm.getCode(), signinForm.getEmail(), signinForm.getApp())
                .fold(Left::apply, topdonQuickLogin -> sessionService.create(topdonQuickLogin.getUserId(), signinForm.getEmail(), sessionForm).map(sid -> {
                    String encode = "0f15637f47c2b35e4f8dbbc1fe1c0ecc";
                    if ("global".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "e7fd39c2fa1a2f5ebea6033b64ca2ca7";
                    }
                    if ("evc".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "928261380e6f99d1cff007ce2c9deb1f";
                    }
                    if ("la".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "ef3dae95bd749fa49d47c2e026e0f8fe";
                    } else if ("au".equalsIgnoreCase(signinForm.getApp())) {
                        encode = "00d6dcb1574ddf9d9bbb91671c10d9eb";
                    }
                    String multipassToken = SignUtil.multipassEncode(ShopifyCustomer.apply(signinForm.getEmail(), topdonQuickLogin.getUserId(), signinForm.getRedirectUrl()), encode);
                    SecurityInfo info = new SecurityInfo();
                    info.setMultipassToken(multipassToken);
                    info.setRedirectUrl("https://www.topdon.com/account/login/multipass/" + multipassToken);
                    info.setTokenType("bearer");
                    info.setAccessToken(topdonQuickLogin.getToken());
                    info.setSid(sid);
                    info.setRefreshToken(topdonQuickLogin.getRefreshToken());
                    info.setExpiresIn(topdonQuickLogin.getExpiresIn());
                    info.setUserId(topdonQuickLogin.getUserId());
                    return info;
                }));
    }
}
