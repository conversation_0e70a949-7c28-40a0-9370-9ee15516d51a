package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductClassification;
import com.jeesite.modules.product.service.ProductClassificationService;

/**
 * 产品分类信息Controller
 * <AUTHOR>
 * @version 2024-05-13
 */
@Controller
@RequestMapping(value = "${adminPath}/product/productClassification")
public class ProductClassificationController extends BaseController {

	@Autowired
	private ProductClassificationService productClassificationService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ProductClassification get(String id, boolean isNewRecord) {
		return productClassificationService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("product:productClassification:view")
	@RequestMapping(value = {"list", ""})
	public String list(ProductClassification productClassification, Model model) {
		model.addAttribute("productClassification", productClassification);
		return "modules/product/productClassificationList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("product:productClassification:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ProductClassification> listData(ProductClassification productClassification, HttpServletRequest request, HttpServletResponse response) {
		productClassification.setPage(new Page<>(request, response));
		Page<ProductClassification> page = productClassificationService.findPage(productClassification);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("product:productClassification:view")
	@RequestMapping(value = "form")
	public String form(ProductClassification productClassification, Model model) {
		model.addAttribute("productClassification", productClassification);
		return "modules/product/productClassificationForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("product:productClassification:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ProductClassification productClassification) {
		productClassificationService.save(productClassification);
		return renderResult(Global.TRUE, text("保存产品分类信息成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("product:productClassification:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ProductClassification productClassification) {
		productClassificationService.delete(productClassification);
		return renderResult(Global.TRUE, text("删除产品分类信息成功！"));
	}
	
}