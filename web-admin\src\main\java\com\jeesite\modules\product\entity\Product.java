package com.jeesite.modules.product.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mapper.JsonMapper;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.classification.entity.Classification;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 产品管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Table(name = "product", alias = "a", label = "产品信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "name", attrName = "name", label = "产品名称", queryType = QueryType.LIKE),
        @Column(name = "new_product", attrName = "newProduct", label = "新否新品"),
        @Column(name = "discontinued", attrName = "discontinued", label = "是否停产"),
        @Column(name = "rma_tool_show", attrName = "rmaToolShow", label = "RMA工具展示"),
        @Column(name = "description", attrName = "description", label = "描述", isQuery = false),
        @Column(name = "search_cover", attrName = "searchCover", label = "搜索结果页封面", isQuery = false),
        @Column(name = "mobile_cover", attrName = "mobileCover", label = "移动端封面", isQuery = false),
        @Column(name = "cover", attrName = "cover", label = "产品列表封面", isQuery = false),
        @Column(name = "search_view", attrName = "searchView", label = "是否在搜索结果页中展示", isQuery = false),
        @Column(name = "sort", attrName = "sort", label = "排序", isQuery = false),
        @Column(name = "create_at", attrName = "createDate", label = "创建时间", isQuery = false, isUpdateForce = true),
        @Column(name = "allow_purchase", attrName = "allowPurchase", label = "允许购买"),
        @Column(name = "url_us", attrName = "urlUs", label = "US官网"),
        @Column(name = "url_eu", attrName = "urlEu", label = "EU官网"),
        @Column(name = "url_au", attrName = "urlAu", label = "AU官网"),
}, orderBy = "a.sort ASC"
)
public class Product extends DataEntity<Product> {

    private static final long serialVersionUID = 1L;
    private String name;        // 产品名称
    private Integer newProduct;
    private Integer discontinued;
    private Integer rmaToolShow;
    private String description;        // 描述
    private Integer sort;
    private List<Classification> classifications;        // 分类
    private String searchCover;        // 搜索结果页封面
    private String mobileCover;        // 移动端封面
    private String cover;        // 产品列表封面
    private Integer searchView;        // 是否在搜索结果页中展示
    private Date createAt;        // 创建时间
    private Integer allowPurchase; // 允许购买
    private String urlUs; // US 官网
    private String urlEu; // EU 官网
    private String urlAu; // AU 官网


    private List<ProductClassification> productClassifications = Lists.newArrayList();

    public Product() {
        this(null);
    }

    public Product(String id) {
        super(id);
    }

    @Size(min = 0, max = 40, message = "产品名称长度不能超过 40 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(Integer newProduct) {
        this.newProduct = newProduct;
    }

    public Integer getDiscontinued() {
        return discontinued;
    }

    public void setDiscontinued(Integer discontinued) {
        this.discontinued = discontinued;
    }

    public Integer getRmaToolShow() {
        return rmaToolShow;
    }

    public void setRmaToolShow(Integer rmaToolShow) {
        this.rmaToolShow = rmaToolShow;
    }

    @Size(min = 0, max = 1024, message = "描述长度不能超过 1024 个字符")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<ProductClassification> getProductClassifications() {
        return productClassifications;
    }

    public void setProductClassifications(List<ProductClassification> productClassifications) {
        this.productClassifications = productClassifications;
    }

    public List<Classification> getClassifications() {
        return classifications;
    }

    public void setClassifications(List<Classification> classifications) {
        this.classifications = classifications;
    }

    @Size(min = 0, max = 256, message = "搜索结果页封面长度不能超过 256 个字符")
    public String getSearchCover() {
        return searchCover;
    }

    public void setSearchCover(String searchCover) {
        this.searchCover = searchCover;
    }

    @Size(min = 0, max = 255, message = "产品列表封面长度不能超过 255 个字符")
    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getMobileCover() {
        return mobileCover;
    }

    public void setMobileCover(String mobileCover) {
        this.mobileCover = mobileCover;
    }

    public Integer getSearchView() {
        return searchView;
    }

    public void setSearchView(Integer searchView) {
        this.searchView = searchView;
    }

    public void setClassificationJson(String jsonString) {
        List<String> list = JsonMapper.fromJson(jsonString, List.class);
        if (list != null) {
            for (String val : list) {
                if (StringUtils.isNotBlank(val)) {
                    ProductClassification e = new ProductClassification();
                    e.setProductId(getId());
                    e.setClassificationId(val);
                    e.setIsNewRecord(true);
                    this.productClassifications.add(e);
                }
            }
        }
    }

    public Integer getAllowPurchase() {
        return allowPurchase;
    }

    public void setAllowPurchase(Integer allowPurchase) {
        this.allowPurchase = allowPurchase;
    }


    @Size(max = 1024, message = "US官网链接长度不能超过 1024 个字符")
    public String getUrlUs() {
        return urlUs;
    }
    public void setUrlUs(String urlUs) {
        this.urlUs = urlUs;
    }

    @Size(max = 1024, message = "EU官网链接长度不能超过 1024 个字符")
    public String getUrlEu() {
        return urlEu;
    }
    public void setUrlEu(String urlEu) {
        this.urlEu = urlEu;
    }

    @Size(max = 1024, message = "AU官网链接长度不能超过 1024 个字符")
    public String getUrlAu() {
        return urlAu;
    }
    public void setUrlAu(String urlAu) {
        this.urlAu = urlAu;
    }

}