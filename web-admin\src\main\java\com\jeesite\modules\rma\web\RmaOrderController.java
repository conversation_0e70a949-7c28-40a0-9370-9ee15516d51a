package com.jeesite.modules.rma.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.rma.entity.RmaOrder;
import com.jeesite.modules.rma.service.RmaOrderService;

/**
 * RMA工单Controller
 * <AUTHOR>
 * @version 2024-07-10
 */
@Controller
@RequestMapping(value = "${adminPath}/rma/rmaOrder")
public class RmaOrderController extends BaseController {

	@Autowired
	private RmaOrderService rmaOrderService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RmaOrder get(Long id, boolean isNewRecord) {
		return rmaOrderService.get(String.valueOf(id), isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("rma:rmaOrder:view")
	@RequestMapping(value = {"list", ""})
	public String list(RmaOrder rmaOrder, Model model) {
		model.addAttribute("rmaOrder", rmaOrder);
		return "modules/rma/rmaOrderList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("rma:rmaOrder:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RmaOrder> listData(RmaOrder rmaOrder, HttpServletRequest request, HttpServletResponse response) {
		rmaOrder.setPage(new Page<>(request, response));
		Page<RmaOrder> page = rmaOrderService.findPage(rmaOrder);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("rma:rmaOrder:view")
	@RequestMapping(value = "form")
	public String form(RmaOrder rmaOrder, Model model) {
		model.addAttribute("rmaOrder", rmaOrder);
		return "modules/rma/rmaOrderForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("rma:rmaOrder:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RmaOrder rmaOrder) {
		rmaOrderService.save(rmaOrder);
		return renderResult(Global.TRUE, text("保存RMA工单成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("rma:rmaOrder:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RmaOrder rmaOrder) {
		rmaOrderService.delete(rmaOrder);
		return renderResult(Global.TRUE, text("删除RMA工单成功！"));
	}
	
}