package com.topdon.website.controller;

import com.hiwie.security.constant.CustomException;
import com.hiwie.security.services.MsgService;
import com.topdon.website.model.api.RequestResult;
import com.topdon.website.model.api.ZohoRmaOrder;
import com.topdon.website.services.RmaOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 11:45
 */
@RestController
@RequestMapping("/api/zoho/rmaOrder")
public class RmaOrderApi {

    @Autowired
    private RmaOrderService rmaOrderService;
    @Autowired
    private MsgService msgService;

    @PostMapping("/update")
    public RequestResult update(@RequestBody ZohoRmaOrder zohoRmaOrder) {
        try {
            return RequestResult.ok(rmaOrderService.update(zohoRmaOrder));
        } catch (CustomException e) {
            String s = msgService.customExceptionMsgAndDefault(e);
            return RequestResult.error(e.getCode(), s);
        }
    }

    @PostMapping("/add")
    public RequestResult add(@RequestBody ZohoRmaOrder zohoRmaOrder) {
        try {
            return RequestResult.ok(rmaOrderService.add(zohoRmaOrder));
        } catch (CustomException e) {
            String s = msgService.customExceptionMsgAndDefault(e);
            return RequestResult.error(e.getCode(), s);
        }
    }
}
