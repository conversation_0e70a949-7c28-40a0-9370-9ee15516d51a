package com.topdon.admin.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.dto.InformationGroupTreeDTO;
import com.topdon.admin.entity.InformationGroup;

import java.util.List;

public interface InformationGroupService extends IService<InformationGroup> {


    List<Tree<Integer>> getTree(InformationGroupTreeDTO informationGroupTreeDTO);

    String delete(Integer id);
}
