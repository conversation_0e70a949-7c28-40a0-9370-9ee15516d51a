package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 属性种类表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "property_category")
public class PropertyCategory {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品分类比较id
     */
    @TableField(value = "classification_compare_id")
    private Integer classificationCompareId;

    /**
     * 父id
     */
    @TableField(value = "parent_id")
    private Integer parentId;

    /**
     * 属性种类
     */
    @TableField(value = "category")
    private String category;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 是否草稿
     */
    @TableField(value = "draft")
    private Boolean draft;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "update_date")
    private Date updateDate;
}