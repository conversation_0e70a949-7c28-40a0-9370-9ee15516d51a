package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.form.AuthorizedDealerQueryForm;
import com.topdon.website.mappers.AuthorizedDealerMapper;
import com.topdon.website.model.AuthorizedDealer;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class AuthorizedDealerRepository extends MysqlJDBCSupport {

    @Inject
    protected AuthorizedDealerRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long count(AuthorizedDealerQueryForm queryForm) {
        @Language("SQL") String sql = "select count(0) from authorized_dealer ad left join region r on ad.region_id = r.code where  1=1";
        MapSqlParameterSource params = new MapSqlParameterSource();
        sql += queryForm.getRegionId().map(regionId -> {
            params.addValue("regionId", prefixLike(regionId));
            return " AND ad.region_id like :regionId";
        }).getOrElse(StringUtil.EMPTY);
        return object(sql, Long.class, params);
    }

    public List<AuthorizedDealer> list(AuthorizedDealerQueryForm queryForm, PaginationForm form) {
        @Language("SQL") String sql = "select ad.id, ad.name, ad.region_id,r.name region_name,r.flag, ad.address, ad.link, ad.cover, ad.create_at,ad.email,ad.phone from authorized_dealer ad left join region r on ad.region_id = r.code where  1=1 ";
        MapSqlParameterSource params = new MapSqlParameterSource();
        sql += queryForm.getRegionId().map(regionId -> {
            params.addValue("regionId", prefixLike(regionId));
            return " AND ad.region_id like :regionId";
        }).getOrElse(StringUtil.EMPTY);
        sql += " order by r.name,ad.name";
        return paged(sql, AuthorizedDealerMapper.DETAIL, params, form);
    }

}
