package com.topdon.website.services;

import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.enums.EnumImp;
import com.topdon.website.form.WorkOrderCreateForm;
import com.topdon.website.repositories.WorkOrderRepository;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import javax.inject.Inject;
import javax.inject.Named;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Named
public class WorkOrderService {
    private final WorkOrderRepository workOrderRepository;
    private final String userName;
    private final String receiveEmail;
    private final ProductServices productService;
    private final RegionService regionService;
    private final JavaMailSender sender;
    private final FileService fileService;
    private final DictService dictService;
    private final String root;

    @Inject
    public WorkOrderService(WorkOrderRepository workOrderRepository,
                            @Value("${mail.user_name}") String userName,
                            @Value("${support.receive.email}") String receiveEmail,
                            ProductServices productService,
                            RegionService regionService,
                            JavaMailSender sender,
                            FileService fileService,
                            DictService dictService, @Value("${file.root.path}") String root) {
        this.workOrderRepository = workOrderRepository;
        this.userName = userName;
        this.receiveEmail = receiveEmail;
        this.productService = productService;
        this.regionService = regionService;
        this.sender = sender;
        this.fileService = fileService;
        this.dictService = dictService;
        this.root = root;
    }

    public AbstractEither<ErrorMessage, Long> create(WorkOrderCreateForm createForm, List<Object> files) {
        long id = workOrderRepository.create(createForm);

        try {
            MimeMessage mailMessage = sender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
            messageHelper.setTo(receiveEmail);
            messageHelper.setFrom(userName);
            messageHelper.setSubject("Tech Support From TOPDON Official Website");
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
            cfg.setClassForTemplateLoading(this.getClass(), "/");
            Template template = cfg.getTemplate("mail.ftl");

            Map<String, String> param = Maps.newHashMap();
            param.put("firstName", createForm.getFirstName());
            param.put("lastName", createForm.getLastName());
            param.put("email", createForm.getEmail());
            param.put("sn", createForm.getSerialNumber());
            param.put("vin", createForm.getVin());
            param.put("describe", createForm.getDescribe());
            param.put("describe", createForm.getDescribe());
            regionService.get(createForm.getRegionId()).ifRight(region -> {
                param.put("region", region.getParent().getName());
                param.put("country", region.getName());
            });
            param.put("product", createForm.getProductId());
            param.put("buyChannel", DictService.getDictMapByType(EnumImp.DictType.BUY_CHANNEL.getValue()).map(m -> m.get(createForm.getBuyChannel())).orElseGet(() -> {
                dictService.getDictByType(EnumImp.DictType.BUY_CHANNEL.getValue());
                return DictService.getDictMapByType(EnumImp.DictType.BUY_CHANNEL.getValue()).map(m -> m.get(createForm.getBuyChannel())).orElse("");
            }));
            param.put("channelInfo", Optional.ofNullable(createForm.getChannelInfo()).filter(StringUtils::isNotEmpty).orElse(""));
            messageHelper.setText(FreeMarkerTemplateUtils.processTemplateIntoString(template, param), true);
            if (files != null) {
                for (Object url : files) {
                    fileService.get(Long.parseLong((String) url)).ifRight(hwFile -> {
                        File file = new File(root + hwFile.getName());
                        try {
                            messageHelper.addAttachment(file.getName(), file);
                        } catch (MessagingException e) {
                            e.printStackTrace();
                        }
                    });
                }
            }

            sender.send(mailMessage);
        } catch (MessagingException | IOException | TemplateException e) {
            e.printStackTrace();
        }
        return Right.apply(id);
    }
}
