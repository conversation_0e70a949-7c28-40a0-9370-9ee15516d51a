package com.topdon.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.admin.dto.MenuClickLogListDto;
import com.topdon.admin.dto.MenuClickLogPageDto;
import com.topdon.admin.entity.MenuClickLog;
import com.topdon.admin.mapper.MenuClickLogMapper;
import com.topdon.admin.service.MenuClickLogService;
import com.topdon.admin.vo.MenuClickLogExcelVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MenuClickLogServiceImpl extends ServiceImpl<MenuClickLogMapper, MenuClickLog> implements MenuClickLogService{

    @Override
    public PageDTO<MenuClickLog> getPage(MenuClickLogPageDto menuClickLogPageDto) {
        return this.baseMapper.getPage(new PageDTO<>(menuClickLogPageDto.getPageNo(),menuClickLogPageDto.getPageSize()),menuClickLogPageDto);
    }

    @Override
    public List<MenuClickLogExcelVo> getExcelList(MenuClickLogListDto menuClickLogListDto) {
        return this.baseMapper.getExcelList(menuClickLogListDto);
    }
}
