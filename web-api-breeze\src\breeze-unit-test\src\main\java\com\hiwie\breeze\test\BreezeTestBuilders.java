package com.hiwie.breeze.test;

import com.hiwie.breeze.ErrorMessage;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.function.Consumer;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 */
public class BreezeTestBuilders extends MockMvcRequestBuilders {

    public static Consumer<ErrorMessage> assertError = errorMessage -> Assertions.fail(errorMessage.toString());

    private static Logger LOGGER = LoggerFactory.getLogger(BreezeTestBuilders.class);

    public static ResultActions success(ResultActions actions) throws Exception {
        actions.andExpect(BreezeTestMatchers.jsonPath("$.error").doesNotExist()).andDo(result ->
                LOGGER.info(result.getResponse().getContentAsString())
        ).andExpect(status().is(HttpStatus.OK.value()));
        return actions;
    }

}