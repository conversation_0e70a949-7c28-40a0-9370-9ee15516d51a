package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.form.ProductQueryForm;
import com.topdon.website.model.Classification;
import com.topdon.website.model.ClassificationExtensionLink;
import com.topdon.website.model.Product;
import com.topdon.website.repositories.ClassificationRepository;
import com.topdon.website.service.ClassificationCompareService;
import com.topdon.website.service.ClassificationExtensionLinkService;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Named
public class ClassificationService {

    private final ClassificationRepository classificationRepository;
    private final ProductServices productService;
    private final ClassificationCompareService classificationCompareService;
    private final ClassificationExtensionLinkService classificationExtensionLinkService;

    @Inject
    public ClassificationService(ClassificationRepository classificationRepository, ProductServices productService, ClassificationCompareService classificationCompareService, ClassificationExtensionLinkService classificationExtensionLinkService) {
        this.classificationRepository = classificationRepository;
        this.productService = productService;
        this.classificationCompareService = classificationCompareService;
        this.classificationExtensionLinkService = classificationExtensionLinkService;
    }

    public AbstractEither<ErrorMessage, List<Classification>> list(String parentId) {
        List<Classification> list = classificationRepository.list(parentId);
        for (Classification classification : list) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
            // 填充拓展链接
            List<ClassificationExtensionLink> extensionLinks = getExtensionLinks(classification.getId());
            classification.setExtensionLinks(extensionLinks);
        }
        return Right.apply(list);
    }

    public AbstractEither<ErrorMessage, List<Classification>> menu() {
        List<Classification> classifications = classificationRepository.list("0");
        for (Classification classification : classifications) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
            // 填充拓展链接
            List<ClassificationExtensionLink> extensionLinks = getExtensionLinks(classification.getId());
            classification.setExtensionLinks(extensionLinks);

            if (classification.isHasSub()) {
                List<Classification> children = classificationRepository.list(classification.getId());
                for (Classification child : children) {
                    child.setCompare(classificationCompareService.existCompare(child.getId()));
                    // 填充子分类的拓展链接
                    List<ClassificationExtensionLink> childExtensionLinks = getExtensionLinks(child.getId());
                    child.setExtensionLinks(childExtensionLinks);

                    if (child.getProductCount() > 0) {
                        List<Product> products = productService.list(ProductQueryForm.classForm(child.getId())).right().get();
                        child.setProducts(products);
                        child.setNewProduct(products.stream().map(Product::getNewProduct).max(Comparator.comparing(o -> o)).orElse(null));
                    }
                }
                classification.setChildren(children);
            }
        }
        return Right.apply(classifications);
    }

    public AbstractEither<ErrorMessage, Classification> get(String id) {
        AbstractOption<Classification> classificationAbstractOption = classificationRepository.get(id);
        Classification classification = classificationAbstractOption.get();
        if (classification != null) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
            // 填充拓展链接
            List<ClassificationExtensionLink> extensionLinks = getExtensionLinks(classification.getId());
            classification.setExtensionLinks(extensionLinks);
        }
        return classificationAbstractOption.toRight(Classification.Errors.NOT_FOUND);
    }

    /**
     * 获取分类的拓展链接列表
     * @param classificationId 分类ID
     * @return 拓展链接列表
     */
    private List<ClassificationExtensionLink> getExtensionLinks(String classificationId) {
        List<com.topdon.website.entity.ClassificationExtensionLink> entityList =
            classificationExtensionLinkService.findByClassificationCode(classificationId);

        return entityList.stream()
            .map(this::convertToModel)
            .collect(Collectors.toList());
    }

    /**
     * 将实体转换为模型
     * @param entity 实体对象
     * @return 模型对象
     */
    private ClassificationExtensionLink convertToModel(com.topdon.website.entity.ClassificationExtensionLink entity) {
        ClassificationExtensionLink model = new ClassificationExtensionLink();
        model.setId(entity.getId());
        model.setClassificationCode(entity.getClassificationCode());
        model.setIconDefault(entity.getIconDefault());
        model.setIconHover(entity.getIconHover());
        model.setNavText(entity.getNavText());
        model.setCategoryText(entity.getCategoryText());
        model.setSortOrder(entity.getSortOrder());
        model.setIsDisplay(entity.getIsDisplay());
        model.setJumpLink(entity.getJumpLink());
        return model;
    }

}
