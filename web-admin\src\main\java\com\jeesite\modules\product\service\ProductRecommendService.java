package com.jeesite.modules.product.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductRecommend;
import com.jeesite.modules.product.dao.ProductRecommendDao;

/**
 * 产品推荐管理Service
 * <AUTHOR>
 * @version 2022-07-13
 */
@Service
@Transactional(readOnly=true)
public class ProductRecommendService extends CrudService<ProductRecommendDao, ProductRecommend> {
	
	/**
	 * 获取单条数据
	 * @param productRecommend
	 * @return
	 */
	@Override
	public ProductRecommend get(ProductRecommend productRecommend) {
		return super.get(productRecommend);
	}
	
	/**
	 * 查询分页数据
	 * @param productRecommend 查询条件
	 * @param productRecommend.page 分页对象
	 * @return
	 */
	@Override
	public Page<ProductRecommend> findPage(ProductRecommend productRecommend) {
		return super.findPage(productRecommend);
	}
	
	/**
	 * 查询列表数据
	 * @param productRecommend
	 * @return
	 */
	@Override
	public List<ProductRecommend> findList(ProductRecommend productRecommend) {
		return super.findList(productRecommend);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param productRecommend
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ProductRecommend productRecommend) {
		super.save(productRecommend);
	}
	
	/**
	 * 更新状态
	 * @param productRecommend
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ProductRecommend productRecommend) {
		super.updateStatus(productRecommend);
	}
	
	/**
	 * 删除数据
	 * @param productRecommend
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ProductRecommend productRecommend) {
		super.delete(productRecommend);
	}
	
}