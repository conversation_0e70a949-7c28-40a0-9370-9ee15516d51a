<% layout('/layouts/default.html', {title: '产品资料配置', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
                <i class="fa icon-notebook"></i> ${text('产品资料配置')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('product:information:edit')){ %>
					<a href="${ctx}/product/information/form" class="btn btn-default btnTool" title="${text('新增产品资料')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
            <#form:form id="searchForm" model="${information}" action="${ctx}/v1/product/information/listData"
            method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
			<div class="form-group">
				<label class="control-label">${text('产品')}：</label>
				<div class="control-inline">
                    <#form:input path="productName" maxlength="255" class="form-control width-120"/>

				</div>
			</div>
				<div class="form-group">
					<label class="control-label">${text('显示内容')}：</label>
					<div class="control-inline">
						<#form:input path="content" maxlength="1024" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资料类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="type" dictType="infomation_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
                    <label class="control-label">${text('一级分组标题')}：</label>
                    <div class="control-inline">
                        <#form:input path="group1" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
            <div class="form-group">
                <label class="control-label">${text('二级分组标题')}：</label>
					<div class="control-inline">
                        <#form:input path="group2" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
        {
            header: '${text("名称")}',
            name: 'name',
            index: 'i.name',
            width: 150,
            align: "left",
            frozen: true,
            formatter: function (val, obj, row, act) {
			return '<a href="${ctx}/product/information/form?id='+row.id+'" class="btnList" data-title="${text("编辑产品资料")}">'+(val||row.id)+'</a>';
		}},
        {header: '${text("产品")}', name: 'productName', index: 'productName', width: 150, align: "left"},
        {header: '${text("显示内容")}', name: 'content', index: 'i.content', width: 150, align: "left"},
        {header: '${text("排序")}', name: 'sort', index: 'i.sort', width: 150, align: "center"},
        {
            header: '${text("显示封面")}',
            name: 'mediaUrl',
            index: 'i.media_url',
            width: 150,
            align: "center",
            formatter: function (val, obj, row, act) {
				if (val){
					return "<img width='100%' src=" + encodeURI(val) + " />";
				}else{
					return "缺失";
				}

			}},
        {header: '${text("创建时间")}', name: 'createAt', index: 'i.create_at', width: 150, align: "center"},
        {header: '${text("更新时间")}', name: 'updateAt', index: 'i.update_at', width: 150, align: "center"},
        {header: '${text("下载地址")}', name: 'downloadUrl', index: 'i.download_url', width: 150, align: "left"},
		{header:'${text("资料类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('infomation_type')}, val, '${text("未知")}', true);
		}},
        {header: '${text("一级分组标题")}', name: 'group1', index: 'group1', width: 150, align: "left"},
        {header: '${text("二级分组标题")}', name: 'group2', index: 'group2', width: 150, align: "left"},
        {
            header: '${text("文件类型")}',
            name: 'fileType',
            index: 'i.file_type',
            width: 150,
            align: "center",
            formatter: function (val, obj, row, act) {
			return js.getDictLabel(${@DictUtils.getDictListJson('file_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('product:information:edit')){ %>
				actions.push('<a href="${ctx}/product/information/form?id='+row.id+'" class="btnList" title="${text("编辑产品资料")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/product/information/delete?id='+row.id+'" class="btnList" title="${text("删除产品资料")}" data-confirm="${text("确认要删除该产品资料吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/product/information/copy?id='+row.id+'" class="btnList" title="${text("复制产品资料")}"><i class="fa fa-copy"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>