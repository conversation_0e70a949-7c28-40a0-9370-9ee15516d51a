package com.topdon.website.configs;

import com.zoho.oauth.client.ZohoOAuthClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/15 16:23
 */
@Configuration
@Slf4j
public class ZohoConfig implements ApplicationContextAware {

    @Value("${desk.sdk.config.fileName}")
    private String configFileName;
    @Value("${zoho.config.clientId}")
    private String clientId;
    @Value("${zoho.config.clientSecret}")
    private String clientSecret;
    @Value("${zoho.config.redirectUri}")
    private String redirectUri;
    @Value("${zoho.config.timeOut}")
    private String timeOut;
    @Value("${zoho.config.scope}")
    private String scope;
    @Value("${zoho.config.minLogLevel}")
    private String minLogLevel;
    @Value("${zoho.config.dc}")
    private String dc;
    @Value("${zoho.config.userIdentifier}")
    private String userIdentifier;
    @Value("${zoho.config.oauthTokensFilePath}")
    private String oauthTokensFilePath;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, Object> zohoOAuthMap = new HashMap<>();
        zohoOAuthMap.put("client_id", clientId);
        zohoOAuthMap.put("client_secret", clientSecret);
        zohoOAuthMap.put("redirect_uri", redirectUri);
        zohoOAuthMap.put("timeout", timeOut);
        zohoOAuthMap.put("scope", scope);
        zohoOAuthMap.put("minLogLevel", minLogLevel);
        zohoOAuthMap.put("dc", dc);
        zohoOAuthMap.put("logFilePath", "/var/log");
        zohoOAuthMap.put("persistence_handler_class", "com.topdon.website.helper.ZohoOAuthFilePersistence");
        zohoOAuthMap.put("oauth_tokens_file_path", oauthTokensFilePath + "/oauthtokens.properties");
        zohoOAuthMap.put("user_identifier", userIdentifier);

        File file = new File(oauthTokensFilePath);
        if (!file.isDirectory() || !file.exists()) {
            file.mkdirs();
        }

        File tempFile = null;
        FileWriter writer = null;
        try {
            // 创建临时文件
            tempFile = File.createTempFile(configFileName, ".txt");

            // 将内容写入临时文件
            writer = new FileWriter(tempFile);
            for (Map.Entry<String, Object> stringObjectEntry : zohoOAuthMap.entrySet()) {
                writer.write(String.format("%s=%s\r", stringObjectEntry.getKey(), stringObjectEntry.getValue()));
            }

            // 输出临时文件的路径
            System.setProperty("desk.sdk.config", tempFile.getAbsolutePath());
            log.info("desk.sdk.config: " + tempFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("init zoho config error!", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("init zoho config error!", e);
                }
            }
        }
        ZohoOAuthClient.getInstance();
    }
}
