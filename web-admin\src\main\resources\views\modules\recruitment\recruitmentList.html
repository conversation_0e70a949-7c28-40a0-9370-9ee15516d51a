<% layout('/layouts/default.html', {title: '招聘管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('招聘管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('recruitment:recruitment:edit')){ %>
					<a href="${ctx}/recruitment/recruitment/form" class="btn btn-default btnTool" title="${text('新增招聘')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${recruitment}" action="${ctx}/recruitment/recruitment/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('岗位名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作地点')}：</label>
					<div class="control-inline">
						<#form:input path="position" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('岗位类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="category" dictType="careers_category" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('招聘类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="type" dictType="careers_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("岗位名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/recruitment/recruitment/form?id='+row.id+'" class="btnList" data-title="${text("编辑招聘")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("招聘人数")}', name:'people', index:'a.people', width:150, align:"center"},
		{header:'${text("工作地点")}', name:'position', index:'a.position', width:150, align:"left"},
		{header:'${text("岗位类型")}', name:'category', index:'a.category', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('careers_category')}, val, '${text("未知")}', true);
		}},
		{header:'${text("招聘类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('careers_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('recruitment:recruitment:edit')){ %>
				actions.push('<a href="${ctx}/recruitment/recruitment/form?id='+row.id+'" class="btnList" title="${text("编辑招聘")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/recruitment/recruitment/delete?id='+row.id+'" class="btnList" title="${text("删除招聘")}" data-confirm="${text("确认要删除该招聘吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>