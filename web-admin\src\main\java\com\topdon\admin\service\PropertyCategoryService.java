package com.topdon.admin.service;

import com.topdon.admin.dto.ProductPropertySaveDTO;
import com.topdon.admin.entity.ProductProperty;
import com.topdon.admin.entity.PropertyCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.vo.PropertyCategoryListVo;

import java.util.List;
import java.util.Map;

public interface PropertyCategoryService extends IService<PropertyCategory>{

    Map<Integer,List<PropertyCategoryListVo>> getList(Integer classificationCompareId, boolean draft);

    Map<String, PropertyCategory> saveOrUpdateList(boolean draft, Integer compareId, List<ProductPropertySaveDTO.PropertyCategorySaveDTO> propertyCategories);
}
