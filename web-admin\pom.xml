<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.jeesite</groupId>
		<artifactId>jeesite-parent</artifactId>
		<version>4.3.3-SNAPSHOT</version>
		<relativePath>../parent/pom.xml</relativePath>
	</parent>
	
	<artifactId>jeesite-web</artifactId>
	<packaging>jar</packaging>
	
	<name>JeeSite Web</name>
	<url>http://jeesite.com</url>
	<inceptionYear>2013-Now</inceptionYear>
	
	<properties>
		
		<finalName>admin</finalName><!-- war or jar 包的名称 -->
		<start-class>com.jeesite.modules.AdminWebApplication</start-class>
		
		<!-- environment setting -->
		<eclipse-plugin-download-sources>false</eclipse-plugin-download-sources>
		<eclipse-plugin-download-javadocs>false</eclipse-plugin-download-javadocs>
		
		<!-- docker setting -->
        <docker.run.port>8980:8980</docker.run.port>
		
	</properties>

	<dependencies>
		
		<!-- 核心模块 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-core</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		
		<!-- 在线文档接口 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-swagger</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.8.0</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>*******</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.29</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.5.5.Final</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.5.5.Final</version>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*</include>
				</includes>
				<filtering>true</filtering>
                <excludes>
                    <exclude>**/*.woff</exclude>
                    <exclude>**/*.woff2</exclude>
					<exclude>**/*.ttf</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.woff</include>
                    <include>**/*.woff2</include>
					<include>**/*.ttf</include>
                </includes>
                <filtering>false</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.3.0.RELEASE</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<mainClass>com.jeesite.modules.AdminWebApplication</mainClass>
					<includeSystemScope>true</includeSystemScope>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>11</source>
					<target>11</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</repository>
		<repository>
			<id>jeesite-repos</id>
			<url>http://maven.jeesite.net/repository/maven-public</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>jeesite-repos</id>
			<url>http://maven.jeesite.net/repository/maven-public</url>
		</pluginRepository>
	</pluginRepositories>
	
</project>
