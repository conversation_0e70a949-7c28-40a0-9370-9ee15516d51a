<% layout('/layouts/default.html', {title: '新闻管理', libs: ['validate','fileupload','ueditor']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(news.isNewRecord ? '新增新闻' : '编辑新闻')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${news}" action="${ctx}/news/news/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="120" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('发布时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="publishAt" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime2" data-type="datetime" data-format="yyyy-MM-dd HH:mm:ss"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('分组')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="category" dictType="news_type" blankOption="false" class="form-control required" defaultValue="PRODUCT" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">

							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('是否一行显示')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:radio path="isMain" dictType="sys_yes_no" class="form-control digits required" defaultValue="1"/>
							</div>
						</div>

					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('是否置顶')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:radio path="isTop" dictType="sys_yes_no" class="form-control digits required" defaultValue="0" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								${text('ID')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="id" maxlength="120" class="form-control" readonly="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								${text('状态')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="newsStatus" dictType="sys_news_status" class="form-control" readonly="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('封面')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadImage" bizKey="${news.id}" bizType="news_cover_image"
								uploadType="image" class="" maxUploadNum="1" readonly="false" preview="true" returnPath="true" filePathInputId="cover"/>
								<#form:hidden path="cover"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								${text('描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="introduction" rows="4" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required">*</span> ${text('内容')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:ueditor path="content" maxlength="500000" height="400" simpleToolbars="false" readonly="false" class="required" outline="false"/>
							</div>
						</div>
					</div>
				</div>



			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('news:news:edit')){ %>
							<button type="submit" class="btn btn-sm btn-info" id="btnDraft" onclick="f()"><i class="fa fa-save"></i> ${text('保存草稿')}</button>&nbsp;
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit" onclick="f1()"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});

function f1() {
	$("#inputForm").attr('action', '${ctx}/news/news/save');
}

function f() {
	let content = contentUE.getContent()
	if(!content.startsWith('<!--HTML-->')){
		content = '<!--HTML-->' + content
	}
	$('#content').val(content)
	$("#inputForm").attr('action', '${ctx}/news/news/draft');
	js.ajaxSubmitForm($("#inputForm"),function (data) {
		if (data.result == Global.TRUE) {
			$('#id').val(data.message);
		}
	})
}
</script>