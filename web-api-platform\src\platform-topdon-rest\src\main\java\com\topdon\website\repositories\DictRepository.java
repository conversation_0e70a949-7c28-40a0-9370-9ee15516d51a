package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.DictMapper;
import com.topdon.website.model.Dict;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/8 14:32
 */
@Named
public class DictRepository extends MysqlJDBCSupport {

    @Inject
    protected DictRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<Dict> getDictByType(String type) {
        @Language("SQL") String sql = "select r.dict_code, r.dict_type, r.dict_value, r.dict_label, r.tree_sort, r.extend_s1 from js_sys_dict_data r where r.status = 0 and r.dict_type = :type";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("type", type);
        return list(sql, DictMapper.DETAIL, params);
    }
}
