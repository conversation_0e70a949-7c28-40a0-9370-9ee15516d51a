package com.hiwie.breeze.json;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.None;
import com.hiwie.breeze.json.serializers.AbstractOptionSerializer;
import com.hiwie.breeze.json.serializers.ErrorMessageSerializer;

/**
 * <AUTHOR>
 */
public class BreezeModule extends SimpleModule {

    private static final long serialVersionUID = 1L;

    public BreezeModule() {
        addSerializer(ErrorMessage.class, ErrorMessageSerializer.INSTANCE);
        addSerializer(AbstractOption.class, AbstractOptionSerializer.INSTANCE);
    }
}
