package com.topdon.website.mappers;

import com.hiwie.breeze.AbstractOption;
import com.topdon.website.model.Classification;
import com.topdon.website.model.Product;
import com.topdon.website.vo.ProductVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.Timestamp;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class ProductMapper {

    public static final RowMapper<Product> DETAIL = (rs, index) -> {
        Product detail = new Product();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setDescription(rs.getString("description"));
        detail.setCover(rs.getString("cover"));
        detail.setSearchCover(rs.getString("search_cover"));
        detail.setMobileCover(rs.getString("mobile_cover"));
        detail.setCreateAt(AbstractOption.apply(rs.getTimestamp("create_at")).map(Timestamp::toLocalDateTime).orNull());
        detail.setClassification(Classification.defaultPk(rs.getString("class_id"), rs.getString("class_name")));
        Optional.ofNullable(rs.getString("class_id_concat")).filter(StringUtils::isNotEmpty).ifPresent(detail::setClassIdConcat);
        Optional.ofNullable(rs.getInt("new_product")).ifPresent(detail::setNewProduct);
        Optional.ofNullable(rs.getInt("discontinued")).ifPresent(detail::setDiscontinued);
        return detail;
    };

    public static final RowMapper<ProductVo> Product = (rs, index) -> {
        ProductVo detail = new ProductVo();
        detail.setId(rs.getString("id"));
        detail.setCompareClassificationCode(rs.getString("compare_classification_code"));
        detail.setCompare(rs.getBoolean("compare"));
        return detail;
    };

}
