package com.hiwie.breeze.service;

import com.hiwie.breeze.*;

import java.util.function.Function;

/**
 * 实例Getter
 *
 * @param <P> 参数类型
 * @param <I> 实例类型
 * <AUTHOR>
 */
public class InstanceGetter<P, I> {

    private final AbstractOption<P> parameterOption;

    private final AbstractOption<I> instanceOption;

    private InstanceGetter(AbstractOption<P> parameterOption, AbstractOption<I> instanceOption) {
        this.parameterOption = parameterOption;
        this.instanceOption = instanceOption;
    }

    public static <P, I> InstanceGetter<P, I> applyInstance(I instance) {
        return new InstanceGetter<>(None.apply(), Some.apply(instance));
    }

    public static <P, I> InstanceGetter<P, I> applyParameter(P parameter) {
        return new InstanceGetter<>(Some.apply(parameter), None.apply());
    }

    public AbstractEither<ErrorMessage, I> get(Function<P, AbstractEither<ErrorMessage, I>> function) {
        return instanceOption.<AbstractEither<ErrorMessage, I>>map(Right::<ErrorMessage, I>apply).getOrElse(() -> function.apply(parameterOption.get()));
    }

}