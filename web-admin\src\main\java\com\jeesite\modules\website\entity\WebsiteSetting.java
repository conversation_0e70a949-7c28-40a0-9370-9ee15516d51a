package com.jeesite.modules.website.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 网站配置Entity
 * <AUTHOR>
 * @version 2023-02-16
 */
@Table(name="website_setting", alias="a", label="配置信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="key", attrName="key", label="关键字"),
		@Column(name="value", attrName="value", label="显示值"),
		@Column(name="remarks", attrName="remarks", label="备注", isQuery=false),
	}, orderBy="a.id DESC"
)
public class WebsiteSetting extends DataEntity<WebsiteSetting> {
	
	private static final long serialVersionUID = 1L;
	private String key;		// 关键字
	private String value;		// 显示值
	
	public WebsiteSetting() {
		this(null);
	}

	public WebsiteSetting(String id){
		super(id);
	}
	
	@Size(min=0, max=20, message="关键字长度不能超过 20 个字符")
	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}
	
	@Size(min=0, max=256, message="显示值长度不能超过 256 个字符")
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
}