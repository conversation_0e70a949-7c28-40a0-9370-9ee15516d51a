package com.topdon.admin.vo;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import lombok.AllArgsConstructor;
import lombok.Data;


@Data
@AllArgsConstructor
public class ProductLinesExportVo {

    @ExcelFields({
            @ExcelField(title = "Product Lines", attrName = "productLines", align = ExcelField.Align.LEFT, sort = 50),
            @ExcelField(title = "Product Series", attrName = "productSeries", align = ExcelField.Align.LEFT, sort = 60),
            @ExcelField(title = "Product Models", attrName = "productModels", align = ExcelField.Align.LEFT, sort = 70),
    })
    public ProductLinesExportVo() {
    }

    private String productLines;
    private String productSeries;
    private String productModels;

}
