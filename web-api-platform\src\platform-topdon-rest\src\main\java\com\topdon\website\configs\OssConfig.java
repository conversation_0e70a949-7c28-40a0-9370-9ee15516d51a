package com.topdon.website.configs;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;

@SpringBootConfiguration
public class OssConfig {

    @Bean(name = "ossClient")
    public OSS ossClient(
            @Value("${file.client.aliyun.endpoint}") String endpoint,
            @Value("${file.client.aliyun.accessKeyId}") String accessKeyId,
            @Value("${file.client.aliyun.accessKeySecret}") String accessKeySecret,
            @Value("${file.client.aliyun.bucketName}") String bucketName
    ) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        /*
         * Determine whether the bucket exists
         */
        if (!ossClient.doesBucketExist(bucketName)) {
            /*
             * Create a new OSS bucket
             */
            ossClient.createBucket(bucketName);
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
            ossClient.createBucket(createBucketRequest);
        }
        return ossClient;
    }

}
