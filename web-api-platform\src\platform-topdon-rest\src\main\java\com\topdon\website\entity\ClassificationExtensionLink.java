package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 分类拓展链接表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "classification_extension_link")
public class ClassificationExtensionLink {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 分类编码
     */
    @TableField(value = "classification_code")
    private String classificationCode;

    /**
     * 图标(默认)
     */
    @TableField(value = "icon_default")
    private String iconDefault;

    /**
     * 图标(鼠标移入)
     */
    @TableField(value = "icon_hover")
    private String iconHover;

    /**
     * 导航文案
     */
    @TableField(value = "nav_text")
    private String navText;

    /**
     * 二级品类文案
     */
    @TableField(value = "category_text")
    private String categoryText;

    /**
     * 排序
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    /**
     * 是否展示(0:否,1:是)
     */
    @TableField(value = "is_display")
    private String isDisplay;

    /**
     * 跳转链接
     */
    @TableField(value = "jump_link")
    private String jumpLink;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 删除标记(0:正常,1:删除)
     */
    @TableField(value = "del_flag")
    private String delFlag;
}