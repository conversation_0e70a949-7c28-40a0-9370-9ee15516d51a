package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

public class WebsiteSetting {
    public static final String ENTITY_NAME = "WEBSITE_SETTING";
    private String id;
    private String name;
    private String value;
    private String remarks;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUNT");
    }
}
