package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.breeze.rest.ErrorRestResponse;
import com.topdon.website.form.WorkOrderCreateForm;
import com.topdon.website.services.WorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("work_order")
public class WorkOrderController extends ControllerSupport {

    private final WorkOrderService workOrderService;

    @Autowired
    public WorkOrderController(WorkOrderService workOrderService) {
        this.workOrderService = workOrderService;
    }

    @PostMapping()
    public AbstractRestResponse create(@Valid WorkOrderCreateForm createForm, @RequestParam(value = "files[]", required = false) List<Object> files, BindingResult result) {
        if (result.hasErrors()) {
            return ErrorRestResponse.apply(BINDING_ERROR.apply(result));
        }
        return AbstractRestResponse.apply(workOrderService.create(createForm, files));
    }
}
