package com.topdon.website.services;

import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.security.models.Session;
import com.topdon.website.form.OrderQueryForm;
import com.topdon.website.form.TopdonPageForm;
import com.topdon.website.helper.RequestUtil;
import com.topdon.website.model.TopdonResponse;

import javax.inject.Named;
import java.util.Map;

@Named
public class OrderService {

    public AbstractEither<ErrorMessage, TopdonResponse> list(Session session, TopdonPageForm pageForm, OrderQueryForm queryForm) {

        Map<String, Object> params = Maps.newHashMap();
        params.put("accountId", session.getUserId());
        params.put("email", session.getUserCode());
        params.put("current", pageForm.getCurrent());
        params.put("size", pageForm.getSize());
        params.put("snNumber", queryForm.getSnNumber());
        params.put("usName", queryForm.getUsName());
        params.put("modal", queryForm.getModal());
        params.put("startTime", queryForm.getStartTime());
        params.put("endTime", queryForm.getEndTime());
        params.put("type", queryForm.getType() == null ? 0 : queryForm.getType());
        return RequestUtil.postWithSign("/order/getOrderPage", params);
    }

    public AbstractEither<ErrorMessage, TopdonResponse> get(Session session, String orderNo, Integer type) {

        Map<String, Object> params = Maps.newHashMap();
        params.put("accountId", session.getUserId());
        params.put("email", session.getUserCode());
        params.put("orderNo", orderNo);
        params.put("type", type);
        return RequestUtil.postWithSign("/order/getOrderDetail", params);
    }

    public AbstractEither<ErrorMessage, TopdonResponse> sync() {
        return RequestUtil.postWithSign("/order/syncShopify", Maps.newHashMap());
    }
}
