package com.topdon.admin.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.modules.classification.entity.Classification;
import com.topdon.admin.entity.ClassificationCompare;
import com.topdon.admin.service.ClassificationCompareService;
import com.topdon.admin.vo.ClassificationCompareVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Controller
@RequestMapping(value = "${adminPath}/classification/productCompare")
@Slf4j
public class ClassificationCompareController {

    @Resource
    private ClassificationCompareService classificationCompareService;


    @RequiresPermissions("product:productCompare:view")
    @GetMapping
    public String view(Model model) throws JsonProcessingException {
        return "modules/classification/classificationCompareList";
    }

    @ResponseBody
    @PostMapping("/getClassificationList")
    public List<ClassificationCompareVo> getClassificationList(Classification classification){
        return classificationCompareService.getClassificationList(classification);
    }

    @PostMapping("/save")
    @ResponseBody
    public String save(ClassificationCompare classificationCompare){
        return classificationCompareService.saveClassificationCompare(classificationCompare);
    }

    @RequestMapping("/form")
    public String form(Integer compareId, Model model) {
        model.addAttribute("compareId",compareId);
        return "modules/classification/productCompareForm";
    }

    @ResponseBody
    @PostMapping("/delete")
    public Dict delete(Integer compareId, Model model) {
        classificationCompareService.removeById(compareId);
        return Dict.create().set("message","删除成功").set("result","true");
    }

}
