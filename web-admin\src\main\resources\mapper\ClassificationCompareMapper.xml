<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.ClassificationCompareMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.ClassificationCompare">
    <!--@mbg.generated-->
    <!--@Table classification_compare-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="classification_code" jdbcType="VARCHAR" property="classificationCode" />
    <result column="draft" jdbcType="BOOLEAN" property="draft" />
    <result column="release_date" jdbcType="TIMESTAMP" property="releaseDate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, classification_code, draft, release_date, create_by, create_date, update_by, 
    update_date
  </sql>
</mapper>