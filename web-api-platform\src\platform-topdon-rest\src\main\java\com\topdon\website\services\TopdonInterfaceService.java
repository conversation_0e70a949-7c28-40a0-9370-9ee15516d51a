package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.json.Json;
import com.topdon.website.SCConstants;
import com.topdon.website.form.VehicleCoverageQueryForm;
import com.topdon.website.model.TopdonResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

import javax.inject.Named;
import java.io.IOException;

import static com.topdon.website.SCConstants.CONTENT_TYPE_TEXT_JSON;

@Named
public class TopdonInterfaceService {



    public AbstractEither<ErrorMessage, TopdonResponse> list(VehicleCoverageQueryForm queryForm){
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://community.topdon.com/expand/diagcar/list";
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = null;
        try {
            StringEntity se = new StringEntity(Json.writeValueAsString(queryForm));
            se.setContentType(CONTENT_TYPE_TEXT_JSON);
            post.setEntity(se);
            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            TopdonResponse cmbResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            return Right.apply(cmbResponse);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Left.apply(SCConstants.API_ERROR);
    }

    public AbstractEither<ErrorMessage, TopdonResponse> condition(VehicleCoverageQueryForm queryForm){
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://community.topdon.com/expand/diagcar/queryConditions";
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = null;
        try {
            StringEntity se = new StringEntity(Json.writeValueAsString(queryForm));
            se.setContentType(CONTENT_TYPE_TEXT_JSON);
            post.setEntity(se);
            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            TopdonResponse cmbResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            return Right.apply(cmbResponse);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Left.apply(SCConstants.API_ERROR);
    }

}
