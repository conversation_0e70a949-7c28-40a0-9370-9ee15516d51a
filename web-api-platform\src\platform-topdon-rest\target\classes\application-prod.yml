spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-request-size: 50MB
      max-file-size: 50MB
  datasource:
    url: **************************************************************************************************************************************************************************************************
    username: tp-user
    password: K6Jv3EQt3J
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1
      validation-query-timeout: 5
      test-on-borrow: true
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000

server:
  id: PM
  domain: https://www.topdon.com
  # domain: http://**************:8082/api
  servlet:
    context-path: /api
  port: 8895

mail:
  host: smtp.gmail.com
  port: 587
  user_name: <EMAIL>
  password: ahpznayulikdyhhx

shopify:
  api:
    graphql:
      version: 2022-10
  graphql:
    url: https://topdon-it.myshopify.com/admin/api/2022-10/graphql.json
  admin:
    graphql:
      token: 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
  storefront:
    graphql:
      url: https://topdon-it.myshopify.com/api/2022-10/graphql.json
      token: 76721c798b82d8f117d677b7d5c67ba3

support:
  receive:
    email: <EMAIL>

file:
  root:
    path: /home/<USER>/webapps/filetmp/
  zip:
    root:
      path: /opt/data
  client:
    aliyun:
      endpoint: oss-accelerate.aliyuncs.com
      accessKeyId: LTAI5tPBweQbPPnW673y1FCy
      accessKeySecret: ******************************
      bucketName: lenkor-plat
      root: topdon-web/
      proxyEndpoint: web-file.topdon.com

topdon:
  url: https://api.topdon.com

lenkor:
  innerApi:
    url: https://erp.lenkor.cn/lk-erp-innerapi/
    clientId: official-website
    clientSecret: dc52e88a30364ec69eafca63670d7687


desk:
  sdk:
    config:
      fileName: zohoSdkConfig

zoho:
  config:
    enabled: true
    clientId: 1000.******************************
    clientSecret: 4f673246bc3217be015821aeeb2f17f6d652b1171b
    redirectUri: https://official-web-api.topdon.com/api/zoho/auth/callBackCode
    timeOut: 900
    scope: Desk.tickets.ALL,Desk.tasks.ALL,Desk.settings.ALL,Desk.events.ALL,Desk.basic.ALL,Desk.contacts.ALL
    minLogLevel: INFO
    dc: com
    userIdentifier: <EMAIL>
    oauthTokensFilePath: /var/zohoOauth

logging:
  level:
    root: info
  config: classpath:logback-server.xml
  file:
    path: logs