package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.hiwie.security.SecurityConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@Data
public class TopdonQuickLogin {


    private String token;
    private String avatar;
    private String userName;
    private String gender;
    private String phone;
    private String email;
    private String topdonId;
    private Integer userId;
    private String refreshToken;
    private String expiresIn;

    public static class Errors {

        private static final String ENTITY_NAME = "LOGIN";

        public static Map<Integer,ErrorMessage> MESSAGE_MAP = new HashMap<>(){{
            put(5000,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "CODE_EMPTY"));
            put(60400,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "PHONE_NOT_BOUND"));
            put(60106,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "INVALID_OR_EXPIRED_CODE"));
            put(60107,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "INVALID_VERIFICATION_CODE"));
            put(60103,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "USER_EXISTS_OR_DISABLED"));
            put(60001,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "ACCOUNT_NOT_REGISTERED"));
            put(60002,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "INVALID_PASSWORD"));
            put(60003,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "USER_DISABLED"));
            put(60508,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "EMAIL_NOT_BOUND"));
            put(60511,new ErrorMessage(SecurityConstants.MODULE, ENTITY_NAME, "CONFIRM_BIND"));
        }};

    }

}
