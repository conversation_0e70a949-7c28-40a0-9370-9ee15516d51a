package com.jeesite.modules.recruitment.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.recruitment.entity.Recruitment;
import com.jeesite.modules.recruitment.service.RecruitmentService;

/**
 * 招聘管理Controller
 * <AUTHOR>
 * @version 2022-04-20
 */
@Controller
@RequestMapping(value = "${adminPath}/recruitment/recruitment")
public class RecruitmentController extends BaseController {

	@Autowired
	private RecruitmentService recruitmentService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public Recruitment get(String id, boolean isNewRecord) {
		return recruitmentService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("recruitment:recruitment:view")
	@RequestMapping(value = {"list", ""})
	public String list(Recruitment recruitment, Model model) {
		model.addAttribute("recruitment", recruitment);
		return "modules/recruitment/recruitmentList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("recruitment:recruitment:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<Recruitment> listData(Recruitment recruitment, HttpServletRequest request, HttpServletResponse response) {
		recruitment.setPage(new Page<>(request, response));
		Page<Recruitment> page = recruitmentService.findPage(recruitment);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("recruitment:recruitment:view")
	@RequestMapping(value = "form")
	public String form(Recruitment recruitment, Model model) {
		model.addAttribute("recruitment", recruitment);
		return "modules/recruitment/recruitmentForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("recruitment:recruitment:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated Recruitment recruitment) {
		recruitmentService.save(recruitment);
		return renderResult(Global.TRUE, text("保存招聘成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("recruitment:recruitment:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(Recruitment recruitment) {
		recruitmentService.delete(recruitment);
		return renderResult(Global.TRUE, text("删除招聘成功！"));
	}
	
}