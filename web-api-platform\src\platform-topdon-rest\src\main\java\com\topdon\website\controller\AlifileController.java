package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping("file")
public class AlifileController extends ControllerSupport {
    private final FileService fileService;

    @Autowired
    public AlifileController(FileService fileService) {
        this.fileService = fileService;
    }

    @PostMapping
    public AbstractRestResponse upload(MultipartFile file, HttpServletRequest request) throws IOException {
        return AbstractRestResponse.apply(fileService.uploadFile(file));
    }
}
