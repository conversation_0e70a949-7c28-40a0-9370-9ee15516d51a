package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 授权卖家违规信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "seller_auth_violations")
public class SellerAuthViolations {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "seller_auth_id")
    private Integer sellerAuthId;

    /**
     * 违规信息
     */
    @TableField(value = "message")
    private String message;

    @TableField(value = "`date`")
    private Date date;
}