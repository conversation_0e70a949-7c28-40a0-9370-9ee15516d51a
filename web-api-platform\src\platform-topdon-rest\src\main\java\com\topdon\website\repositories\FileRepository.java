package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.HWFileMapper;
import com.topdon.website.model.HWFile;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Named
public class FileRepository extends MysqlJDBCSupport {

    @Inject
    protected FileRepository(
            JdbcTemplate db,
            NamedParameterJdbcTemplate namedDB
    ) {
        super(db, namedDB);
    }

    public AbstractOption<HWFile> get(long id) {
        @Language("SQL") String sql = "SELECT id, name, mime, size, create_by, create_at FROM file WHERE ID = :ID;";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("ID", id);
        return option(sql, HWFileMapper.DETAIL, params);
    }


    public long create(String name, String mime, long size) {
        @Language("SQL") String sql = "INSERT INTO file (name, mime, size, create_at) VALUES (:NAME, :MIME, :SIZE, :CREATE_AT)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("NAME", name);
        params.addValue("SIZE", size);
        params.addValue("MIME", mime);
        params.addValue("CREATE_AT", Timestamp.valueOf(LocalDateTime.now()));
        return autoIncreaseInsert(sql, params).longValue();
    }

    public long create(String name, long size) {
        @Language("SQL") String sql = "INSERT INTO file (name, size, create_at) VALUES (:NAME, :SIZE, :CREATE_AT)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("NAME", name);
        params.addValue("SIZE", size);
        params.addValue("CREATE_AT", Timestamp.valueOf(LocalDateTime.now()));
        return autoIncreaseInsert(sql, params).longValue();
    }

}
