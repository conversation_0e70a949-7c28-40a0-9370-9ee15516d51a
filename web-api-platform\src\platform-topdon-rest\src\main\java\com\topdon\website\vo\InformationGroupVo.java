package com.topdon.website.vo;

import com.topdon.website.entity.InformationGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class InformationGroupVo extends InformationGroup {

    private String productName;
    private String product_id;

    private Boolean compare;
    private String compareClassificationCode;

    private List<InformationVo> informationVos;
}
