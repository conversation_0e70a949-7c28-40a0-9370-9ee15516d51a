package com.topdon.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.classification.entity.Classification;
import com.jeesite.modules.classification.service.ClassificationService;
import com.jeesite.modules.sys.utils.UserUtils;
import com.topdon.admin.vo.ClassificationCompareVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.admin.entity.ClassificationCompare;
import com.topdon.admin.mapper.ClassificationCompareMapper;
import com.topdon.admin.service.ClassificationCompareService;

import javax.annotation.Resource;

@Service
@Slf4j
public class ClassificationCompareServiceImpl extends ServiceImpl<ClassificationCompareMapper, ClassificationCompare> implements ClassificationCompareService {

    @Resource
    private ClassificationService classificationService;


    @Override
    public List<ClassificationCompareVo> getClassificationList(Classification classification) {
        if (StringUtils.isBlank(classification.getParentCode())) {
            classification.setParentCode(Classification.ROOT_CODE);
        }
        if (StringUtils.isNotBlank(classification.getName())) {
            classification.setParentCode(null);
        }
        List<Classification> list = classificationService.findList(classification);

        log.info("list:{}", list);

        Map<String, ClassificationCompare> classificationCodeSet = new HashMap<>();

        for (ClassificationCompare classificationCompare : list()) {
            Classification classification1 = classificationService.get(classificationCompare.getClassificationCode());
            queryClassification(classification1, classificationCodeSet, classificationCompare);
        }

        return list.stream()
                .filter(classification1 -> classificationCodeSet.containsKey(classification1.getCode()))
                .map(item -> {
                    ClassificationCompareVo classificationCompareVo = new ClassificationCompareVo();
                    BeanUtils.copyProperties(item, classificationCompareVo);
                    Optional.ofNullable(classificationCodeSet.get(item.getCode()))
                            .ifPresent(classificationCompare -> {
                                classificationCompareVo.setClassificationCompareId(classificationCompare.getId());
                                classificationCompareVo.setClassificationCompareDraft(classificationCompare.getDraft());
                                classificationCompareVo.setClassificationCompareReleaseDate(classificationCompare.getReleaseDate());

                                classificationCompareVo.setClassificationCompareCreateDate(classificationCompare.getCreateDate());
                                classificationCompareVo.setClassificationCompareCreateBy(classificationCompare.getCreateBy());
                                classificationCompareVo.setClassificationCompareUpdateBy(classificationCompare.getUpdateBy());
                                classificationCompareVo.setClassificationCompareUpdateDate(classificationCompare.getUpdateDate());
                            });
                    return classificationCompareVo;
                }).collect(Collectors.toList());
    }

    @Override
    public String saveClassificationCompare(ClassificationCompare classificationCompare) {
        if (lambdaQuery()
                .eq(ClassificationCompare::getClassificationCode, classificationCompare.getClassificationCode())
                .count() > 0) {
            return "比较分类已经存在";
        }
        classificationCompare.setCreateDate(new Date());
        classificationCompare.setCreateBy(UserUtils.getLoginInfo().getId());
        classificationCompare.setUpdateBy(UserUtils.getLoginInfo().getId());
        classificationCompare.setUpdateDate(new Date());
        save(classificationCompare);
        return "";
    }

    @Override
    public void updateStatus(Integer compareId, boolean draft) {
        lambdaUpdate()
                .eq(ClassificationCompare::getId,compareId)
                .set(ClassificationCompare::getDraft,draft)
                .set(!draft,ClassificationCompare::getReleaseDate,new Date())
                .set(ClassificationCompare::getUpdateBy,UserUtils.getLoginInfo().getId())
                .set(ClassificationCompare::getUpdateDate,new Date())
                .update();
    }

    private void queryClassification(Classification classification, Map<String, ClassificationCompare> classificationCodeSet, ClassificationCompare classificationCompare) {
        if (classification == null) {
            return;
        }
        classificationCodeSet.putIfAbsent(classification.getCode(), classificationCompare);

        for (String s : classification.getParentCodes().split(",")) {
            if (StrUtil.isBlank(s) || s.equalsIgnoreCase("0")) {
                continue;
            }
            queryClassification(classificationService.get(s), classificationCodeSet, null);
        }
    }
}
