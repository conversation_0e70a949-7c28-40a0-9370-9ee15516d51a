package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 授权平台
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "authorized_platforms")
public class AuthorizedPlatforms {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 授权卖家id
     */
    @TableField(value = "seller_auth_id")
    private Integer sellerAuthId;

    /**
     * 授权平台
     */
    @TableField(value = "auth_platform")
    private String authPlatform;

    /**
     * 店铺名称
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 店铺链接
     */
    @TableField(value = "store_link")
    private String storeLink;
}