package com.hiwie.breeze.rest;

import com.hiwie.breeze.ErrorMessage;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.function.Function;
import java.util.stream.Collectors;

public class ControllerSupport {

    private static final ErrorMessage BINDING_ERROR_ = new ErrorMessage("REST", "FORM", "BINDING");

    protected static Function<BindingResult, ErrorMessage> BINDING_ERROR = result -> BINDING_ERROR_.applyParameter("fields", result.getFieldErrors().stream().collect(
            Collectors.groupingBy(
                    FieldError::getField,
                    Collectors.mapping(FieldError::getField, Collectors.toList())
            )
    ));
}
