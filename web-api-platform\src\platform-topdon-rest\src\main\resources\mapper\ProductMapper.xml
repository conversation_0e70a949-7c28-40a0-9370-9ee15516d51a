<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.ProductMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.Product">
    <!--@mbg.generated-->
    <!--@Table product-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="new_product" jdbcType="BIT" property="newProduct" />
    <result column="classification_id" jdbcType="VARCHAR" property="classificationId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="search_cover" jdbcType="VARCHAR" property="searchCover" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="search_view" jdbcType="BIT" property="searchView" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="mobile_cover" jdbcType="VARCHAR" property="mobileCover" />
    <result column="discontinued" jdbcType="BIT" property="discontinued" />
    <result column="rma_tool_show" jdbcType="BIT" property="rmaToolShow" />
    <result column="allow_purchase" jdbcType="BIT" property="allowPurchase" />
    <result column="url_us" jdbcType="VARCHAR" property="urlUs" />
    <result column="url_eu" jdbcType="VARCHAR" property="urlEu" />
    <result column="url_au" jdbcType="VARCHAR" property="urlAu" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, new_product, classification_id, description, search_cover, cover, search_view, 
    sort, create_at, mobile_cover, discontinued, rma_tool_show, allow_purchase, url_us, 
    url_eu, url_au
  </sql>

  <select id="getCompareProductList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>,
    (select sort from product_property where product_id = p.id limit 1) compare_sort
    from product p
    where p.id in (
        select product_id from product_property where classification_compare_id = (select id from classification_compare where classification_code = #{classificationCode,jdbcType=VARCHAR} limit 1) and draft = #{draftBool}
      )
    order by compare_sort
  </select>
</mapper>