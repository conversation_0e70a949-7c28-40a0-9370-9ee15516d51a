package com.topdon.website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topdon.website.entity.PropertyCategory;
import com.topdon.website.vo.PropertyCategoryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PropertyCategoryMapper extends BaseMapper<PropertyCategory> {
    List<PropertyCategory> getList(@Param("classificationCode") String classificationCode,@Param("draft") boolean draft);
}