package com.hiwie.breeze.tuples;

/**
 * <AUTHOR>
 */
public class <PERSON><PERSON>11<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11> implements Tuple {

    private final T1 _1;
    private final T2 _2;
    private final T3 _3;
    private final T4 _4;
    private final T5 _5;
    private final T6 _6;
    private final T7 _7;
    private final T8 _8;
    private final T9 _9;
    private final T10 _10;
    private final T11 _11;

    Tuple11(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6, T7 t7, T8 t8, T9 t9, T10 t10, T11 t11) {
        this._1 = t1;
        this._2 = t2;
        this._3 = t3;
        this._4 = t4;
        this._5 = t5;
        this._6 = t6;
        this._7 = t7;
        this._8 = t8;
        this._9 = t9;
        this._10 = t10;
        this._11 = t11;
    }

    public T11 _11() {
        return _11;
    }

    public T10 _10() {
        return _10;
    }

    public T9 _9() {
        return _9;
    }

    public T8 _8() {
        return _8;
    }

    public T7 _7() {
        return _7;
    }

    public T6 _6() {
        return _6;
    }

    public T5 _5() {
        return _5;
    }

    public T4 _4() {
        return _4;
    }

    public T3 _3() {
        return _3;
    }

    public T2 _2() {
        return _2;
    }

    public T1 _1() {
        return _1;
    }

    @Override
    public String toString() {
        return "(" + _1 + "," + _2 + "," + _3 + "," + _4 + "," + _5 + "," + _6 + "," + _7 + "," + _8 + "," + _9 + "," + _10 +
                "," + _11 + ")";
    }


}
