package com.topdon.admin.controller;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.topdon.admin.dto.InformationGroupTreeDTO;
import com.topdon.admin.entity.InformationGroup;
import com.topdon.admin.service.InformationGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping(value = "${adminPath}/product/informationGroup")
@Slf4j
public class InformationGroupController {

    @Resource
    private InformationGroupService informationGroupService;

    @RequiresPermissions("product:informationGroup:view")
    @GetMapping
    public String view(Model model) throws JsonProcessingException {
        return "modules/product/informationGroup";
    }

    @ResponseBody
    @PostMapping("/tree")
    public List<Tree<Integer>> tree(@RequestBody InformationGroupTreeDTO informationGroupTreeDTO) {
        return informationGroupService.getTree(informationGroupTreeDTO);
    }

    @RequiresPermissions("product:informationGroup:edit")
    @ResponseBody
    @PostMapping("/saveOrUpdate")
    public String saveOrUpdate(@RequestBody InformationGroup informationGroup) {
        Long count = informationGroupService.lambdaQuery()
                .eq(InformationGroup::getType, informationGroup.getType())
                .ne(informationGroup.getId() != null, InformationGroup::getId, informationGroup.getId())
                .eq(InformationGroup::getParentId, informationGroup.getParentId())
                .eq(informationGroup.getProductId() != null, InformationGroup::getProductId, informationGroup.getProductId())
                .isNull(informationGroup.getProductId() == null, InformationGroup::getProductId)
                .eq(InformationGroup::getName, informationGroup.getName())
                .count();
        if (count > 0) {
            return "已经存在相同的产品资料配置";
        }
        informationGroupService.saveOrUpdate(informationGroup);
        return null;
    }

    @RequiresPermissions("product:informationGroup:edit")
    @ResponseBody
    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Integer id) {
        return informationGroupService.delete(id);
    }

}
