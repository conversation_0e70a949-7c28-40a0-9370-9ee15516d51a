package com.topdon.website.model.graphql.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.topdon.website.model.CustomerAccessToken;
import lombok.Data;

import java.util.List;

@Data
@JsonRootName(value = "customerAccessTokenCreateWithMultipass")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerAccessTokenReturn {

    public static String NODE_NAME = "customerAccessTokenCreateWithMultipass";

    @JsonProperty("customerAccessToken")
    private CustomerAccessToken customerAccessToken;

    @JsonProperty("customerUserErrors")
    private List<ShopifyGraphqlError> error;
}
