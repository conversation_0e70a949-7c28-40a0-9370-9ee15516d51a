package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.BannerMapper;
import com.topdon.website.model.Banner;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class BannerRepository extends MysqlJDBCSupport {

    @Inject
    protected BannerRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<Banner> list() {
        @Language("SQL") String sql = "select b.id, image,mobile_image, name, link,mobile_link, title, `desc`, button_title from banner b order by sort ";
        return list(sql, BannerMapper.DETAIL);
    }
}
