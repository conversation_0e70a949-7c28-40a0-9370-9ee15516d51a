package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.ClassificationMapper;
import com.topdon.website.model.Classification;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class ClassificationRepository extends MysqlJDBCSupport {

    @Inject
    protected ClassificationRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<Classification> list(String parentId) {
        @Language("SQL") String sql = "select c.code, c.name, c.create_date, c.parent_code ,c1.name parent_name, c.parent_codes, c.tree_names,c.banner_media,c.menu_media,c.description,c.pro_media,c.nav_desc,(select count(0) from product_classification p where p.classification_id = c.code) product_count,(select count(0) from classification c2 where c2.parent_code = c.code) sub_count from classification c left join classification c1 on c.parent_code = c1.code where c.parent_code = :parentId order by c.tree_sort";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("parentId", parentId);
        return list(sql, ClassificationMapper.DETAIL, params);
    }

    public AbstractOption<Classification> get(String id) {
        @Language("SQL") String sql = "select c.code, c.name, c.create_date, c.parent_code ,c1.name parent_name, c.parent_codes, c.tree_names,c.banner_media,c.menu_media,c.description,c.pro_media,c.nav_desc,(select count(0) from product_classification p where p.classification_id = c.code) product_count,(select count(0) from classification c2 where c2.parent_code = c.code) sub_count from classification c left join classification c1 on c.parent_code = c1.code where c.code = :id";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("id", id);
        return option(sql, ClassificationMapper.DETAIL, params);
    }
}
