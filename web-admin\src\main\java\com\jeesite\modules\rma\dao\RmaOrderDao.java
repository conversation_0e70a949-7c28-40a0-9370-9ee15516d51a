package com.jeesite.modules.rma.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.jeesite.modules.rma.entity.RmaOrder;
import com.jeesite.modules.rma.entity.RmaOrderStatusLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RMA工单DAO接口
 * <AUTHOR>
 * @version 2024-07-10
 */
@MyBatisDao
public interface RmaOrderDao extends CrudDao<RmaOrder> {

    List<RmaOrderStatusLog> getByRmaOrderId(@Param("rmaOrderId") Integer rmaOrderId);
}