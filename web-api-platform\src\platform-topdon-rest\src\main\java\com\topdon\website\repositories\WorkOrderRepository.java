package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.form.WorkOrderCreateForm;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class WorkOrderRepository extends MysqlJDBCSupport {

    @Inject
    protected WorkOrderRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long create(WorkOrderCreateForm createForm) {
        @Language("SQL") String sql = "insert into work_order(last_name, first_name, region_id, product_id, email, serial_number,buy_channel,channel_info, `describe`, create_at,vin) values (:lastName,:firstName,:regionId,:productId,:email,:serialNumber,:buyChannel,:channelInfo,:describe,now(),:vin)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("lastName", createForm.getLastName());
        params.addValue("firstName", createForm.getFirstName());
        params.addValue("regionId", createForm.getRegionId());
        params.addValue("productId", createForm.getProductId());
        params.addValue("email", createForm.getEmail());
        params.addValue("serialNumber", createForm.getSerialNumber());
        params.addValue("buyChannel", createForm.getBuyChannel());
        params.addValue("channelInfo", createForm.getChannelInfo());
        params.addValue("describe", createForm.getDescribe());
        params.addValue("vin", createForm.getVin());
        return autoIncreaseInsert(sql, params).longValue();
    }
}
