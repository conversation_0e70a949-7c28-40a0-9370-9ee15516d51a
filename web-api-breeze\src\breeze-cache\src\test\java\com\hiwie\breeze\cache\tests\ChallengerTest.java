package com.hiwie.breeze.cache.tests;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.cache.Cache;
import com.hiwie.breeze.cache.Challenger;
import com.hiwie.breeze.util.StringUtil;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import redis.clients.jedis.JedisPool;

import java.time.Duration;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
@DisplayName("Challenger test")
@RunWith(JUnitPlatform.class)
class ChallengerTest {

    private static Cache cache;

    private static Challenger challenger;

    @BeforeAll
    static void init() {
        JedisPool pool = new JedisPool(new GenericObjectPoolConfig(), "dev.11ejia.com", 6379, 2000, "ahuwqoiye12c7b9484c2b94", 0, "breeze_test");
        cache = new Cache(pool);
        challenger = new Challenger("test.challenger", 0, Duration.parse("PT5S"), () -> StringUtil.secureRandom(6, StringUtil.Encoder.NUMBER), 3, Duration.parse("PT3S"), true);
    }

    @Test
    @DisplayName("prepare secret")
    void prepare() {
        AbstractEither<ErrorMessage, String> either = cache.apply(challenger.prepare(UUID.randomUUID().toString()));
        assertTrue(either.isRight());
    }

    @Test
    @DisplayName("challenge secret")
    void challenge() {
        String key = UUID.randomUUID().toString();
        AbstractEither<ErrorMessage, String> either = cache.apply(challenger.prepare(key));
        assertTrue(either.isRight());
        String secret = either.right().get();
        AbstractOption<ErrorMessage> option = cache.apply(challenger.challenge(key, secret));
        assertTrue(option.isEmpty());
    }

}
