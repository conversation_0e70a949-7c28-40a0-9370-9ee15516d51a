package com.topdon.website.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class ShopifyCartBuyer {
    public static String NODE_NAME = "buyerIdentity";
    @JsonIgnore
    private String customerAccessToken;
    private String email;
    @JsonIgnore
    private String phone;

    public ShopifyCartBuyer(String userId, String email) {
        this.customerAccessToken = userId;
        this.email = email;
    }

    public ShopifyCartBuyer(String email) {
        this.email = email;
    }

    public String getCustomerAccessToken() {
        return customerAccessToken;
    }

    public void setCustomerAccessToken(String customerAccessToken) {
        this.customerAccessToken = customerAccessToken;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
