package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.FooterMapper;
import com.topdon.website.model.FooterMenu;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class FooterMenuRepository extends MysqlJDBCSupport {

    @Inject
    protected FooterMenuRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<FooterMenu> list(String parentCode) {
        @Language("SQL") String sql = "select f.code, name, link,mobile_link, tree_names from footer_menu f where parent_code = :parentCode order by f.tree_sort";
        MapSqlParameterSource param = new MapSqlParameterSource("parentCode", parentCode);
        return list(sql, FooterMapper.DETAIL, param);
    }
}
