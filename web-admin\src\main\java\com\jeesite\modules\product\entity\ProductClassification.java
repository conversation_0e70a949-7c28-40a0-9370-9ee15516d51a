package com.jeesite.modules.product.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 产品分类信息Entity
 * <AUTHOR>
 * @version 2024-05-13
 */
@Table(name="product_classification", alias="a", label="产品分类信息信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="product_id", attrName="productId", label="产品id"),
		@Column(name="classification_id", attrName="classificationId", label="分类Id"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
	}, orderBy="a.id DESC"
)
public class ProductClassification extends DataEntity<ProductClassification> {
	
	private static final long serialVersionUID = 1L;
	private String productId;		// 产品id
	private String classificationId;		// 分类Id
	
	public ProductClassification() {
		this(null);
	}

	public ProductClassification(String id){
		super(id);
	}
	
	@Size(min=0, max=40, message="产品id长度不能超过 40 个字符")
	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}
	
	@Size(min=0, max=64, message="分类Id长度不能超过 64 个字符")
	public String getClassificationId() {
		return classificationId;
	}

	public void setClassificationId(String classificationId) {
		this.classificationId = classificationId;
	}
	
}