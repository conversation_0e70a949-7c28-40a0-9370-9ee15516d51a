package com.topdon.admin.service;

import com.jeesite.modules.classification.entity.Classification;
import com.topdon.admin.entity.ClassificationCompare;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.vo.ClassificationCompareVo;

import java.util.List;

public interface ClassificationCompareService extends IService<ClassificationCompare>{

    List<ClassificationCompareVo> getClassificationList(Classification classification);

    String saveClassificationCompare(ClassificationCompare classificationCompare);

    void updateStatus(Integer compareId, boolean draft);
}
