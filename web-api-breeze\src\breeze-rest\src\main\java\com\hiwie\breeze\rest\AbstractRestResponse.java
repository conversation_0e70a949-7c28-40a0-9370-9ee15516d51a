package com.hiwie.breeze.rest;

import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.util.ObjectUtil;

import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractRestResponse {

    private Map<String, Object> meta;

    public static AbstractRestResponse apply(AbstractOption<ErrorMessage> errorMessageOption) {
        return errorMessageOption.<AbstractRestResponse>map(ErrorRestResponse::new).getOrElse(SuccessRestResponse.EMPTY);
    }

    public static <T> AbstractRestResponse apply(AbstractEither<ErrorMessage, T> either) {
        return either.fold(
                ErrorRestResponse::new,
                SuccessRestResponse::new
        );
    }

    public AbstractRestResponse addMeta(String key, Object value) {
        if (ObjectUtil.isNull(meta)) {
            this.meta = Maps.newHashMap();
        }
        this.meta.put(key, value);
        return this;
    }

    public Map<String, Object> getMeta() {
        return this.meta;
    }

    public AbstractRestResponse addMeta(Map<String, Object> meta) {
        if (ObjectUtil.isNull(this.meta)) {
            this.meta = meta;
        } else {
            this.meta.putAll(meta);
        }
        return this;
    }

}
