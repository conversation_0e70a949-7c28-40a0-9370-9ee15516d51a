package com.topdon.website.mappers;

import com.topdon.website.model.Classification;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class ClassificationMapper {

    public static final RowMapper<Classification> DETAIL = (rs, index) -> {
        Classification detail = new Classification();
        detail.setId(rs.getString("code"));
        detail.setName(rs.getString("name"));
        detail.setParent(Classification.defaultPk(rs.getString("parent_code"), rs.getString("parent_name")));
        detail.setCreateAt(rs.getTimestamp("create_date").toLocalDateTime());
        detail.setIdPath(rs.getString("parent_codes"));
        detail.setNamePath(rs.getString("tree_names"));
        detail.setProductCount(rs.getLong("product_count"));
        detail.setBannerMedia(rs.getString("banner_media"));
        detail.setMenuMedia(rs.getString("menu_media"));
        detail.setProMedia(rs.getString("pro_media"));
        detail.setNavDesc(rs.getString("nav_desc"));
        detail.setDescription(rs.getString("description"));
        detail.setHasSub(rs.getLong("sub_count") > 0);
        return detail;
    };

}
