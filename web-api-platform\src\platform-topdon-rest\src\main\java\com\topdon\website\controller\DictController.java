package com.topdon.website.controller;

import com.hiwie.breeze.Right;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.services.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/7 17:00
 */
@RestController
@RequestMapping("dict")
public class DictController {

    @Autowired
    private DictService dictService;

    @PostMapping("/{type}")
    public AbstractRestResponse getDict(@PathVariable String type) {
        return AbstractRestResponse.apply(dictService.getDictByType(type));
    }

    @PostMapping("/cleanDict/{type}")
    public AbstractRestResponse cleanCacheDict(@PathVariable String type) {
        dictService.cleanType(type);
        return AbstractRestResponse.apply(Right.apply(Boolean.TRUE));
    }
}
