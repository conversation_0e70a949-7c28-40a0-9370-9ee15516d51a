package com.hiwie.breeze;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public abstract class AbstractEither<A, B> {

    public static <X, Y> AbstractEither<X, Y> cond(boolean test, Supplier<X> left, Supplier<Y> right) {
        return test ? Right.apply(right.get()) : Left.apply(left.get());
    }

    public final LeftProjection<A, B> left() {
        return new LeftProjection<>(this);
    }

    public final RightProjection<A, B> right() {
        return new RightProjection<>(this);
    }

    public abstract Boolean isLeft();

    public abstract Boolean isRight();

    public AbstractEither<A, B> ifLeft(Consumer<A> consumer) {
        if (isLeft()) {
            consumer.accept(getLeft());
        }
        return this;
    }

    public AbstractEither<A, B> ifRight(Consumer<B> consumer) {
        if (isRight()) {
            consumer.accept(getRight());
        }
        return this;
    }

    public <C> C fold(Function<A, C> fa, Function<B, C> fb) {
        return isLeft() ? fa.apply(this.left().get()) : fb.apply(this.right().get());
    }

    public AbstractEither<A, B> accept(Consumer<A> ca, Consumer<B> cb) {
        if (isLeft()) {
            ca.accept(this.left().get());
        } else {
            cb.accept(this.right().get());
        }
        return this;
    }

    public AbstractEither<B, A> swap() {
        return isRight() ? Left.apply(this.right().get()) : Right.apply(this.left().get());
    }

    public B getOrElse(Supplier<B> or) {
        return isRight() ? getRight() : or.get();
    }

    public boolean contains(B ele) {
        return isRight() && getRight().equals(ele);
    }

    public boolean forall(Function<B, Boolean> f) {
        return isRight() ? f.apply(getRight()) : true;
    }

    public boolean exists(Function<B, Boolean> p) {
        return isRight() ? p.apply(getRight()) : false;
    }

    public <Y> AbstractEither<A, Y> flatMap(Function<B, AbstractEither<A, Y>> f) {
        return isRight() ? f.apply(getRight()) : Left.apply(getLeft());
    }

    public <Y> AbstractEither<A, Y> map(Function<B, Y> f) {
        return isRight() ? Right.apply(f.apply(getRight())) : Left.apply(getLeft());
    }

    public AbstractEither<A, B> filterOrElse(Function<B, Boolean> p, Supplier<A> zero) {
        return isRight() ? (p.apply(getRight()) ? this : Left.apply(zero.get())) : this;
    }

    public List<B> toList() {
        if (isRight()) {
            List<B> list = new ArrayList<>(1);
            list.add(getRight());
            return list;
        } else {
            return new ArrayList<>(0);
        }
    }

    public AbstractOption<B> toOption() {
        return isRight() ? Some.apply(getRight()) : None.apply();
    }

    protected abstract A getLeft();

    protected abstract B getRight();

    public static final class LeftProjection<LA, LB> {

        private final AbstractEither<LA, LB> e;

        private LeftProjection(AbstractEither<LA, LB> e) {
            this.e = e;
        }

        public LA get() {
            return e.getLeft();
        }

        public void foreach(Consumer<LA> func) {
            if (e.isLeft()) {
                func.accept(e.getLeft());
            }
        }

        public LA getOrElse(Supplier<LA> or) {
            return e.isLeft() ? e.getLeft() : or.get();
        }

        public boolean forall(Function<LA, Boolean> p) {
            return e.isLeft() ? p.apply(e.getLeft()) : true;
        }

        public boolean exists(Function<LA, Boolean> p) {
            return e.isLeft() ? p.apply(e.getLeft()) : false;
        }

        public <X> AbstractEither<X, LB> flatMap(Function<LA, AbstractEither<X, LB>> f) {
            return e.isLeft() ? f.apply(e.getLeft()) : Right.apply(e.getRight());
        }

        public <X> AbstractEither<X, LB> map(Function<LA, X> f) {
            return e.isLeft() ? Left.apply(f.apply(e.getLeft())) : Right.apply(e.getRight());
        }

        public AbstractOption<AbstractEither<LA, LB>> filter(Function<LA, Boolean> p) {
            return e.isLeft() && p.apply(e.getLeft()) ? Some.apply(e) : None.apply();
        }

        public List<LA> toList() {
            if (e.isLeft()) {
                List<LA> list = new ArrayList<>(1);
                list.add(e.getLeft());
                return list;
            } else {
                return new ArrayList<>(0);
            }
        }

        public AbstractOption<LA> toOption() {
            return e.isLeft() ? Some.apply(e.getLeft()) : None.apply();
        }

    }

    public static final class RightProjection<RA, RB> {

        private final AbstractEither<RA, RB> e;

        private RightProjection(AbstractEither<RA, RB> e) {
            this.e = e;
        }

        public RB get() {
            return e.getRight();
        }

        public void foreach(Consumer<RB> func) {
            if (e.isRight()) {
                func.accept(e.getRight());
            }
        }

        public RB getOrElse(Supplier<RB> or) {
            return e.isRight() ? e.getRight() : or.get();
        }

        public boolean forall(Function<RB, Boolean> f) {
            return e.isRight() ? f.apply(e.getRight()) : true;
        }

        public boolean exists(Function<RB, Boolean> p) {
            return e.isRight() ? p.apply(e.getRight()) : false;
        }

        public <Y> AbstractEither<RA, Y> flatMap(Function<RB, AbstractEither<RA, Y>> f) {
            return e.isRight() ? f.apply(e.getRight()) : Left.apply(e.getLeft());
        }

        public <Y> AbstractEither<RA, Y> map(Function<RB, Y> f) {
            return e.isRight() ? Right.apply(f.apply(e.getRight())) : Left.apply(e.getLeft());
        }

        public AbstractOption<AbstractEither<RA, RB>> filter(Function<RB, Boolean> p) {
            return e.isRight() && p.apply(e.getRight()) ? Some.apply(e) : None.apply();
        }

        public List<RB> toList() {
            if (e.isRight()) {
                List<RB> list = new ArrayList<>(1);
                list.add(e.getRight());
                return list;
            } else {
                return new ArrayList<>(0);
            }
        }

        public AbstractOption<RB> toOption() {
            return e.isRight() ? Some.apply(e.getRight()) : None.apply();
        }
    }

}
