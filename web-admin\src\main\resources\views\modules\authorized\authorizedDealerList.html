<% layout('/layouts/default.html', {title: '经销商管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('经销商管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('authorized:authorizedDealer:edit')){ %>
					<a href="${ctx}/authorized/authorizedDealer/form" class="btn btn-default btnTool" title="${text('新增经销商')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${authorizedDealer}" action="${ctx}/authorized/authorizedDealer/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('经销商名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="40" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("经销商名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/authorized/authorizedDealer/form?id='+row.id+'" class="btnList" data-title="${text("编辑经销商")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("显示封面")}', name:'cover', index:'a.cover', width:150, align:"left",formatter:function (val,obj,row,act) {
				if (val){
					return "<img width='100%' src=" + encodeURI(val) + " />";
				}else{
					return "缺失";
				}

			}},
		{header:'${text("地区")}', name:'region.name', index:'a.region_id', width:150, align:"left"},
		{header:'${text("电话")}', name:'phone', index:'a.phone', width:150, align:"left"},
		{header:'${text("邮箱")}', name:'email', index:'a.email', width:150, align:"left"},
		{header:'${text("官网链接")}', name:'link', index:'a.link', width:150, align:"left"},
		{header:'${text("地址")}', name:'address', index:'a.address', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('authorized:authorizedDealer:edit')){ %>
				actions.push('<a href="${ctx}/authorized/authorizedDealer/form?id='+row.id+'" class="btnList" title="${text("编辑经销商")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/authorized/authorizedDealer/delete?id='+row.id+'" class="btnList" title="${text("删除经销商")}" data-confirm="${text("确认要删除该经销商吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>