package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.SubscribeForm;
import com.topdon.website.services.EmailSubscribeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("subscribe")
public class EmailSubscribeController extends ControllerSupport {

    private final EmailSubscribeService emailSubscribeService;

    @Autowired
    public EmailSubscribeController(EmailSubscribeService emailSubscribeService) {
        this.emailSubscribeService = emailSubscribeService;
    }

    @PostMapping()
    public AbstractRestResponse create(SubscribeForm from) {
        return AbstractRestResponse.apply(emailSubscribeService.create(from));
    }

    @PostMapping("copiedDiscountCode")
    public AbstractRestResponse copiedDiscountCode(SubscribeForm from) {
        return AbstractRestResponse.apply(emailSubscribeService.copiedDiscountCode(from));
    }
}
