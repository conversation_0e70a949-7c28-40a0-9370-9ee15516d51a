package com.topdon.website.form;

import com.hiwie.breeze.AbstractOption;
import com.topdon.website.model.Recruitment;

public class RecruitmentQueryForm {
    private Recruitment.Type type;
    private Recruitment.Category category;
    private String position;
    private String name;

    public Recruitment.Type getType() {
        return type;
    }

    public void setType(Recruitment.Type type) {
        this.type = type;
    }

    public AbstractOption<Recruitment.Category> getCategory() {
        return AbstractOption.apply(category);
    }

    public void setCategory(Recruitment.Category category) {
        this.category = category;
    }

    public AbstractOption<String> getPosition() {
        return AbstractOption.apply(position);
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public AbstractOption<String> getName() {
        return AbstractOption.apply(name);
    }

    public void setName(String name) {
        this.name = name;
    }
}
