package com.hiwie.breeze.cache;

import com.hiwie.breeze.*;
import com.hiwie.breeze.util.AssertUtil;
import com.hiwie.breeze.util.StringUtil;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;

import java.time.Duration;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Challenger
 *
 * <AUTHOR>
 */
public class Challenger extends AbstractCache {

    public static final String ENTITY_NAME = "CHALLENGER";

    private static final String SECRET_FIELD_NAME = "SECRET";
    private static final String ATTEMPTS_FIELD_NAME = "ATTEMPTS";
    private static final String IS_ATTEMPT_DISABLED_FIELD_NAME = "IS_ATTEMPT_DISABLED";
    private static final String NOT_PREPARED = "NOT_PREPARED";
    private final Supplier<String> secretGenerator;
    private final int attemptLimit;
    private final boolean reuseSecret;
    private final int attemptDisabledCountDownSeconds;

    public Challenger(String prefix, int db, Duration expireDuration, Supplier<String> secretGenerator, int attemptLimit, Duration attemptDisabledCountDownDuration, boolean reuseSecret) {
        super(StringUtil.join(ENTITY_NAME, PREFIX_SEPARATOR, prefix), db, Some.apply(expireDuration));
        AssertUtil.isTrue(attemptDisabledCountDownDuration.getSeconds() <= Integer.MAX_VALUE, "attempt disabled count down duration can NOT larger than " + Integer.MAX_VALUE);
        this.secretGenerator = secretGenerator;
        this.attemptLimit = attemptLimit;
        this.attemptDisabledCountDownSeconds = (int) attemptDisabledCountDownDuration.getSeconds();
        this.reuseSecret = reuseSecret;
    }

    public Function<Jedis, AbstractEither<ErrorMessage, String>> prepare(String key) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);
            String theSecret;
            int theAttempts;
            int theExpireSeconds;

            Pipeline p1 = jedis.pipelined();
            p1.select(db);
            Response<String> dbSecretResponse = p1.hget(theKey, SECRET_FIELD_NAME);
            Response<String> dbAttemptsResponse = p1.hget(theKey, ATTEMPTS_FIELD_NAME);
            Response<String> dbIsAttemptDisabledResponse = p1.hget(theKey, IS_ATTEMPT_DISABLED_FIELD_NAME);
            p1.sync();

            if (StringUtil.isEmpty(dbSecretResponse.get()) || StringUtil.isEmpty(dbIsAttemptDisabledResponse.get()) || StringUtil.isEmpty(dbAttemptsResponse.get())) {
                theSecret = secretGenerator.get();
                theAttempts = 0;
                theExpireSeconds = expireSeconds;
            } else {
                boolean dbIsAttemptDisabled = Boolean.valueOf(dbIsAttemptDisabledResponse.get());
                int dbAttempts = Integer.valueOf(dbAttemptsResponse.get());
                if (dbIsAttemptDisabled) {
                    return Left.apply(Errors.ATTEMPT_LIMIT_REACHED);
                } else {
                    if (reuseSecret) {
                        theSecret = dbSecretResponse.get();
                    } else {
                        theSecret = secretGenerator.get();
                    }
                    theExpireSeconds = expireSeconds;
                    theAttempts = dbAttempts;
                }
            }

            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            pipeline.hset(theKey, SECRET_FIELD_NAME, theSecret);
            pipeline.hset(theKey, ATTEMPTS_FIELD_NAME, String.valueOf(theAttempts));
            pipeline.hset(theKey, IS_ATTEMPT_DISABLED_FIELD_NAME, Boolean.toString(false));
            pipeline.expire(theKey, theExpireSeconds);
            pipeline.sync();

            return Right.apply(theSecret);
        };
    }

    public Function<Jedis, AbstractOption<ErrorMessage>> challenge(String key, String secret) {
        return (Jedis jedis) -> {
            String theKey = generateKey(key);

            Pipeline p1 = jedis.pipelined();
            p1.select(db);
            Response<String> dbSecretResponse = p1.hget(theKey, SECRET_FIELD_NAME);
            Response<String> dbAttemptsResponse = p1.hget(theKey, ATTEMPTS_FIELD_NAME);
            Response<String> dbIsAttemptDisabledResponse = p1.hget(theKey, IS_ATTEMPT_DISABLED_FIELD_NAME);
            Response<Long> dbTTLResponse = p1.ttl(theKey);
            p1.sync();

            String dbSecret = dbSecretResponse.get();

            int theExpireSeconds;
            int theAttempts;
            boolean theIsAttemptDisabled;
            String theSecret;

            if (StringUtil.isEmpty(dbSecret) || StringUtil.isEmpty(dbIsAttemptDisabledResponse.get()) || StringUtil.isEmpty(dbAttemptsResponse.get())) {
                theSecret = NOT_PREPARED;
                theAttempts = 1;
                theIsAttemptDisabled = false;
                theExpireSeconds = expireSeconds;
            } else {
                int dbAttempts = Integer.valueOf(dbAttemptsResponse.get());
                boolean dbIsAttemptDisabled = Boolean.valueOf(dbIsAttemptDisabledResponse.get());
                if (dbIsAttemptDisabled) {
                    return Some.apply(Errors.ATTEMPT_LIMIT_REACHED);
                } else if (StringUtil.equals(dbSecret, secret) && !StringUtil.equals(NOT_PREPARED, dbSecret)) {
                    jedis.del(theKey);
                    return None.apply();
                } else {
                    theSecret = dbSecret;
                    int nowAttempts = dbAttempts + 1;
                    if (nowAttempts > attemptLimit) {
                        theExpireSeconds = attemptDisabledCountDownSeconds;
                        theAttempts = nowAttempts;
                        theIsAttemptDisabled = true;
                    } else {
                        theExpireSeconds = dbTTLResponse.get().intValue();
                        theAttempts = nowAttempts;
                        theIsAttemptDisabled = false;
                    }
                }
            }

            Pipeline p2 = jedis.pipelined();
            p2.select(db);
            p2.hset(theKey, SECRET_FIELD_NAME, theSecret);
            p2.hset(theKey, ATTEMPTS_FIELD_NAME, String.valueOf(theAttempts));
            p2.hset(theKey, IS_ATTEMPT_DISABLED_FIELD_NAME, Boolean.toString(theIsAttemptDisabled));
            p2.expire(theKey, theExpireSeconds);
            p2.sync();
            return Some.apply(Errors.SECRET_NOT_MATCH);
        };
    }

    public static class Errors {

        public static final ErrorMessage SECRET_NOT_MATCH = new ErrorMessage(CacheConstants.MODULE, ENTITY_NAME, "SECRET_NOT_MATCH");

        public static final ErrorMessage ATTEMPT_LIMIT_REACHED = new ErrorMessage(CacheConstants.MODULE, ENTITY_NAME, "ATTEMPT_LIMIT_REACHED");

    }

}