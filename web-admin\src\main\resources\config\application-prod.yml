productName: Topdon官网后台管理系统
companyName: 联科科技
productVersion: V4.3
copyrightYear: 2022
demoMode: false

server:
  port: 8980
  servlet:
    context-path: /admin
  tomcat:
    uri-encoding: UTF-8
    max-http-form-post-size: 20MB
  schemeHttps: false

jdbc:
  type: mysql
  driver: com.mysql.cj.jdbc.Driver
  url: *********************************************************************************************************************************************************
  username: tp-user
  password: fae2061dea6ab681ed40ef33af9f54ea
  testSql: SELECT 1
spring:
  application:
    name: jeesite-web
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
shiro:
  rememberMe:
    secretKey: RtqFt0/TxYXskUJkiFp5WA==
file:
  enabled: true
  client:
    aliyun:
      endpoint: oss-accelerate.aliyuncs.com
      accessKeyId: LTAI5tPBweQbPPnW673y1FCy
      accessKeySecret: ******************************
      bucketName: lenkor-plat
      root: topdon-web/
      proxyEndpoint: web-file.topdon.com
  imageMaxWidth: -1
  imageMaxHeight: -1
  email:
    beanName: emailSendService
    fromAddress: <EMAIL>
    fromPassword: 55bedd8ec71e80dfed00
    fromHostName: smtp.139.com
    sslOnConnect: true
    sslSmtpPort: 465
  maxFileSize: 800*1024*1024

#视频转码
video:
  #
  #  # 视频格式转换  ffmpeg.exe 所放的路径
  ffmpegFile: /usr/local/bin/ffmpeg

logging:
  level:
    root: info
  config: classpath:config/logback-server.xml
  file:
    path: logs