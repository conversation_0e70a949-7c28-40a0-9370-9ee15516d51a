package com.topdon.admin.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import lombok.Data;

import java.util.Date;

@Data
public class EmailSubscribeExcelVo {

    @ExcelFields({
            @ExcelField(title = "电子邮箱", attrName = "email", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "订阅来源", attrName = "site", align = ExcelField.Align.CENTER, dictType = "site_from", sort = 20),
            @ExcelField(title = "功能来源", attrName = "from", align = ExcelField.Align.CENTER, dictType = "from", sort = 30),
            @ExcelField(title = "订阅时间", attrName = "createAt", align = ExcelField.Align.CENTER, sort = 40, dataFormat = "yyyy-MM-dd HH:mm:ss"),
            @ExcelField(title = "复制折扣码", attrName = "copiedDiscountCodeStr", align = ExcelField.Align.CENTER, sort = 50)
    })
    public EmailSubscribeExcelVo() {
    }

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField(value = "email")
    private String email;

    @TableField(value = "`from`")
    private String from;

    @TableField(value = "create_at")
    private Date createAt;

    @TableField(value = "site")
    private String site;

    @TableField(value = "copied_discount_code")
    private Integer copiedDiscountCode;

    public String getCopiedDiscountCodeStr() {
        if (copiedDiscountCode == null) {
            return "";
        } else if (copiedDiscountCode == 0) {
            return "否";
        } else {
            return "是";
        }
    }
}