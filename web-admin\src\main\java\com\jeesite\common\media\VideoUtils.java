//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.jeesite.common.media;

import cn.hutool.core.util.StrUtil;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.image.ImageUtils;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.io.PropertiesUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.lang.TimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class VideoUtils {
    private static final Logger log = LoggerFactory.getLogger(VideoUtils.class);
    private static String ffmpegFile;
    private static String mencoderFile;
    private static String qtFaststartFile;
    private String inputFile;
    private String inputFileExtension;
    private String outputFile;
    private String outputFileExtension;
    private String imgFile;
    private String imgFileExtension;
    private String width;
    private String height;
    private boolean status;

    public VideoUtils(String inputFile) {
        this(inputFile, (String) null, (String) null);
    }

    public VideoUtils(String inputFile, String outputFile, String imgFile) {
        this.inputFile = "";
        this.inputFileExtension = "";
        this.outputFile = "";
        this.outputFileExtension = "mp4";
        this.imgFile = "";
        this.imgFileExtension = "jpg";
        this.width = null;
        this.height = null;
        this.status = false;
        String inputFileAbsolutePath = new File(inputFile).getAbsolutePath();
        this.inputFile = inputFileAbsolutePath;
        this.inputFileExtension = FileUtils.getFileExtension(inputFileAbsolutePath);
        this.outputFile = outputFile != null ? new File(outputFile).getAbsolutePath() : inputFileAbsolutePath;
        this.imgFile = imgFile != null ? imgFile : StrUtil.replaceLast(inputFileAbsolutePath, this.inputFileExtension, this.imgFileExtension);
        this.status = this.checkfile(inputFile);
    }

    public VideoUtils(String inputFile, String outputFile, String imgFile, String width, String height) {
        this(inputFile, outputFile, imgFile);
        this.width = width;
        this.height = height;
    }

    private int checkContentType() {
        if (StringUtils.inString(this.inputFileExtension, new String[]{"avi", "mpg", "wmv", "3gp", "mov", "mp4", "asf", "asx", "flv", "rm", "rmvb"})) {
            return 0;
        } else {
            return StringUtils.inString(this.inputFileExtension, new String[]{"wmv9"}) ? 1 : 9;
        }
    }

    public boolean cutPic() {
        long startTime = System.currentTimeMillis();
        boolean statusTemp = this.status;
        if (statusTemp) {
            statusTemp = this.processFfmpegCutpic(this.inputFile, this.outputFile);

            try {
                File imgfile = new File(this.imgFile);
                if (imgfile.exists()) {
                    ImageUtils.thumbnails(imgfile, 800, 600, (String) null);
                } else {
                    statusTemp = false;
                }
            } catch (Exception e) {
                statusTemp = false;
                log.error("视频剪切图片失败", e);
            }
        }

        log.info("视频剪切图片" + (statusTemp ? "成功" : "失败") + "，用时：" + TimeUtils.formatDateAgo(System.currentTimeMillis() - startTime));
        return statusTemp;
    }

    public boolean convert() {
        long startTime = System.currentTimeMillis();
        boolean statusTemp = this.status;
        int type = this.checkContentType();
        String tempFile = this.outputFile + ".tmp";
        if (statusTemp && type == 0) {
            log.info("使用ffmpage进行视频转换");
            statusTemp = this.processFfmpeg(this.inputFile, tempFile);
        } else if (statusTemp && type == 1) {
            log.info("使用mencoder进行视频转换");
            statusTemp = this.processMencoder(this.inputFile, tempFile);
        }

        if (statusTemp) {
            log.info("将mp4视频的元数据信息转到视频第一帧");
            statusTemp = this.processQtFaststart(tempFile, this.outputFile);
        }

        log.info("删除临时文件");
        FileUtils.deleteFile(tempFile);
        log.info("视频转换" + (statusTemp ? "成功" : "失败") + "，用时：" + TimeUtils.formatDateAgo(System.currentTimeMillis() - startTime));
        return statusTemp;
    }

    public boolean checkfile(String inputFile) {
        File file = new File(inputFile);
        if (file.isFile() && file.exists()) {
            return true;
        } else {
            log.warn("文件不存在！");
            return false;
        }
    }

    public boolean processFfmpegCutpic(String inputFile, String outputFile) {
        List<String> command = new ArrayList();
        command.add(getFfmpegFile());
        command.add("-i");
        command.add(inputFile);
        if (this.imgFileExtension.toLowerCase().equals("gif")) {
            command.add("-vframes");
            command.add("30");
            command.add("-f");
            command.add("gif");
        } else {
            command.add("-ss");
            command.add("4");
            command.add("-t");
            command.add("0.001");
            command.add("-f");
            command.add("image2");
        }

        command.add("-y");
        command.add(this.imgFile);
        return this.process(command);
    }

    private boolean processFfmpeg(String inputFile, String outputFile) {
        List<String> command = new ArrayList();
        command.add(getFfmpegFile());
        command.add("-i");
        command.add(inputFile);
        command.add("-f");
        command.add(this.outputFileExtension);
        command.add("-c:v");
        command.add("libx264");
        command.add("-b:v");
        command.add("600k");
        command.add("-g");
        command.add("300");
        command.add("-bf");
        command.add("2");
        command.add("-c:a");
        command.add("aac");
        command.add("-strict");
        command.add("experimental");
        command.add("-ac");
        command.add("1");
        command.add("-ar");
        command.add("44100");
        command.add("-r");
        command.add("29.97");
        command.add("-qscale");
        command.add("6");
        if (StringUtils.isNotBlank(this.width) && StringUtils.isNotBlank(this.height)) {
            command.add("-s");
            command.add(this.width + "x" + this.height);
        }

        command.add("-y");
        command.add(outputFile);
        return this.process(command);
    }

    private boolean processMencoder(String inputFile, String outputFile) {
        List<String> command = new ArrayList();
        command.add(getMencoderFile());
        command.add(inputFile);
        command.add("-oac");
        command.add("mp3lame");
        command.add("-lameopts");
        command.add("aq=7:vbr=2:q=6");
        command.add("-srate");
        command.add("44100");
        if (StringUtils.isNotBlank(this.width) && StringUtils.isNotBlank(this.height)) {
            command.add("-vf");
            command.add("scale=" + this.width + ":" + this.height + ",harddup");
        }

        command.add("-ovc");
        command.add("xvid");
        command.add("-xvidencopts");
        command.add("fixed_quant=8");
        command.add("-of");
        command.add("lavf");
        command.add("-o");
        command.add(outputFile);
        return this.process(command);
    }

    private boolean processQtFaststart(String inputFile, String outputFile) {
        List<String> command = new ArrayList();
        command.add(getQtFaststartFile());
        command.add(inputFile);
        command.add(outputFile);
        return this.process(command);
    }

    private boolean process(List<String> command) {
        try {
            log.info(ListUtils.convertToString(command, " "));
            Process process = Runtime.getRuntime().exec((String[]) command.toArray(new String[command.size()]));
            (new PrintErrorReader(process.getErrorStream())).start();
            (new PrintInputStream(process.getInputStream())).start();
            process.waitFor();
            return true;
        } catch (Exception e) {
            if (StringUtils.contains(e.getMessage(), "CreateProcess error=2")) {
                log.error("缺少视频转换工具，请配置video.ffmpegFile相关参数。" + e.getMessage());
            } else {
                log.error(e.getMessage(), e);
            }

            return false;
        }
    }

    public static String getFfmpegFile() {
        if (ffmpegFile == null) {
            ffmpegFile = PropertiesUtils.getInstance().getProperty("video.ffmpegFile");
        }

        return ffmpegFile;
    }

    public static void setFfmpegFile(String ffmpegFile) {
        VideoUtils.ffmpegFile = ffmpegFile;
    }

    public static String getMencoderFile() {
        if (mencoderFile == null) {
            mencoderFile = PropertiesUtils.getInstance().getProperty("video.mencoderFile");
        }

        return mencoderFile;
    }

    public static void setMencoderFile(String mencoderFile) {
        VideoUtils.mencoderFile = mencoderFile;
    }

    public static String getQtFaststartFile() {
        if (qtFaststartFile == null) {
            qtFaststartFile = PropertiesUtils.getInstance().getProperty("video.qtFaststartFile");
        }

        return qtFaststartFile;
    }

    public static void setQtFaststartFile(String qtFaststartFile) {
        VideoUtils.qtFaststartFile = qtFaststartFile;
    }

    public String getInputFile() {
        return this.inputFile;
    }

    public void setInputFile(String inputFile) {
        this.inputFile = new File(inputFile).getAbsolutePath();
    }

    public String getInputFileExtension() {
        return this.inputFileExtension;
    }

    public void setInputFileExtension(String inputFileExtension) {
        this.inputFileExtension = inputFileExtension;
    }

    public String getOutputFile() {
        return this.outputFile;
    }

    public void setOutputFile(String outputFile) {
        this.outputFile = new File(outputFile).getAbsolutePath();
    }

    public String getOutputFileExtension() {
        return this.outputFileExtension;
    }

    public void setOutputFileExtension(String outputFileExtension) {
        this.outputFileExtension = outputFileExtension;
    }

    public String getImgFile() {
        return this.imgFile;
    }

    public void setImgFile(String imgFile) {
        this.imgFile = imgFile;
    }

    public String getImgFileExtension() {
        return this.imgFileExtension;
    }

    public void setImgFileExtension(String imgFileExtension) {
        this.imgFileExtension = imgFileExtension;
    }

    public String getWidth() {
        return this.width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return this.height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    class PrintInputStream extends Thread {
        InputStream __is = null;

        public PrintInputStream(InputStream is) {
            this.__is = is;
        }

        public void run() {
            try {
                BufferedReader br = new BufferedReader(new InputStreamReader(this.__is));
                String line = null;

                while ((line = br.readLine()) != null) {
                    VideoUtils.log.info(line);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    class PrintErrorReader extends Thread {
        InputStream __is = null;

        public PrintErrorReader(InputStream is) {
            this.__is = is;
        }

        public void run() {
            try {
                BufferedReader br = new BufferedReader(new InputStreamReader(this.__is));
                String line = null;

                while ((line = br.readLine()) != null) {
                    VideoUtils.log.error(line);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }
}
