//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.jeesite.common.ueditor.upload;

import com.jeesite.common.image.ImageUtils;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.media.VideoUtils;
import com.jeesite.common.ueditor.PathFormat;
import com.jeesite.common.ueditor.define.BaseState;
import com.jeesite.common.ueditor.define.FileType;
import com.jeesite.common.ueditor.define.State;
import org.apache.commons.fileupload.FileItemIterator;
import org.apache.commons.fileupload.FileItemStream;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class BinaryUploader {
    public BinaryUploader() {
    }

    public static final State save(HttpServletRequest request, Map<String, Object> conf) {
        FileItemStream fileStream = null;
        MultipartFile fileStream2 = null;
        boolean isAjaxUpload = request.getHeader("X-Requested-With") != null;
        if (!ServletFileUpload.isMultipartContent(request)) {
            return new BaseState(false, 5);
        } else {
            ServletFileUpload upload = new ServletFileUpload(new DiskFileItemFactory());
            if (isAjaxUpload) {
                upload.setHeaderEncoding("UTF-8");
            }

            try {
                for (FileItemIterator iterator = upload.getItemIterator(request); iterator.hasNext(); fileStream = null) {
                    fileStream = iterator.next();
                    if (!fileStream.isFormField()) {
                        break;
                    }
                }

                if (fileStream == null) {
                    MultipartFile file = null;
                    if (request instanceof MultipartHttpServletRequest) {
                        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                        Iterator<String> it = multiRequest.getFileNames();
                        if (it.hasNext()) {
                            file = multiRequest.getFile((String) it.next());
                        }
                    }

                    if (file != null && !file.isEmpty() && file.getOriginalFilename() != null) {
                        fileStream2 = file;
                    }
                }

                if (fileStream == null && fileStream2 == null) {
                    return new BaseState(false, 7);
                } else {
                    String savePath = (String) conf.get("savePath");
                    String originFileName = fileStream != null ? fileStream.getName() : fileStream2.getOriginalFilename();
                    originFileName = originFileName.replace(" ","_");
                    originFileName = originFileName.replace(" ","_");
                    originFileName = originFileName.replace(" ","_");
                    String suffix = FileType.getSuffixByFilename(originFileName);
                    originFileName = originFileName.substring(0, originFileName.length() - suffix.length());
                    savePath = savePath + suffix;
                    long maxSize = (Long) conf.get("maxSize");
                    if (!validType(suffix, (String[]) conf.get("allowFiles"))) {
                        return new BaseState(false, 8);
                    } else {
                        savePath = PathFormat.parse(savePath, originFileName);
                        String physicalPath = FileUtils.path((String) conf.get("rootPath") + savePath);
                        InputStream is = fileStream != null ? fileStream.openStream() : fileStream2.getInputStream();
                        State storageState = StorageManager.saveFileByInputStream(is, physicalPath, maxSize);
                        is.close();
                        if (storageState.isSuccess()) {
                            int actionCode = (Integer) conf.get("actionCode");
                            String ctx = request.getContextPath();
                            if (actionCode == 1) {
                                if ((Boolean) conf.get("imageCompressEnable")) {
                                    Integer maxWidth = (Integer) conf.get("imageCompressBorder");
                                    ImageUtils.thumbnails(new File(physicalPath), maxWidth, -1, (String) null);
                                }
                            } else if (actionCode == 3) {
                                final VideoUtils v = new VideoUtils(physicalPath);
                                if (v.cutPic()) {
                                    StorageManager.uploadFileSuccess(v.getImgFile(), storageState);
                                    v.convert();
                                    storageState.putInfo("url", ctx + PathFormat.format(savePath) + "." + v.getOutputFileExtension());
                                    storageState.putInfo("type", "." + v.getOutputFileExtension());
                                    storageState.putInfo("original", originFileName + "." + v.getInputFileExtension());
                                    StorageManager.uploadFileSuccess(physicalPath, storageState);
                                    return storageState;
                                }
                            }

                            storageState.putInfo("url", ctx + PathFormat.format(savePath));
                            storageState.putInfo("type", suffix);
                            storageState.putInfo("original", originFileName + suffix);
                            StorageManager.uploadFileSuccess(physicalPath, storageState);
                        }

                        return storageState;
                    }
                }
            } catch (FileUploadException var19) {
                return new BaseState(false, 6);
            } catch (IOException var20) {
                return new BaseState(false, 4);
            }
        }
    }

    private static boolean validType(String type, String[] allowTypes) {
        List<String> list = Arrays.asList(allowTypes);
        return list.contains(type);
    }
}
