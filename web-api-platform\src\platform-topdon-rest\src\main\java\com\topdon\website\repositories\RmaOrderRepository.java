package com.topdon.website.repositories;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.form.RmaOrderForm;
import com.topdon.website.mappers.RmaOrderMapper;
import com.topdon.website.model.RmaOrder;
import com.topdon.website.model.api.ZohoRmaOrder;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Date;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/4 11:14
 */
@Named
public class RmaOrderRepository extends MysqlJDBCSupport {

    @Inject
    protected RmaOrderRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public AbstractOption<RmaOrder> getByTicketNumber(String ticketNumber) {
        @Language("SQL") String sql = "select id,ticket_number ticketNumber, sn, product_id, issue_type,classification_id,product_name,description,order_no,country,state_region,city" +
                ",address1,postal_code,seller_name,channel,ticket_status,status_modified_time,solution,email,phone" +
                ",huo_wu_liu_dan_hao,tui_hui_gen_zong_hao,chu_li_fang_an,jing_xiao_shang_ming_cheng,platform from rma_order where ticket_number=:ticketNumber";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("ticketNumber", ticketNumber);
        return option(sql, RmaOrderMapper.DETAIL, params);
    }

    public long add(String ticketNumber, String ticketId, long userId, RmaOrderForm rmaOrderForm) {
        @Language("SQL") String sql = "insert into rma_order(ticket_number,ticket_id,user_id, sn, product_id, issue_type,classification_id,product_name,description,order_no,country,state_region,city,address1,postal_code,seller_name,channel,email,phone,platform,create_time) " +
                "VALUES (:ticketNumber,:ticketId,:userId,:sn,:productId,:issueType,:classificationId,:productName,:description,:orderNo,:country,:stateRegion,:city,:address1,:postalCode,:sellerName,:channel,:email,:phone,:platform,:createTime)";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("ticketNumber", ticketNumber);
        params.addValue("ticketId", ticketId);
        params.addValue("userId", userId);
        params.addValue("sn", rmaOrderForm.getSn());
        params.addValue("productId", rmaOrderForm.getProductId());
        params.addValue("issueType", rmaOrderForm.getIssueType());
        params.addValue("classificationId", rmaOrderForm.getClassificationId());
        params.addValue("productName", rmaOrderForm.getProductName());
        params.addValue("description", rmaOrderForm.getDescription());
        params.addValue("orderNo", rmaOrderForm.getOrderNo());
        params.addValue("country", rmaOrderForm.getCountry());
        params.addValue("stateRegion", rmaOrderForm.getStateRegion());
        params.addValue("city", rmaOrderForm.getCity());
        params.addValue("address1", rmaOrderForm.getAddress1());
        params.addValue("postalCode", rmaOrderForm.getPostalCode());
        params.addValue("sellerName", rmaOrderForm.getSellerName());
        params.addValue("channel", rmaOrderForm.getChannel());
        params.addValue("email", rmaOrderForm.getEmail());
        params.addValue("phone", rmaOrderForm.getPhone());
        params.addValue("platform", rmaOrderForm.getPlatform());
        params.addValue("createTime", Optional.ofNullable(rmaOrderForm.getCreateTime()).orElseGet(Date::new));
        return autoIncreaseInsert(sql, params).longValue();
    }

    public int update(ZohoRmaOrder zohoRmaOrder) {
        @Language("SQL") String sql = "update rma_order set ticket_status=:ticketStatus,order_no=:orderNo,seller_name=:sellerName,tui_hui_gen_zong_hao=:thgzh,huo_wu_liu_dan_hao=:hwldh,chu_li_fang_an=:clfa,status_modified_time=:statusModifiedTime ,update_time=now() " +
                "where (status_modified_time is null or status_modified_time<=:statusModifiedTime) and ticket_number = :ticketNumber";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("ticketNumber", zohoRmaOrder.getTicketNumber());
        params.addValue("sellerName", zohoRmaOrder.getCf_user_id());
        params.addValue("orderNo", zohoRmaOrder.getCf_ping_tai_ding_dan_hao());
        params.addValue("ticketStatus", zohoRmaOrder.getCf_zhong_tai_rma_zhuang_tai());
        params.addValue("thgzh", zohoRmaOrder.getCf_tui_hui_gen_zong_hao());
        params.addValue("hwldh", zohoRmaOrder.getCf_fa_huo_wu_liu_dan_hao());
        params.addValue("clfa", zohoRmaOrder.getCf_chu_li_fang_an());
        params.addValue("statusModifiedTime", zohoRmaOrder.getModifiedTime());
        return update(sql, params);
    }
}
