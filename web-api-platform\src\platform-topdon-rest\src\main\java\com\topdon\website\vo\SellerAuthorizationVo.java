package com.topdon.website.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SellerAuthorizationVo {
    private String authCertNo;
    private String authorizedEntity;
    private List<ProductLines> product = new ArrayList<>();
    private List<String> region = new ArrayList<>();
    private Integer authType;
    private Date authStartDate;
    private Date authEndDate;
    private List<String> authPlatform = new ArrayList<>();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductLines {
        private String productLines;
        private String productSeries;
        private String productModels;
    }
}
