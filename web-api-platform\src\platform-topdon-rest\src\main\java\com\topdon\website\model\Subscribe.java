package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

public class Subscribe {

    public static final String ENTITY_NAME = "SUBSCRIBE";
    private long id;
    private String email;
    private SubscribeSiteFrom siteFrom;
    private SubscribeFrom from;

    public enum SubscribeSiteFrom {
        EU,
        GLOBAL,
        MOBILE,
        EVC,
        AU,
        LA
    }

    public enum SubscribeFrom {
        REGISTER,
        WEBSITE,
        COOP,
        POP
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public SubscribeSiteFrom getSiteFrom() {
        return siteFrom;
    }

    public void setSiteFrom(SubscribeSiteFrom siteFrom) {
        this.siteFrom = siteFrom;
    }

    public SubscribeFrom getFrom() {
        return from;
    }

    public void setFrom(SubscribeFrom from) {
        this.from = from;
    }

    public static class Errors {
        public static final ErrorMessage EXISTS = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "EXISTS");
        public static final ErrorMessage EMPTY_EMAIL = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "EMPTY_EMAIL");
    }
}
