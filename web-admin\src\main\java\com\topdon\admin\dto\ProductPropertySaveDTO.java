package com.topdon.admin.dto;

import com.topdon.admin.entity.ProductProperty;
import com.topdon.admin.entity.PropertyCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class ProductPropertySaveDTO {

    private boolean draft;
    private Integer compareId;

    private List<PropertyCategorySaveDTO> propertyCategories;
    private List<ProductPropertySaveDTOInner> productProperties;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PropertyCategorySaveDTO extends PropertyCategory {
        private String parentCategory;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ProductPropertySaveDTOInner extends ProductProperty {
        private String category;
    }

}
