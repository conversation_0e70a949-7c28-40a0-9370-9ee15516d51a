<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="aliyun-repos" />
      <option name="name" value="aliyun-repos" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun-repos" />
      <option name="name" value="aliyun-repos" />
      <option name="url" value="http://172.16.50.125:8082/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://172.16.50.125:8082/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jeesite-repos" />
      <option name="name" value="jeesite-repos" />
      <option name="url" value="http://maven.jeesite.net/repository/maven-public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jeesite-repos" />
      <option name="name" value="jeesite-repos" />
      <option name="url" value="http://172.16.50.125:8082/nexus/content/groups/public" />
    </remote-repository>
  </component>
</project>