package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.InformationQueryForm;
import com.topdon.website.service.InformationService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("informations")
public class InformationsController extends ControllerSupport {

    @Resource
    private InformationService informationService;

    @GetMapping("/list")
    public AbstractRestResponse list(InformationQueryForm form) {
        return AbstractRestResponse.apply(informationService.getList(form));
    }

    @GetMapping("/search")
    public AbstractRestResponse search(InformationQueryForm form) {
        return AbstractRestResponse.apply(informationService.search(form));
    }
}
