package com.hiwie.breeze.cache.others;

import com.hiwie.breeze.util.StringUtil;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.List;

public class RedeemableContent {

    private List<String> stringList;

    private int integer;

    public RedeemableContent() {
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof RedeemableContent)) {
            return false;
        }
        RedeemableContent content = (RedeemableContent) o;
        return new EqualsBuilder().append(integer, content.getInteger()).append(stringList.stream().reduce(StringUtil.EMPTY, (a, b) -> StringUtil.join(a, b)), content.getStringList().stream().reduce(StringUtil.EMPTY, (a, b) -> StringUtil.join(a, b))).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(integer).append(stringList.stream().reduce(StringUtil.EMPTY, (a, b) -> StringUtil.join(a, b))).toHashCode();
    }

    @SuppressWarnings("WeakerAccess")
    public List<String> getStringList() {
        return stringList;
    }

    public void setStringList(List<String> stringList) {
        this.stringList = stringList;
    }

    @SuppressWarnings("WeakerAccess")
    public int getInteger() {
        return integer;
    }

    public void setInteger(int integer) {
        this.integer = integer;
    }
}