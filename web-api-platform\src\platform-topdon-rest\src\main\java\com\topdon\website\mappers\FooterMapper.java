package com.topdon.website.mappers;

import com.topdon.website.model.FooterMenu;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class FooterMapper {

    public static final RowMapper<FooterMenu> DETAIL = (rs, index) -> {
        FooterMenu detail = new FooterMenu();
        detail.setCode(rs.getString("code"));
        detail.setName(rs.getString("name"));
        detail.setNames(rs.getString("tree_names"));
        detail.setLink(rs.getString("link"));
        detail.setMobileLink(rs.getString("mobile_link"));
        return detail;
    };

}
