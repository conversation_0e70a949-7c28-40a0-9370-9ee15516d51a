package com.jeesite.modules.work.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.cooperation.entity.CooperationApply;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.work.entity.WorkOrder;
import com.jeesite.modules.work.service.WorkOrderService;

import java.util.List;

/**
 * 工单管理Controller
 * <AUTHOR>
 * @version 2022-03-10
 */
@Controller
@RequestMapping(value = "${adminPath}/work/workOrder")
public class WorkOrderController extends BaseController {

	@Autowired
	private WorkOrderService workOrderService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public WorkOrder get(String id, boolean isNewRecord) {
		return workOrderService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("work:workOrder:view")
	@RequestMapping(value = {"list", ""})
	public String list(WorkOrder workOrder, Model model) {
		model.addAttribute("workOrder", workOrder);
		return "modules/work/workOrderList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("work:workOrder:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<WorkOrder> listData(WorkOrder workOrder, HttpServletRequest request, HttpServletResponse response) {
		workOrder.setPage(new Page<>(request, response));
		Page<WorkOrder> page = workOrderService.findPage(workOrder);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("work:workOrder:view")
	@RequestMapping(value = "form")
	public String form(WorkOrder workOrder, Model model) {
		model.addAttribute("workOrder", workOrder);
		return "modules/work/workOrderForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("work:workOrder:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated WorkOrder workOrder) {
		workOrderService.save(workOrder);
		return renderResult(Global.TRUE, text("保存工单成功！"));
	}



	/**
	 * 导出用户数据
	 */
	@RequiresPermissions("work:workOrder:view")
	@RequestMapping(value = "exportData")
	public void exportData(WorkOrder workOrder, HttpServletResponse response) {
		List<WorkOrder> list = workOrderService.findList(workOrder);
		String fileName = "技术支持申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("技术支持申请", WorkOrder.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}