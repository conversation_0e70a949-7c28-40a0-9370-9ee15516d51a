package com.topdon.website.controller;

import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/file")
public class FileController extends ControllerSupport {

    private final FileService fileService;

    @Autowired
    public FileController(FileService fileService) {
        this.fileService = fileService;
    }


    @GetMapping("/{group}/{id}/{name}.{ext}")
    public ResponseEntity read(
            @PathVariable String group,
            @PathVariable String name,
            @PathVariable String ext,
            @PathVariable long id
    ) {
        return fileService.read(group, id).fold(
                error -> ResponseEntity.notFound().build(),
                bytes -> {
                    String contentDisposition = String.format("attachment;filename=\"%s.%s\"", name, ext);
                    return ResponseEntity.status(HttpStatus.OK).header("Content-Disposition", contentDisposition).body(bytes);
                }
        );
    }
}


