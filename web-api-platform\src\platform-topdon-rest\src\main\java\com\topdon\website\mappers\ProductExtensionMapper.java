package com.topdon.website.mappers;

import com.hiwie.breeze.json.Json;
import com.topdon.website.model.Product;
import com.topdon.website.model.ProductExtension;
import com.topdon.website.vo.ProductExtensionVo;
import org.springframework.jdbc.core.RowMapper;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ProductExtensionMapper {

    public static final RowMapper<ProductExtension> DETAIL = (rs, index) -> {
        ProductExtension detail = new ProductExtension();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setProduct(Product.apply(rs.getString("product_id"), rs.getString("product_name")));
        detail.setExtension(Json.readValue(rs.getString("content"), Map.class));
        return detail;
    };

    public static final RowMapper<ProductExtensionVo> DETAIL_VO = (rs, index) -> {
        ProductExtensionVo detail = new ProductExtensionVo();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setCompareClassificationCode(rs.getString("compare_classification_code"));
        detail.setCompare(rs.getBoolean("compare"));
        detail.setProduct(Product.apply(rs.getString("product_id"), rs.getString("product_name")));
        detail.setExtension(Json.readValue(rs.getString("content"), Map.class));
        return detail;
    };

}
