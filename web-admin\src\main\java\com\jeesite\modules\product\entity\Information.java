package com.jeesite.modules.product.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 产品资料管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Table(name = "information", alias = "a", label = "产品资料信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "product_id", attrName = "product.id", label = "产品"),
        @Column(name = "name", attrName = "name", label = "名称", queryType = QueryType.LIKE),
        @Column(name = "content", attrName = "content", label = "显示内容",queryType = QueryType.LIKE),
        @Column(name = "media_url", attrName = "mediaUrl", label = "显示封面", isQuery = false),
        @Column(name = "create_at", attrName = "createDate", label = "创建时间", isUpdate = false, isQuery = false, isUpdateForce = true),
        @Column(name = "update_at", attrName = "updateDate", label = "更新时间", isQuery = false, isUpdateForce = true),
        @Column(name = "download_url", attrName = "downloadUrl", label = "下载地址", isQuery = false),
        @Column(name = "type", attrName = "type", label = "资料类型"),
        @Column(name = "sort", attrName = "sort", label = "排序", isQuery = false),
        @Column(name = "group_by", attrName = "groupBy", label = "分组标题",queryType = QueryType.LIKE),
        @Column(name = "information_group_id", attrName = "informationGroupId", label = "分组ID"),
        @Column(name = "other_param", attrName = "otherParam", label = "其他参数", isQuery = false),
        @Column(name = "file_type", attrName = "fileType", label = "文件类型", isQuery = false),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(name = "name", label = "产品名称",queryType = QueryType.LIKE),})
}, orderBy = "a.sort"
)
public class Information extends DataEntity<Information> {

    private static final long serialVersionUID = 1L;
    private String name;        // 名称
    private Product product;
    private String content;        // 显示内容
    private String mediaUrl;        // 显示封面
    private Date createDate;        // 创建时间
    private Date updateDate;        // 更新时间
    private String downloadUrl;        // 下载地址
    private String type;        // 资料类型
    private Integer sort;
    private String groupBy;        // 分组标题
    private Integer informationGroupId;        // 分组id
    private String otherParam;        // 其他参数
    private String fileType;        // 文件类型
    private List<InformationApp> apps = ListUtils.newArrayList();

    public Information() {
        this(null);
    }

    public Information(String id) {
        super(id);
    }

    @NotBlank(message = "名称不能为空")
    @Size(min = 0, max = 255, message = "名称长度不能超过 255 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMediaUrl() {
        return mediaUrl;
    }

    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Size(min = 0, max = 255, message = "下载地址长度不能超过 255 个字符")
    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    @Size(min = 0, max = 20, message = "资料类型长度不能超过 20 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Size(min = 0, max = 40, message = "分组标题长度不能超过 40 个字符")
    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    @Size(min = 0, max = 102400, message = "其他参数长度不能超过 1024 个字符")
    public String getOtherParam() {
        return otherParam;
    }

    public void setOtherParam(String otherParam) {
        this.otherParam = otherParam;
    }

    @Size(min = 0, max = 20, message = "文件类型长度不能超过 20 个字符")
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public List<InformationApp> getApps() {
        return apps;
    }

    public void setApps(List<InformationApp> apps) {
        this.apps = apps;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @NotNull(message = "分组不能为空")
    public Integer getInformationGroupId() {
        return informationGroupId;
    }

    public void setInformationGroupId(Integer informationGroupId) {
        this.informationGroupId = informationGroupId;
    }
}