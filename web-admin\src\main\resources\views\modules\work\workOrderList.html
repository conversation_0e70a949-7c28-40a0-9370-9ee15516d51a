<% layout('/layouts/default.html', {title: '工单管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('工单管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('work:workOrder:edit')){ %>
					<a href="${ctx}/work/workOrder/form" class="btn btn-default btnTool" title="${text('新增工单')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i>
				${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${workOrder}" action="${ctx}/work/workOrder/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('电子邮箱')}：</label>
					<div class="control-inline">
						<#form:input path="email" maxlength="50" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('序列号')}：</label>
					<div class="control-inline">
						<#form:input path="serialNumber" maxlength="40" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('问题描述')}：</label>
					<div class="control-inline">
						<#form:input path="describe" maxlength="1024" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	$('#btnExport').click(function () {
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/work/workOrder/exportData',
			downloadFile: true
		});
	});
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("姓")}', name:'lastName', index:'a.last_name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/work/workOrder/form?id='+row.id+'" class="btnList" data-title="${text("编辑工单")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("名")}', name:'firstName', index:'a.first_name', width:150, align:"left"},
		{header:'${text("区域")}', name:'region.name', index:'a.region_id', width:150, align:"left"},
		{header:'${text("产品")}', name:'productName', index:'a.product_id', width:150, align:"left"},
		{header:'${text("电子邮箱")}', name:'email', index:'a.email', width:150, align:"left"},
		{header:'${text("序列号")}', name:'serialNumber', index:'a.serial_number', width:150, align:"left"},
		{header:'${text("购买渠道")}', name:'buyChannelName', index:'a.buy_channel', width:150, align:"left"},
		{header:'${text("渠道信息")}', name:'channelInfo', index:'a.channel_info', width:150, align:"left"},
		{header:'${text("Vehicle info/VIN")}', name:'vin', index:'a.vin', width:150, align:"left"},
		{header:'${text("问题描述")}', name:'describe', index:'a.describe', width:150, align:"left"},
		{header:'${text("时间")}', name:'createDate', index:'a.create_at', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('work:workOrder:edit')){ %>
				actions.push('<a href="${ctx}/work/workOrder/form?id='+row.id+'" class="btnList" title="${text("编辑工单")}"><i class="fa fa-pencil"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>