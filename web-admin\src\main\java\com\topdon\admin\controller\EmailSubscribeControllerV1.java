package com.topdon.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.topdon.admin.dto.EmailSubscribeDeleteDto;
import com.topdon.admin.dto.EmailSubscribeListDto;
import com.topdon.admin.dto.EmailSubscribePageDto;
import com.topdon.admin.entity.EmailSubscribe;
import com.topdon.admin.service.EmailSubscribeServiceV1;
import com.topdon.admin.vo.EmailSubscribeExcelVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.jeesite.common.web.BaseController.text;
import static com.jeesite.common.web.http.ServletUtils.renderResult;

@Controller
@RequestMapping(value = "${adminPath}/v1/email/emailSubscribe")
public class EmailSubscribeControllerV1 {

    @Resource
    private EmailSubscribeServiceV1 emailSubscribeServiceV1;

    /**
     * 导出用户数据
     */
    @RequiresPermissions("email:emailSubscribe:view")
    @RequestMapping(value = "exportData")
    public void exportData(EmailSubscribeListDto emailSubscribe, HttpServletResponse response) {
        List<EmailSubscribeExcelVo> list = emailSubscribeServiceV1.getExcelList(emailSubscribe);
        String fileName = "订阅数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try (ExcelExport ee = new ExcelExport(null, EmailSubscribeExcelVo.class)) {
            ee.setDataList(list).write(response, fileName);
        }
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("email:emailSubscribe:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<EmailSubscribe> listData(EmailSubscribePageDto emailSubscribe) {
        if (emailSubscribe.getPageNo() == null) {
            emailSubscribe.setPageNo(1);
        }
        if (emailSubscribe.getPageSize() == null) {
            emailSubscribe.setPageSize(20);
        }
        if(StrUtil.isBlank(emailSubscribe.getOrderBy())){
            emailSubscribe.setOrderBy("a.create_at desc");
        }
        PageDTO<EmailSubscribe> page = emailSubscribeServiceV1.getPage(emailSubscribe);
        return new Page<>((int) page.getCurrent(), (int) page.getSize(), page.getTotal(), page.getRecords());
    }

    @RequiresPermissions("email:emailSubscribe:edit")
    @RequestMapping(value = "batchDelete")
    @ResponseBody
    public String batchDelete(@RequestBody EmailSubscribeDeleteDto deleteDto) {
        return emailSubscribeServiceV1.batchDelete(deleteDto);
    }


    /**
     * 保存数据
     */
    @RequiresPermissions("email:emailSubscribe:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated EmailSubscribe emailSubscribe) {
        String result = emailSubscribeServiceV1.saveWithDuplicateCheck(emailSubscribe);
        if ("duplicate".equals(result)) {
            return renderResult(Global.FALSE, text("同一站点下该邮箱已存在，不能重复订阅！"));
        }
        return renderResult(Global.TRUE, text("保存订阅成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("email:emailSubscribe:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(EmailSubscribe emailSubscribe) {
        emailSubscribeServiceV1.removeById(emailSubscribe.getId());
        return renderResult(Global.TRUE, text("删除订阅成功！"));
    }
}