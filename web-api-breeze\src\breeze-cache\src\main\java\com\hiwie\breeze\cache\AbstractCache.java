package com.hiwie.breeze.cache;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.util.AssertUtil;
import com.hiwie.breeze.util.StringUtil;

import java.time.Duration;

/**
 * <AUTHOR>
 */
public abstract class AbstractCache {

    protected static final String PREFIX_SEPARATOR = ".";
    private final String prefix;
    protected final int db;
    protected final int expireSeconds;

    protected AbstractCache(String prefix, int db, AbstractOption<Duration> expireDuration) {
        AssertUtil.isTrue(prefix.matches("^[A-Z_]+(\\.[A-Z_]+)*$"), "only dots and uppercase letters allowed");
        expireDuration.foreach(duration -> AssertUtil.isTrue(duration.getSeconds() <= Integer.MAX_VALUE, "expire duration can NOT larger than " + Integer.MAX_VALUE));
        this.prefix = prefix;
        this.db = db;
        this.expireSeconds = expireDuration.map(Duration::getSeconds).getOrElse(0L).intValue();
    }

    protected String generateKey(String key) {
        return StringUtil.join(this.prefix, PREFIX_SEPARATOR, key);
    }

}
