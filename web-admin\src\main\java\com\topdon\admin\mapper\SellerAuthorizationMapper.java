package com.topdon.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.topdon.admin.dto.SellerAuthorizationDTO;
import com.topdon.admin.entity.SellerAuthorization;
import com.topdon.admin.vo.SellerAuthorizationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SellerAuthorizationMapper extends BaseMapper<SellerAuthorization> {
    IPage<SellerAuthorizationVo> getPage(PageDTO<SellerAuthorizationVo> tPageDTO, @Param("param") SellerAuthorizationDTO sellerAuthorizationDTO);

    List<SellerAuthorizationVo> getList(@Param("param") SellerAuthorizationDTO sellerAuthorizationDTO);

}