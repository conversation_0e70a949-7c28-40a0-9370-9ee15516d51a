package com.topdon.website;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Optional;


@Slf4j
@SpringBootApplication(scanBasePackages = {"com.hiwie","com.topdon.website"})
@EnableTransactionManagement
@EnableAsync
@MapperScan("com.topdon.website.mapper")
public class TopdonApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext applicationContext = null;
        try {
            applicationContext = SpringApplication.run(TopdonApplication.class,args);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        log.info("SpringApplication run on: " + Optional.ofNullable(applicationContext.getEnvironment().getProperty("server.port")).orElse("8080"));
    }
}
