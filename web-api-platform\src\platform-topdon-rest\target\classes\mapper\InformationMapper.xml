<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.InformationMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.Information">
    <!--@mbg.generated-->
    <!--@Table information-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="BLOB" property="content" />
    <result column="media_url" jdbcType="VARCHAR" property="mediaUrl" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="group_by" jdbcType="VARCHAR" property="groupBy" />
    <result column="other_param" jdbcType="VARCHAR" property="otherParam" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="information_group_id" jdbcType="INTEGER" property="informationGroupId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, `name`, content, media_url, create_at, update_at, download_url, `type`, 
    group_by, other_param, file_type, sort, information_group_id
  </sql>

    <resultMap id="informationResultMap" type="com.topdon.website.vo.InformationGroupVo">
        <id column="ig.id" jdbcType="INTEGER" property="id" />
        <result column="ig.parent_id" jdbcType="INTEGER" property="parentId" />
        <result column="ig.product_id" jdbcType="VARCHAR" property="productId" />
        <result column="product_id" jdbcType="VARCHAR" property="product_id" />
        <result column="productName" jdbcType="VARCHAR" property="productName" />
        <result column="compare" jdbcType="BOOLEAN" property="compare" />
        <result column="compare_classification_code" jdbcType="VARCHAR" property="compareClassificationCode" />
        <result column="ig.name" jdbcType="VARCHAR" property="name" />
        <result column="ig.type" jdbcType="VARCHAR" property="type" />
        <result column="ig.sort" jdbcType="INTEGER" property="sort" />
        <result column="ig.create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="ig.create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="ig.update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="ig.update_date" jdbcType="TIMESTAMP" property="updateDate" />

        <collection property="informationVos" ofType="com.topdon.website.vo.InformationVo">
            <id column="i.id" jdbcType="VARCHAR" property="id" />
            <result column="i.product_id" jdbcType="VARCHAR" property="productId" />
            <result column="i.name" jdbcType="VARCHAR" property="name" />
            <result column="i.content" jdbcType="BLOB" property="content" />
            <result column="i.media_url" jdbcType="VARCHAR" property="mediaUrl" />
            <result column="i.create_at" jdbcType="TIMESTAMP" property="createAt" />
            <result column="i.update_at" jdbcType="TIMESTAMP" property="updateAt" />
            <result column="i.download_url" jdbcType="VARCHAR" property="downloadUrl" />
            <result column="i.type" jdbcType="VARCHAR" property="type" />
            <result column="i.group_by" jdbcType="VARCHAR" property="groupBy" />
            <result column="i.other_param" jdbcType="VARCHAR" property="otherParam" />
            <result column="i.file_type" jdbcType="VARCHAR" property="fileType" />
            <result column="i.sort" jdbcType="INTEGER" property="sort" />
            <result column="i.information_group_id" jdbcType="INTEGER" property="informationGroupId" />
        </collection>
    </resultMap>

  <select id="getList" resultMap="informationResultMap">
      select
          ig.id `ig.id`,
          ig.parent_id `ig.parent_id`,
          ig.product_id `ig.product_id`,
          p.name `productName`,
          ig.name `ig.name`,
          ig.type `ig.type`,
          ig.sort `ig.sort`,
          ig.create_by `ig.create_by`,
          ig.create_date `ig.create_date`,
          ig.update_by `ig.update_by`,
          ig.update_date `ig.update_date`,
          i.id `i.id`,
          i.product_id `i.product_id`,
          i.name `i.name`,
          i.content `i.content`,
          i.media_url `i.media_url`,
          i.create_at `i.create_at`,
          i.update_at `i.update_at`,
          i.download_url `i.download_url`,
          i.type `i.type`,
          i.group_by `i.group_by`,
          i.other_param `i.other_param`,
          i.file_type `i.file_type`,
          i.sort `i.sort`,
          i.information_group_id `i.information_group_id`,
          (select id from product where name = #{productName,jdbcType=VARCHAR}) `product_id`,
          if((select count(1) from product_property where product_id = (select id from product where name = #{productName,jdbcType=VARCHAR}) and draft = #{draft} limit 1)>0,true,false) compare,
          (select cc.classification_code from product_property pp left join classification_compare cc on cc.id = pp.classification_compare_id where pp.product_id = (select id from product where name = #{productName,jdbcType=VARCHAR}) and pp.draft = #{draft} and cc.draft = #{draft} limit 1) compare_classification_code

      from information i
               left join information_group ig on i.information_group_id = ig.id
               left join product p on p.id = ig.product_id
               left join product p2 on p2.id = i.product_id
      where ig.type = #{type,jdbcType=VARCHAR}
      and p2.name = #{productName,jdbcType=VARCHAR}
      union
      select
          ig.id `ig.id`,
          ig.parent_id `ig.parent_id`,
          ig.product_id `ig.product_id`,
          null `productName`,
          ig.name `ig.name`,
          ig.type `ig.type`,
          ig.sort `ig.sort`,
          ig.create_by `ig.create_by`,
          ig.create_date `ig.create_date`,
          ig.update_by `ig.update_by`,
          ig.update_date `ig.update_date`,
          null `i.id`,
          null `i.product_id`,
          null `i.name`,
          null `i.content`,
          null `i.media_url`,
          null `i.create_at`,
          null `i.update_at`,
          null `i.download_url`,
          null `i.type`,
          null `i.group_by`,
          null `i.other_param`,
          null `i.file_type`,
          null `i.sort`,
          null `i.information_group_id`,
          null `product_id`,
          null compare,
          null compare_classification_code
      from information_group ig
          where id in (
              select ig.parent_id
              from information i
                       left join information_group ig on i.information_group_id = ig.id
                       left join product p on p.id = ig.product_id
                       left join product p2 on p2.id = i.product_id
              where ig.type = #{type,jdbcType=VARCHAR}
                and p2.name = #{productName,jdbcType=VARCHAR}
              )
      order by `i.sort`
  </select>

  <select id="search" resultType="com.topdon.website.vo.InformationVo">
      select *,ig.name groupName
      from information i
               left join information_group ig on i.information_group_id = ig.id
               left join product p2 on p2.id = i.product_id
      <where>
          ig.name != ''
          and (i.type = 'DOWNLOAD' or i.type = 'VIDEO')
          <if test="productName != null and productName != ''">
            and  p2.name like concat('%',#{productName,jdbcType=VARCHAR},'%')
          </if>
      </where>
      order by i.sort
    </select>
</mapper>