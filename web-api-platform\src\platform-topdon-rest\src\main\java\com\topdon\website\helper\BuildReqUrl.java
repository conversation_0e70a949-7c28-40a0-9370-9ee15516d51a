package com.topdon.website.helper;

import cn.hutool.core.util.StrUtil;

import java.util.Map;

public class BuildReqUrl {

	private boolean isHasUrl = false;

	private StringBuffer reqUrl;

	public BuildReqUrl(String str) {
		this.isHasUrl = true;
		this.reqUrl = new StringBuffer(str);
	}

	public BuildReqUrl() {
		this.reqUrl = new StringBuffer();
	}
	public BuildReqUrl set(String name,Object value) {
		if(this.isHasUrl)
			reqUrl.append(!StrUtil.containsAny(reqUrl, "?") ? "?" : "&");
		else
			reqUrl.append(reqUrl.length() == 0 ? "" : "&");
		reqUrl.append(name + "=" + value);
		return this;
	}

	public BuildReqUrl set(Map<String,Object> params){
		for (String key : params.keySet()) {
			set(key,params.get(key));
		}
		return this;
	}

	@Override
	public String toString() {
		return reqUrl.toString();
	}
	
}
