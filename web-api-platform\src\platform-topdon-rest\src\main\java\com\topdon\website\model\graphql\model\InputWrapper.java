package com.topdon.website.model.graphql.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Data
public class InputWrapper {

    private Map<String, Object> input;

    public InputWrapper(Map<String, Object> input) {
        this.input = input;
    }

    public InputWrapper() {}

    @SuppressWarnings("unchecked")
    public InputWrapper(String jsonString) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            this.input = mapper.readValue(jsonString, Map.class);
        } catch (JsonProcessingException e) {
            log.error("Error while parsing json string", e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
