package com.topdon.website;


import com.hiwie.breeze.ErrorMessage;

public class SCConstants {
    public static final String MODULE = "WEBSITE";
    public static final ErrorMessage SYSTEM_INNER_ERROR = new ErrorMessage(MODULE, "SYSTEM", "INNER_ERROR");
    public static final ErrorMessage API_ERROR = new ErrorMessage(MODULE, "API", "ERROR");
    public static final ErrorMessage UPLOAD_ERROR = new ErrorMessage(MODULE, "FILE", "UPLOAD_ERROR");
    public static final ErrorMessage SIZE_LIMIT = new ErrorMessage(MODULE, "FILE", "SIZE_LIMIT");
    public static final String CONTENT_TYPE_TEXT_JSON = "text/json";
}
