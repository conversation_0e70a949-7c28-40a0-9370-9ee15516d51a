package com.topdon.website.mappers;

import com.topdon.website.model.HWFile;
import org.springframework.jdbc.core.RowMapper;

public class HWFileMapper {

    public static final RowMapper<HWFile> DETAIL = (row, index) -> {
        HWFile file = new HWFile();
        file.setId(row.getLong("ID"));
        file.setName(row.getString("NAME"));
        file.setMime(row.getString("MIME"));
        file.setSize(row.getLong("SIZE"));
        file.setCreateBy(row.getLong("CREATE_BY"));
        file.setCreateAt(row.getTimestamp("CREATE_AT").toLocalDateTime());
        return file;
    };

}
