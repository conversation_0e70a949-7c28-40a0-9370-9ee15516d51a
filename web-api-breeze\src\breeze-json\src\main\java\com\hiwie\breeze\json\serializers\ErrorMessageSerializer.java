package com.hiwie.breeze.json.serializers;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.util.StringUtil;
import org.apache.commons.text.StringSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;
import java.util.Objects;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 */
public class ErrorMessageSerializer extends StdSerializer<ErrorMessage> {

    public static final ErrorMessageSerializer INSTANCE = new ErrorMessageSerializer();

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorMessageSerializer.class);

    private static final long serialVersionUID = 1L;

    private ErrorMessageSerializer() {
        super(ErrorMessage.class);
    }

    @Override
    public void serialize(ErrorMessage value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        Locale locale = LocaleContextHolder.getLocale();
        ResourceBundle bundle = ResourceBundle.getBundle("errors", locale);
        String code = value.getCode();
        String message = bundle.getString(code);
        if (StringUtil.isEmpty(message)) {
            LOGGER.warn("errors.properties missing i18n for '" + code + "'");
            message = code;
        } else if (value.getParametersOption().isDefined()) {
            StringSubstitutor sub = new StringSubstitutor(value.getParametersOption().get());
            message = sub.replace(message);
        }
        gen.writeStartObject();
        gen.writeStringField("code", code);
        gen.writeStringField("message", message);
        if (value.getParametersOption().isDefined()) {
            gen.writeObjectField("parameters", value.getParametersOption().get());
        }
        gen.writeEndObject();
    }

}
