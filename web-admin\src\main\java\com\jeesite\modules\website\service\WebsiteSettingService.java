package com.jeesite.modules.website.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.website.entity.WebsiteSetting;
import com.jeesite.modules.website.dao.WebsiteSettingDao;

/**
 * 网站配置Service
 * <AUTHOR>
 * @version 2023-02-16
 */
@Service
@Transactional(readOnly=true)
public class WebsiteSettingService extends CrudService<WebsiteSettingDao, WebsiteSetting> {
	
	/**
	 * 获取单条数据
	 * @param websiteSetting
	 * @return
	 */
	@Override
	public WebsiteSetting get(WebsiteSetting websiteSetting) {
		return super.get(websiteSetting);
	}
	
	/**
	 * 查询分页数据
	 * @param websiteSetting 查询条件
	 * @param websiteSetting.page 分页对象
	 * @return
	 */
	@Override
	public Page<WebsiteSetting> findPage(WebsiteSetting websiteSetting) {
		return super.findPage(websiteSetting);
	}
	
	/**
	 * 查询列表数据
	 * @param websiteSetting
	 * @return
	 */
	@Override
	public List<WebsiteSetting> findList(WebsiteSetting websiteSetting) {
		return super.findList(websiteSetting);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param websiteSetting
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(WebsiteSetting websiteSetting) {
		super.save(websiteSetting);
	}
	
	/**
	 * 更新状态
	 * @param websiteSetting
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(WebsiteSetting websiteSetting) {
		super.updateStatus(websiteSetting);
	}
	
	/**
	 * 删除数据
	 * @param websiteSetting
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(WebsiteSetting websiteSetting) {
		super.delete(websiteSetting);
	}
	
}