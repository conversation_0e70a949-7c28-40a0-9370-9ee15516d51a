<% layout('/layouts/default.html', {title: '数据管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('数据管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('test:testData:edit')){ %>
					<a href="${ctx}/test/testData/form" class="btn btn-default btnTool" title="${text('新增数据')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${testData}" action="${ctx}/test/testData/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('单行文本')}：</label>
					<div class="control-inline">
						<#form:input path="testInput" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('多行文本')}：</label>
					<div class="control-inline">
						<#form:input path="testTextarea" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('下拉框')}：</label>
					<div class="control-inline width-120">
						<#form:select path="testSelect" dictType="sys_menu_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('下拉多选')}：</label>
					<div class="control-inline width-120">
						<#form:select path="testSelectMultiple" dictType="sys_menu_type" multiple="true" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单选框')}：</label>
					<div class="control-inline">
						<#form:radio path="testRadio" dictType="sys_menu_type" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('复选框')}：</label>
					<div class="control-inline">
						<#form:checkbox path="testCheckbox" dictType="sys_menu_type" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('日期选择')}：</label>
					<div class="control-inline">
						<#form:input path="testDate_gte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="testDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="testDate_lte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('日期时间')}：</label>
					<div class="control-inline">
						<#form:input path="testDatetime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="testDatetime_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="testDatetime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用户选择')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testUser" title="${text('用户选择')}"
							path="testUser.userCode" labelPath="testUser.userName" 
							url="${ctx}/sys/office/treeData?isLoadUser=true" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('机构选择')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testOffice" title="${text('机构选择')}"
							path="testOffice.officeCode" labelPath="testOffice.officeName" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域选择')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testAreaCode" title="${text('区域选择')}"
							path="testAreaCode" labelPath="testAreaName" 
							url="${ctx}/sys/area/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("单行文本")}', name:'testInput', index:'a.test_input', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/test/testData/form?id='+row.id+'" class="btnList" data-title="${text("编辑数据")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("多行文本")}', name:'testTextarea', index:'a.test_textarea', width:150, align:"left"},
		{header:'${text("下拉框")}', name:'testSelect', index:'a.test_select', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_menu_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("下拉多选")}', name:'testSelectMultiple', index:'a.test_select_multiple', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_menu_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("单选框")}', name:'testRadio', index:'a.test_radio', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_menu_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("复选框")}', name:'testCheckbox', index:'a.test_checkbox', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_menu_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("日期选择")}', name:'testDate', index:'a.test_date', width:150, align:"center"},
		{header:'${text("日期时间")}', name:'testDatetime', index:'a.test_datetime', width:150, align:"center"},
		{header:'${text("用户选择")}', name:'testUser.userName', index:'a.test_user_code', width:150, align:"center"},
		{header:'${text("机构选择")}', name:'testOffice.officeName', index:'a.test_office_code', width:150, align:"center"},
		{header:'${text("区域选择")}', name:'testAreaName', index:'a.test_area_code', width:150, align:"center"},
		{header:'${text("区域名称")}', name:'testAreaName', index:'a.test_area_name', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_search_status')}, val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('test:testData:edit')){ %>
				actions.push('<a href="${ctx}/test/testData/form?id='+row.id+'" class="btnList" title="${text("编辑数据")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/test/testData/disable?id='+row.id+'" class="btnList" title="${text("停用数据")}" data-confirm="${text("确认要停用该数据吗？")}"><i class="glyphicon glyphicon-ban-circle"></i></a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/test/testData/enable?id='+row.id+'" class="btnList" title="${text("启用数据")}" data-confirm="${text("确认要启用该数据吗？")}"><i class="glyphicon glyphicon-ok-circle"></i></a>&nbsp;');
				}
				actions.push('<a href="${ctx}/test/testData/delete?id='+row.id+'" class="btnList" title="${text("删除数据")}" data-confirm="${text("确认要删除该数据吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>