<% layout('/layouts/default.html', {title: '订阅管理', libs: ['dataGrid']}){ %>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('订阅管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('email:emailSubscribe:edit')){ %>
					<a href="${ctx}/email/emailSubscribe/form" class="btn btn-default btnTool" title="${text('新增订阅')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i>
					${text('导出')}</a>
				<% if(hasPermi('email:emailSubscribe:edit')){ %>
				<button class="btn btn-danger " title="${text('批量删除')}" id="batchDelete" ><i class="fa fa-trash"></i> ${text('批量删除')}</button>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${emailSubscribe}" action="${ctx}/v1/email/emailSubscribe/listData" method="post" class="form-inline"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('邮箱')}：</label>
					<div class="control-inline">
						<#form:input path="email" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('功能')}：</label>
					<div class="control-inline width-120">
						<#form:select path="from" dictType="from" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="startCreateAt" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="endCreateAt" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('订阅来源')}：</label>
					<div class="control-inline width-160">
						<#form:select path="site" dictType="site_from" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('复制折扣码')}：</label>
					<div class="control-inline width-120">
						<#form:select path="copiedDiscountCode" dictType="sys_yes_no" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	$('#btnExport').click(function () {
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/v1/email/emailSubscribe/exportData',
			downloadFile: true
		});
	});

	var selectedValues = []
	$('#batchDelete').click(function () {
		if (selectedValues.length === 0) {
			js.showErrorMessage('请先选择要删除的项！')
			return;
		}

		if (!confirm('确定要删除选中的项吗？')) {
			return;
		}

		axios.post('${ctx}/v1/email/emailSubscribe/batchDelete', {
			ids: selectedValues
		}).then(res => {
			selectedValues = []
			if (res.data) {
				js.showErrorMessage(res.data)
			} else {
				js.showMessage('删除成功')
				$('#searchForm button[type="submit"]').click();
			}
		})
	});

// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("邮箱")}', name:'email', index:'a.email', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/email/emailSubscribe/form?id='+row.id+'" class="btnList" data-title="${text("编辑订阅")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("功能")}', name:'from', index:'a.from', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('from')}, val, '${text("未知")}', true);
		}},
		{header:'${text("订阅时间")}', name:'createAt', index:'a.create_at', width:150, align:"center"},
		{header:'${text("订阅来源")}', name:'site', index:'a.site', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('site_from')}, val, '${text("未知")}', true);
		}},
		{header:'${text("复制折扣码")}', name:'copiedDiscountCode', index:'a.copied_discount_code', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('email:emailSubscribe:edit')){ %>
				actions.push('<a href="${ctx}/email/emailSubscribe/form?id='+row.id+'" class="btnList" title="${text("编辑订阅")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/v1/email/emailSubscribe/delete?id='+row.id+'" class="btnList" title="${text("删除订阅")}" data-confirm="${text("确认要删除该订阅吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	onSelectRow: function(id, isSelect, event){
		if(isSelect){
			selectedValues.push(String(id))
		}else {
			selectedValues = selectedValues.filter(item => item !== String(id))
		}
	},
	onSelectAll: function(ids, isSelect){
		if(isSelect){
			selectedValues = ids
		}else {
			selectedValues = []
		}
	},
	showCheckbox:true,
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
</script>