package com.topdon.website.configs;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

import java.util.Locale;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/5 17:46
 */
@Configuration
@Slf4j
public class I18nConfig {

    @Primary
    @Bean(name = "messageSource")
    public ReloadableResourceBundleMessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageBundle = new ReloadableResourceBundleMessageSource();
        messageBundle.setBasenames("classpath:i18n/securityMsg");//即配置文件所在目录为 i18n，文件前缀为 errorMessages
        messageBundle.setDefaultEncoding("UTF-8");
        messageBundle.setFallbackToSystemLocale(false);//是否使用系统默认的编码  默认为true
        messageBundle.setDefaultLocale(Locale.US);
        return messageBundle;
    }
}
