package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "product")
public class Product {
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField(value = "`name`")
    private String name;

    /**
     * 是否新品
     */
    @TableField(value = "new_product")
    private Boolean newProduct;

    @TableField(value = "classification_id")
    private String classificationId;

    @TableField(value = "description")
    private String description;

    @TableField(value = "search_cover")
    private String searchCover;

    @TableField(value = "cover")
    private String cover;

    @TableField(value = "search_view")
    private Boolean searchView;

    @TableField(value = "sort")
    private Integer sort;

    @TableField(value = "create_at")
    private Date createAt;

    @TableField(value = "mobile_cover")
    private String mobileCover;

    /**
     * 是否停产
     */
    @TableField(value = "discontinued")
    private Boolean discontinued;

    /**
     * 是否RMA工具
     */
    @TableField(value = "rma_tool_show")
    private Boolean rmaToolShow;

    /**
     * 是否允许购买
     */
    @TableField(value = "allow_purchase")
    private Boolean allowPurchase;

    /**
     * US官网跳转链接
     */
    @TableField(value = "url_us")
    private String urlUs;

    /**
     * EU官网跳转链接
     */
    @TableField(value = "url_eu")
    private String urlEu;

    /**
     * AU官网跳转链接
     */
    @TableField(value = "url_au")
    private String urlAu;
}