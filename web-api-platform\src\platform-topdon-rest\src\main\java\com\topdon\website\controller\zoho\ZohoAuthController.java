package com.topdon.website.controller.zoho;

import com.zoho.oauth.client.ZohoOAuthClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/15 15:22
 */
@RestController
@RequestMapping("/zoho/auth")
@Slf4j
public class ZohoAuthController {

    public static Optional<String> key = Optional.empty();

    @GetMapping("/generatorAuthUrl")
    @ResponseBody
    public Object generatorAuthUrl() {
        String state = String.valueOf(System.currentTimeMillis());
        key = Optional.of(state);
        return ZohoOAuthClient.getInstance().getLoginWithZohoUrl() + "&state=" + state;
    }

    /**
     * zoho授权成功回调
     */
    @GetMapping("/callBackCode")
    @ResponseBody
    public String callBackCode(@RequestParam("code") String code, @RequestParam("state") String state) {
        log.info("zoho callBackCode {}", state);
        if (key.map(o -> o.equals(state)).orElse(Boolean.FALSE)) {
            ZohoOAuthClient instance = ZohoOAuthClient.getInstance();
            instance.generateAccessToken(code);
            return "zoho callBackCode success！";
        } else {
            return "zoho callBackCode error！";
        }
    }
}
