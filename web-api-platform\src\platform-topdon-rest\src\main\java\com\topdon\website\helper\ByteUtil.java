package com.topdon.website.helper;

public class ByteUtil {

    /**
     * 合并两个字节数组到一个字节数组
     *
     * @param data1 字节数组1
     * @param data2 字节数组2
     * @return byte[] 合并后的字节数字
     */
    public static byte[] mergeBytes(byte[] data1, byte[] data2) {
        byte[] result = new byte[data1.length + data2.length];
        System.arraycopy(data1, 0, result, 0, data1.length);
        System.arraycopy(data2, 0, result, data1.length, data2.length);
        return result;
    }

    /**
     * 合并多个字节数组到一个字节数组
     *
     * @param values 动态字节数字参数
     * @return byte[] 合并后的字节数字
     */
    public static byte[] mergeBytes(byte[]... values) {
        int lengthByte = 0;
        for (byte[] value : values) {
            lengthByte += value.length;
        }
        byte[] allBytes = new byte[lengthByte];
        int countLength = 0;
        for (byte[] b : values) {
            System.arraycopy(b, 0, allBytes, countLength, b.length);
            countLength += b.length;
        }
        return allBytes;
    }
}
