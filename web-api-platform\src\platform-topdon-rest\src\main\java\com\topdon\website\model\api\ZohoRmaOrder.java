package com.topdon.website.model.api;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 16:28
 */
@Data
public class ZohoRmaOrder {

    private String ticketNumber;
    private String cf_zhong_tai_rma_zhuang_tai;
    private Date modifiedTime;
    private String cf_tui_hui_gen_zong_hao;
    private String cf_fa_huo_wu_liu_dan_hao;
    private String cf_chu_li_fang_an;
    private String cf_ping_tai_ding_dan_hao;

    private String id;
    private Date createdTime;
    private String cf_sn;
    private String cf_problem_description;
    private String cf_wen_ti_lei_bie;
    private String cf_chan_pin_xing_hao;
    private String cf_please_tell_us_your_order_number;
    private String cf_jing_xiao_shang_ming_cheng;
    private String cf_platform;
    private String cf_which_is_your_country;
    private String cf_shipping_address_state_or_region;
    private String cf_shipping_address_city;
    private String cf_your_address_1;
    private String cf_shipping_address_postal_code;
    private String cf_user_id;
    private String phone;
    private String email;
}
