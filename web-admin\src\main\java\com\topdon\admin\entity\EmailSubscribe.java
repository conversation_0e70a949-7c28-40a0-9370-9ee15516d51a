package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "email_subscribe")
public class EmailSubscribe {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "email")
    private String email;

    @TableField(value = "`from`")
    private String from;

    @TableField(value = "create_at")
    private Date createAt;

    @TableField(value = "site")
    private String site;

    /**
     * 是否复制折扣码 0否 1是
     */
    @TableField(value = "copied_discount_code")
    private Integer copiedDiscountCode;
}