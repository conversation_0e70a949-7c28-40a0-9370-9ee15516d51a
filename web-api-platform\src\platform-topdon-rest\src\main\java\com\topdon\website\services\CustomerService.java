package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.json.Json;
import com.topdon.website.SCConstants;
import com.topdon.website.form.ForgetPasswordForm;
import com.topdon.website.form.RegisterForm;
import com.topdon.website.model.TopdonResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

import javax.inject.Named;
import java.io.IOException;

@Named
public class CustomerService {
    final String CONTENT_TYPE_TEXT_JSON = "text/json";


    public AbstractEither<ErrorMessage, TopdonResponse> forgetPassword(ForgetPasswordForm form) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://app.lenkor.cn/lenkor-app-user/api/client/forgetPwdByWebsite";
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = null;
        try {
            StringEntity se = new StringEntity(Json.writeValueAsString(form));
            se.setContentType(CONTENT_TYPE_TEXT_JSON);
            post.setEntity(se);
            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            TopdonResponse cmbResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            if ("0".equals(cmbResponse.getCode())) {
                return Left.apply(SCConstants.API_ERROR);
            }
            return Right.apply(cmbResponse);
        } catch (IOException e) {
            return Left.apply(SCConstants.API_ERROR);
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public AbstractEither<ErrorMessage, TopdonResponse> register(RegisterForm form) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        String url = "https://app.lenkor.cn/lenkor-app-user/api/client/registerByWebsite";
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        CloseableHttpResponse response = null;
        try {
            StringEntity se = new StringEntity(Json.writeValueAsString(form));
            se.setContentType(CONTENT_TYPE_TEXT_JSON);
            post.setEntity(se);
            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            TopdonResponse cmbResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            if ("0".equals(cmbResponse.getCode())) {
                return Left.apply(SCConstants.API_ERROR);
            }
            return Right.apply(cmbResponse);
        } catch (IOException e) {
            return Left.apply(SCConstants.API_ERROR);
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
