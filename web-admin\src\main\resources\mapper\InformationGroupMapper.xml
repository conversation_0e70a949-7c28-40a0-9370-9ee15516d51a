<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.InformationGroupMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.InformationGroup">
    <!--@mbg.generated-->
    <!--@Table information_group-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, parent_id, product_id, `name`, `type`, sort, create_by, create_date, update_by, 
    update_date
  </sql>

  <sql id="getListCondition">
    <if test="param.type != null and param.type != '' ">
      and ig.type = #{param.type,jdbcType=VARCHAR}
    </if>
    <if test="param.name != null and param.name != '' ">
      and ig.name like concat('%',#{param.name,jdbcType=VARCHAR},'%')
    </if>
  </sql>

  <select id="getList" resultType="com.topdon.admin.vo.InformationGroupVo">
    select ig.*,p.name productName
    from information_group ig
    left join product p on p.id = ig.product_id
    <where>
      <include refid="getListCondition">
      </include>
    </where>
  </select>
</mapper>