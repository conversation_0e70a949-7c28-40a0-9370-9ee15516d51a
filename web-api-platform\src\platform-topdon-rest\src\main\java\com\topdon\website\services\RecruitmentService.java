package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.repository.PaginationForm;
import com.topdon.website.form.RecruitmentQueryForm;
import com.topdon.website.model.Recruitment;
import com.topdon.website.repositories.RecruitmentRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class RecruitmentService {

    private final RecruitmentRepository recruitmentRepository;

    @Inject
    public RecruitmentService(RecruitmentRepository recruitmentRepository) {
        this.recruitmentRepository = recruitmentRepository;
    }

    public AbstractEither<ErrorMessage, List<Recruitment>> list(RecruitmentQueryForm queryForm, PaginationForm form) {
        return Right.apply(recruitmentRepository.list(queryForm, form));
    }

    public AbstractEither<ErrorMessage, Recruitment> get(String id) {
        return recruitmentRepository.get(id).toRight(Recruitment.Errors.NOT_FOUND);
    }
}
