spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-request-size: 50MB
      max-file-size: 50MB
  datasource:
    url: *********************************************************************************************************************************************************************************************************
    username: root
    password: lenkor2019
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1
      validation-query-timeout: 5
      test-on-borrow: true
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000

server:
  id: PM
  # domain: https://www.topdon.com
  domain: http://**************:8082/api
  port: 8895
  servlet:
    context-path: /api
mail:
  host: smtp.gmail.com
  port: 587
  user_name: <EMAIL>
  password: ahpznayulikdyhhx

shopify:
  api:
    graphql:
      version: 2022-10
  graphql:
    url: https://topdon-it.myshopify.com/admin/api/2022-10/graphql.json
  admin:
    graphql:
      token: 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
  storefront:
    graphql:
      url: https://topdon-it.myshopify.com/api/2022-10/graphql.json
      token: 76721c798b82d8f117d677b7d5c67ba3

support:
  receive:
    email: <EMAIL>

file:
  root:
    path: /root/filetmp/
  zip:
    root:
      path: /opt/data
  client:
    aliyun:
      endpoint: oss-cn-shenzhen.aliyuncs.com
      accessKeyId: LTAI5tJkqkY6aLfpMZpxRyeC
      accessKeySecret: ******************************
      bucketName: lenkor-oss-dev
      root: topdon-web/
      proxyEndpoint: ""

topdon:
  url: http://************:8762
lenkor:
  innerApi:
    url: http://************:9113/lk-erp-innerapi/
    clientId: official-website
    clientSecret: 20913b5f9065410bbf77762431b252db
desk:
  sdk:
    config:
      fileName: zohoSdkConfig

zoho:
  config:
    enabled: true
    clientId: 1000.******************************
    clientSecret: 55e2a416fdc42e2ac9346bcde24e75ec399397b71d
    redirectUri: http://************:8081/api/zoho/auth/callBackCode
    timeOut: 900
    scope: Desk.tickets.ALL,Desk.tasks.ALL,Desk.settings.ALL,Desk.events.ALL,Desk.basic.ALL,Desk.contacts.ALL
    minLogLevel: INFO
    dc: com
    userIdentifier: <EMAIL>
    oauthTokensFilePath: C:\\Users\\<USER>\\Downloads
