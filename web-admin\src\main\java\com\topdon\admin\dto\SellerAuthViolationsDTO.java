package com.topdon.admin.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.topdon.admin.entity.SellerAuthViolations;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SellerAuthViolationsDTO extends SellerAuthViolations {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Override
    public Date getDate() {
        return super.getDate();
    }
}
