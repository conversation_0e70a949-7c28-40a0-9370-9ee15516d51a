package com.jeesite.modules.email.service;

import cn.hutool.core.util.StrUtil;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.email.dao.EmailSubscribeDao;
import com.jeesite.modules.email.entity.EmailSubscribe;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 订阅管理Service
 * <AUTHOR>
 * @version 2023-02-14
 */
@Service
@Transactional(readOnly=true)
public class EmailSubscribeService extends CrudService<EmailSubscribeDao, EmailSubscribe> {
	
	/**
	 * 获取单条数据
	 * @param emailSubscribe
	 * @return
	 */
	@Override
	public EmailSubscribe get(EmailSubscribe emailSubscribe) {
		return super.get(emailSubscribe);
	}
	
	/**
	 * 查询分页数据
	 * @param emailSubscribe 查询条件
	 * @param emailSubscribe.page 分页对象
	 * @return
	 */
	@Override
	public Page<EmailSubscribe> findPage(EmailSubscribe emailSubscribe) {
		if(StrUtil.isBlank(emailSubscribe.getPage().getOrderBy())){
			emailSubscribe.getPage().setOrderBy("a.create_at desc");
		}
		return super.findPage(emailSubscribe);
	}
	
	/**
	 * 查询列表数据
	 * @param emailSubscribe
	 * @return
	 */
	@Override
	public List<EmailSubscribe> findList(EmailSubscribe emailSubscribe) {
		return super.findList(emailSubscribe);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param emailSubscribe
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(EmailSubscribe emailSubscribe) {
		super.save(emailSubscribe);
	}
	
	/**
	 * 更新状态
	 * @param emailSubscribe
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(EmailSubscribe emailSubscribe) {
		super.updateStatus(emailSubscribe);
	}
	
	/**
	 * 删除数据
	 * @param emailSubscribe
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(EmailSubscribe emailSubscribe) {
		super.delete(emailSubscribe);
	}
	
}