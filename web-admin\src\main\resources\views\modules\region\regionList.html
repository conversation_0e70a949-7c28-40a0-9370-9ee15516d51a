<% layout('/layouts/default.html', {title: '区域管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('区域管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('region:region:edit')){ %>
					<a href="${ctx}/region/region/form" class="btn btn-default btnTool" title="${text('新增区域')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<a href="#" class="btn btn-default" id="btnFixTreeData" title="修复树表数据（包含字段：parentCodes、treeLeaf、treeLevel、treeSorts、treeNames）"><i class="fa fa-refresh"></i> 修复</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${region}" action="${ctx}/region/region/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('名字')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('国旗')}：</label>
					<div class="control-inline">
						<#form:input path="flag" maxlength="1024" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="1024" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("名字")}', name:'name', index:'a.name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '( '+row.code+' ) '+'<a href="${ctx}/region/region/form?code='+row.code+'" class="btnList" data-title="${text("编辑区域")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("国旗")}', name:'flag', index:'a.flag', width:150, align:"left",formatter:function (val,obj,row,act) {
			if (val){
				return '<img width="30%" src=' + encodeURI(val) + ' />';
			}else{
				return "缺失";
			}

			}},
		{header:'${text("本级排序号")}', name:'treeSort', index:'a.tree_sort', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('region:region:edit')){ %>
				actions.push('<a href="${ctx}/region/region/form?code='+row.code+'" class="btnList" title="${text("编辑区域")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/region/region/delete?code='+row.code+'" class="btnList" title="${text("删除区域")}" data-confirm="${text("确认要删除该区域及所有子区域吗？")}" data-deltreenode="'+row.id+'"><i class="fa fa-trash-o"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/region/region/form?parentCode='+row.id+'" class="btnList" title="${text("新增下级区域")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'name,flag,remarks,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
// 修复树表数据（包含字段：parentCodes、treeLeaf、treeLevel、treeSorts、treeNames）
$("#btnFixTreeData").click(function(){
	js.ajaxSubmit("${ctx}/region/region/fixTreeData", function(data){
		js.showMessage(data.message);
	});
	return false;
});
</script>