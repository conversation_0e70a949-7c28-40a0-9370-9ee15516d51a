package com.hiwie.breeze;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ResultMap<P> {

    private List<ResultProviderInterface<P>> elements;

    public ResultMap() {
        this.elements = Lists.newArrayList();
    }

    public void addProvider(ResultProviderInterface<P> provider) {
        this.elements.add(provider);
    }

    public Map<String, Object> get(P tuples) {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(elements.size());
        for (ResultProviderInterface<P> element : elements) {
            AbstractOption<Object> elementResult = element.getValue(tuples);
            if (elementResult.isDefined()) {
                result.put(element.getKey(), elementResult.get());
            }
        }
        return result;
    }

}
