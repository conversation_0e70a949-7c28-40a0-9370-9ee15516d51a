package com.topdon.website.model.graphql.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CartBuyerIdentityUpdate {
    private String cartId;
    private BuyerIdentity buyerIdentity;

    @Data
    public static class BuyerIdentity{
        private String customerAccessToken;
        private String email;
    }


    @JsonProperty("checkoutUserErrors")
    private List<ShopifyGraphqlError> error;
}
