package com.jeesite.modules.product.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.region.entity.Region;

import java.util.List;
import java.util.Map;

/**
 * 产品参数管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Table(name = "product_extension", alias = "a", label = "产品参数信息", columns = {
        @Column(name="id", attrName="id", label="id", isPK=true),
        @Column(name = "name", attrName = "name", label = "分组名称"),
        @Column(name = "product_id", attrName = "product.id", label = "产品"),
        @Column(name = "content", attrName = "content", label = "内容", isQuery = false),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(includeEntity = Product.class)})
}, orderBy = "a.product_id DESC"
)
public class ProductExtension extends DataEntity<ProductExtension> {

    private static final long serialVersionUID = 1L;
    private String name;        // 分组名称
    private Product product;        // 产品
    private String content;        // 内容
    private List<ProductExtensionDetail> params = ListUtils.newLinkedList();

    public ProductExtension() {
        this(null);
    }

    public ProductExtension(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    @Size(min = 0, max = 1024, message = "内容长度不能超过 1024 个字符")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<ProductExtensionDetail> getParams() {
        return params;
    }

    public void setParams(List<ProductExtensionDetail> params) {
        this.params = params;
    }
}