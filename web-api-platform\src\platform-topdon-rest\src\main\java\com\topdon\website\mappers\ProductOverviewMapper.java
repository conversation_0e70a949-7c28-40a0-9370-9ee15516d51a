package com.topdon.website.mappers;

import com.google.common.collect.Maps;
import com.hiwie.breeze.json.Json;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.model.Product;
import com.topdon.website.model.ProductExtension;
import org.springframework.jdbc.core.RowMapper;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ProductOverviewMapper {

    public static final RowMapper<Map<String, Object>> DETAIL = (rs, index) -> {
        Map<String, Object> detail = Maps.newHashMap();
        detail.put("id", rs.getString("id"));
        detail.put("product", Product.apply(rs.getString("product_id"), rs.getString("product_name")));
        detail.put("type", rs.getString("type"));
        detail.put("image", rs.getString("image"));
        String titleColor = rs.getString("title_color");
        if (StringUtil.isBlank(titleColor)) {
            detail.put("title", rs.getString("title"));
        } else {
            Map<String, String> title = Maps.newHashMap();
            title.put("text", rs.getString("title"));
            title.put("color", titleColor);
            detail.put("title", title);
        }
        String descColor = rs.getString("desc_color");
        if (StringUtil.isBlank(descColor)) {
            detail.put("desc", rs.getString("desc"));
        } else {
            Map<String, String> desc = Maps.newHashMap();
            desc.put("text", rs.getString("desc"));
            desc.put("color", descColor);
            detail.put("desc", desc);
        }
        detail.put("background", rs.getString("background"));

        return detail;
    };

}
