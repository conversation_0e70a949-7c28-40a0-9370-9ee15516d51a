package com.jeesite.modules.region.web;

import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.modules.sys.utils.UserUtils;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.region.entity.Region;
import com.jeesite.modules.region.service.RegionService;

import javax.servlet.http.HttpServletRequest;

/**
 * 区域管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-03
 */
@Controller
@RequestMapping(value = "${adminPath}/region/region")
public class RegionController extends BaseController {

    @Autowired
    private RegionService regionService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public Region get(String code, boolean isNewRecord) {
        return regionService.get(code, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("region:region:view")
    @RequestMapping(value = {"list", ""})
    public String list(Region region, Model model) {
        model.addAttribute("region", region);
        return "modules/region/regionList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("region:region:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public List<Region> listData(Region region) {
        if (StringUtils.isBlank(region.getParentCode())) {
            region.setParentCode(Region.ROOT_CODE);
        }
        if (StringUtils.isNotBlank(region.getName())) {
            region.setParentCode(null);
        }
        if (StringUtils.isNotBlank(region.getFlag())) {
            region.setParentCode(null);
        }
        if (StringUtils.isNotBlank(region.getRemarks())) {
            region.setParentCode(null);
        }
        List<Region> list = regionService.findList(region);
        return list;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("region:region:view")
    @RequestMapping(value = "form")
    public String form(Region region, Model model) {
        // 创建并初始化下一个节点信息
        region = createNextNode(region);
        model.addAttribute("region", region);
        return "modules/region/regionForm";
    }

    /**
     * 创建并初始化下一个节点信息，如：排序号、默认值
     */
    @RequiresPermissions("region:region:edit")
    @RequestMapping(value = "createNextNode")
    @ResponseBody
    public Region createNextNode(Region region) {
        if (StringUtils.isNotBlank(region.getParentCode())) {
            region.setParent(regionService.get(region.getParentCode()));
        }
        if (region.getIsNewRecord()) {
            Region where = new Region();
            where.setParentCode(region.getParentCode());
            Region last = regionService.getLastByParentCode(where);
            // 获取到下级最后一个节点
            if (last != null) {
                region.setTreeSort(last.getTreeSort() + 30);
                region.setCode(IdGen.nextCode(last.getCode()));
            } else if (region.getParent() != null) {
                region.setCode(region.getParent().getCode() + "001");
            }
        }
        // 以下设置表单默认数据
        if (region.getTreeSort() == null) {
            region.setTreeSort(Region.DEFAULT_TREE_SORT);
        }
        return region;
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("region:region:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated Region region, HttpServletRequest request) {
        regionService.save(region);
        return renderResult(Global.TRUE, text("保存区域成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("region:region:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(Region region) {
        regionService.delete(region);
        return renderResult(Global.TRUE, text("删除区域成功！"));
    }

    /**
     * 获取树结构数据
     *
     * @param excludeCode 排除的Code
     * @param isShowCode  是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
     * @return
     */
    @RequiresPermissions("region:region:view")
    @RequestMapping(value = "treeData")
    @ResponseBody
    public List<Map<String, Object>> treeData(String excludeCode, String isShowCode) {
        List<Map<String, Object>> mapList = ListUtils.newArrayList();
        List<Region> list = regionService.findList(new Region());
        for (int i = 0; i < list.size(); i++) {
            Region e = list.get(i);
            // 过滤被排除的编码（包括所有子级）
            if (StringUtils.isNotBlank(excludeCode)) {
                if (e.getId().equals(excludeCode)) {
                    continue;
                }
                if (e.getParentCodes().contains("," + excludeCode + ",")) {
                    continue;
                }
            }
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("id", e.getId());
            map.put("pId", e.getParentCode());
            map.put("name", StringUtils.getTreeNodeName(isShowCode, e.getCode(), e.getName()));
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 修复表结构相关数据
     */
    @RequiresPermissions("region:region:edit")
    @RequestMapping(value = "fixTreeData")
    @ResponseBody
    public String fixTreeData(Region region) {
        if (!UserUtils.getUser().isAdmin()) {
            return renderResult(Global.FALSE, "操作失败，只有管理员才能进行修复！");
        }
        regionService.fixTreeData();
        return renderResult(Global.TRUE, "数据修复成功");
    }

}