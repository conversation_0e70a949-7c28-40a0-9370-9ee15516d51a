package com.jeesite.common.ueditor.upload;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.io.PropertiesUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.ueditor.define.AppInfo;
import com.jeesite.common.ueditor.define.BaseState;
import com.jeesite.common.ueditor.define.State;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.io.*;
import java.util.Optional;

/**
 * <p>
 * 覆盖Jeesite文件上传功能，上传到自己的OSS上
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/13 18:07
 */
@Slf4j
public class StorageManager {

    public static final int BUFFER_SIZE = 8192;

    public static State saveBinaryFile(byte[] data, String path) {
        File file = new File(path);
        State state = valid(file);
        if (!state.isSuccess()) {
            return state;
        }
        BufferedOutputStream bos = null;
        try {
            bos = new BufferedOutputStream(new FileOutputStream(file));
            bos.write(data);
        } catch (IOException ioe) {
            return (State) new BaseState(false, AppInfo.IO_ERROR);
        } finally {
            if (bos != null) {
                try {
                    bos.flush();
                    bos.close();
                } catch (IOException iOException) {
                }
            }
        }
        String allowContentTypes = PropertiesUtils.getInstance().getProperty("file.allowContentTypes");
        if (StringUtils.isNotBlank(allowContentTypes)) {
            String rct = FileUtils.getRealContentType(file);
            if (!StringUtils.inString(rct, allowContentTypes.split(","))) {
                file.delete();
                return (State) new BaseState(false, AppInfo.NOT_ALLOW_FILE_TYPE);
            }
        }
        BaseState baseState = new BaseState(true, file.getAbsolutePath());
        baseState.putInfo("size", data.length);
        baseState.putInfo("title", file.getName());
        return (State) baseState;
    }

    public static State saveFileByInputStream(InputStream is, String path, long maxSize) {
        State state = null;
        File tmpFile = getTmpFile();
        byte[] dataBuf = new byte[2048];
        BufferedInputStream bis = new BufferedInputStream(is, StorageManager.BUFFER_SIZE);
        BufferedOutputStream bos = null;
        try {
            try {
                bos = new BufferedOutputStream(new FileOutputStream(tmpFile), StorageManager.BUFFER_SIZE);
                int count = 0;
                while ((count = bis.read(dataBuf)) != -1) {
                    bos.write(dataBuf, 0, count);
                }
            } finally {
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
            }
            if (tmpFile.length() > maxSize) {
                tmpFile.delete();
                return (State) new BaseState(false, AppInfo.MAX_SIZE);
            }
            String allowContentTypes = PropertiesUtils.getInstance().getProperty("file.allowContentTypes");
            if (StringUtils.isNotBlank(allowContentTypes)) {
                String rct = FileUtils.getRealContentType(tmpFile);
                if (!StringUtils.inString(rct, allowContentTypes.split(","))) {
                    tmpFile.delete();
                    return (State) new BaseState(false, AppInfo.NOT_ALLOW_FILE_TYPE);
                }
            }
            state = saveTmpFile(tmpFile, path);
            if (!state.isSuccess()) {
                tmpFile.delete();
            }
            return state;
        } catch (IOException iOException) {

        } finally {
            if (bis != null)
                try {
                    bis.close();
                } catch (IOException iOException) {
                }
        }
        return (State) new BaseState(false, AppInfo.IO_ERROR);
    }

    public static State saveFileByInputStream(InputStream is, String path) {
        State state = null;
        File tmpFile = getTmpFile();
        byte[] dataBuf = new byte[2048];
        BufferedInputStream bis = new BufferedInputStream(is, StorageManager.BUFFER_SIZE);
        try {
            BufferedOutputStream bos = null;
            try {
                bos = new BufferedOutputStream(new FileOutputStream(tmpFile), StorageManager.BUFFER_SIZE);
                int count = 0;
                while ((count = bis.read(dataBuf)) != -1) {
                    bos.write(dataBuf, 0, count);
                }
            } finally {
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
            }
            state = saveTmpFile(tmpFile, path);
            if (!state.isSuccess()) {
                tmpFile.delete();
            }
            return state;
        } catch (IOException iOException) {

        } finally {
            if (bis != null)
                try {
                    bis.close();
                } catch (IOException iOException) {
                }
        }
        return (State) new BaseState(false, AppInfo.IO_ERROR);
    }

    private static File getTmpFile() {
        File tmpDir = new File(System.getProperty("java.io.tmpdir"));
        return new File(tmpDir, IdGen.randomBase62(10));
    }

    private static State saveTmpFile(File tmpFile, String path) {
        State state = null;
        File targetFile = new File(path);
        if (targetFile.canWrite()) {
            return (State) new BaseState(false, AppInfo.PERMISSION_DENIED);
        }
        try {
            FileUtils.moveFile(tmpFile, targetFile);
        } catch (IOException e) {
            return (State) new BaseState(false, AppInfo.IO_ERROR);
        }
        BaseState baseState = new BaseState(true);
        baseState.putInfo("size", targetFile.length());
        baseState.putInfo("title", targetFile.getName());
        return (State) baseState;
    }

    private static State valid(File file) {
        File parentPath = file.getParentFile();
        if (!parentPath.exists() && !parentPath.mkdirs()) {
            return (State) new BaseState(false, AppInfo.FAILED_CREATE_FILE);
        }
        if (!parentPath.canWrite()) {
            return (State) new BaseState(false, AppInfo.PERMISSION_DENIED);
        }
        return (State) new BaseState(true);
    }

    private static final String endpoint = PropertiesUtils.getInstance().getProperty("file.client.aliyun.endpoint");

    private static final String accessKeyId = PropertiesUtils.getInstance().getProperty("file.client.aliyun.accessKeyId");

    private static final String accessKeySecret = PropertiesUtils.getInstance().getProperty("file.client.aliyun.accessKeySecret");

    private static final String bucketName = PropertiesUtils.getInstance().getProperty("file.client.aliyun.bucketName");

    private static final String proxyEndpoint = Optional.ofNullable(PropertiesUtils.getInstance().getProperty("file.client.aliyun.proxyEndpoint")).filter(Strings::isNotEmpty).orElse(bucketName + "." + endpoint);

    private static final String root = PropertiesUtils.getInstance().getProperty("file.client.aliyun.root");

    public static void uploadFileSuccess(String physicalPath, State storageState) {
        String fileName = StringUtils.substringAfterLast(physicalPath, "/");
        if (!fileName.contains(".")) {
            String[] combo = physicalPath.split("/");
            fileName = combo[combo.length - 1];
        }
        String ossPath = root + "ueditor/" + fileName;
        OSS ossClient = (new OSSClientBuilder()).build(endpoint, accessKeyId, accessKeySecret);
        try {
            if (!ossClient.doesBucketExist(bucketName)) {
                ossClient.createBucket(bucketName);
                CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
                createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
                ossClient.createBucket(createBucketRequest);
            }
            File file = new File(physicalPath);
            ossClient.putObject(new PutObjectRequest(bucketName, ossPath, file));
            String url = "https://" + proxyEndpoint + "/" + ossPath;
            storageState.putInfo("url", url);
        } catch (Exception e) {
            log.error("上传OSS错误,bucketName:{},ossPath:{}", bucketName, ossPath, e);
        } finally {
            ossClient.shutdown();
            // 文件上传后删除临时文件
            FileUtils.deleteFile(physicalPath);
        }
    }
}
