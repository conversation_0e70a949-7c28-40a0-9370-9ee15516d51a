package com.hiwie.breeze;

import com.hiwie.breeze.util.AssertUtil;

import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public final class Left<A, B> extends AbstractEither<A, B> {

    private final A value;

    private Left(A value) {
        AssertUtil.isTrue(Objects.nonNull(value), "value can NOT be null!");
        this.value = value;
    }

    public static <A, B> Left<A, B> apply(final A a) {
        return new Left<>(a);
    }

    @Override
    protected A getLeft() {
        return value;
    }

    @Override
    protected B getRight() {
        throw new NoSuchElementException("getRight() on Left()");
    }

    @Override
    public Boolean isLeft() {
        return true;
    }

    @Override
    public Boolean isRight() {
        return false;
    }

    @Override
    public String toString() {
        return "Left(" + value + ")";
    }
}