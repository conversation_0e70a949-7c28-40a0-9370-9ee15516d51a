package com.topdon.website.model.graphql.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class CartCreate {

    private List<LinesDTO> lines;

    private String id;
    private String checkoutUrl;
    private String webUrl;

    public String getWebUrl() {
        return checkoutUrl;
    }

    private CartBuyerIdentityInput buyerIdentity;

    @NoArgsConstructor
    @Data
    public static class CartBuyerIdentityInput {

        private String email;
    }

    @NoArgsConstructor
    @Data
    public static class LinesDTO {
        private List<AttributesDTO> attributes;
        private String merchandiseId;
        private Integer quantity;
        private String sellingPlanId;

        @NoArgsConstructor
        @Data
        public static class AttributesDTO {
            private String key;
            private String value;
        }
    }

}
