package com.topdon.admin.service;

import com.topdon.admin.dto.ProductPropertyListCategoryIdDTO;
import com.topdon.admin.dto.ProductPropertySaveDTO;
import com.topdon.admin.entity.ProductProperty;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.vo.ProductPropertyVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ProductPropertyService extends IService<ProductProperty>{


    void saveProductProperty(ProductPropertySaveDTO productPropertySaveDTO);

    List<ProductPropertyVo> listByCategoryId(ProductPropertyListCategoryIdDTO productPropertyListCategoryIdDTO);

    String importExcel(MultipartFile file, Integer compareId);
}
