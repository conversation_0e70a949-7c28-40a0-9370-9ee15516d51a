package com.topdon.website.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.topdon.website.service.MenuClickLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/log")
public class MenuClickLogController {

    @Autowired
    private MenuClickLogService logService;

    @PostMapping("/click")
    public ResponseEntity<Void> logMenuClick(
            @RequestParam String menuName,
            @RequestParam String productName,
            HttpServletRequest request
    ) {

        String clientIP = ServletUtil.getClientIP(request);
        String userAgent = request.getHeader("User-Agent");
        logService.logClick(clientIP, menuName, productName, userAgent);
        return ResponseEntity.ok().build();
    }
}
