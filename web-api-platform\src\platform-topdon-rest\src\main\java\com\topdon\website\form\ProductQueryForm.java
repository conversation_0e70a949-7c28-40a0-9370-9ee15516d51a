package com.topdon.website.form;

import com.hiwie.breeze.AbstractOption;

public class ProductQueryForm {
    private String classId;
    private String name;
    private Boolean searchView;
    private Boolean rmaToolShow;

    public void setSearchView(Boolean searchView) {
        this.searchView = searchView;
    }
    public void setRmaToolShow(Boolean rmaToolShow) {
        this.rmaToolShow = rmaToolShow;
    }

    public AbstractOption<Boolean> getSearchView() {
        return AbstractOption.apply(searchView);
    }

    public AbstractOption<Boolean> getRmaToolShow() {
        return AbstractOption.apply(rmaToolShow);
    }

    public AbstractOption<String> getClassId() {
        return AbstractOption.apply(classId);
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public AbstractOption<String> getName() {
        return AbstractOption.apply(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ProductQueryForm classForm(String classId) {
        ProductQueryForm form = new ProductQueryForm();
        form.setClassId(classId);
        return form;
    }
}
