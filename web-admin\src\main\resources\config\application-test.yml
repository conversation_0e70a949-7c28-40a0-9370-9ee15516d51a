#======================================#
#========== Project settings ==========#
#======================================#

# 产品或项目名称、软件开发公司名称
productName: Topdon官网后台管理系统
companyName: 联科科技

# 产品版本、版权年份
productVersion: V4.3
copyrightYear: 2022

#是否演示模式
demoMode: false

#======================================#
#========== Server settings ===========#
#======================================#

server:

  port: 8980
  servlet:
    context-path: /admin
  tomcat:
    uri-encoding: UTF-8
    # 表单请求数据的最大大小
    max-http-form-post-size: 20MB
  #    # 进程的最大连接数
  #    max-connections: 8192
  #    # 连接数满后的排队个数
  #    accept-count: 100
  #    # 线程数最大和最小个数
  #    threads:
  #      max: 200
  #      min-spare: 10

  # 当 Nginx 为 https，tomcat 为 http 时，设置该选项为 true
  schemeHttps: false

#======================================#
#========== Database sttings ==========#
#======================================#

# 数据库连接
jdbc:

  # Mysql 数据库配置
  type: mysql
  driver: com.mysql.cj.jdbc.Driver
  url: *****************************************************************************************************************************************************************
  username: root
  password: Obdlianke@2020
  testSql: SELECT 1


#======================================#
#========== Spring settings ===========#
#======================================#

spring:
  # 应用程序名称
  application:
    name: jeesite-web
  # 打印横幅
  main:
    bannerMode: "off"
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss

# Shiro 相关
shiro:

  #  #索引页路径
  #  defaultPath: ${shiro.loginUrl}
  #
  #  # 登录相关设置
  #  loginUrl: ${adminPath}/login
  #  logoutUrl: ${shiro.loginUrl}
  #  successUrl: ${adminPath}/index
  #
  ##  # Apereo CAS 相关配置（标准版）
  ##  casServerUrl: http://127.0.0.1:8981/cas
  ##  casClientUrl: http://127.0.0.1:8980/js
  ##  loginUrl: ${shiro.casServerUrl}?service=${shiro.casClientUrl}${adminPath}/login-cas
  ##  logoutUrl: ${shiro.casServerUrl}/logout?service=${shiro.loginUrl}
  ##  successUrl: ${shiro.casClientUrl}${adminPath}/index
  #
  #  # 简单 SSO 登录相关配置
  #  sso:
  #    # 如果启用/sso/{username}/{token}单点登录，请修改此安全key并与单点登录系统key一致。
  #    secretKey: ~
  #    # 是否加密单点登录安全Key
  #    encryptKey: true
  #
  #  # 登录提交信息加密（如果不需要加密，设置为空即可）
  #  loginSubmit:
  #    # 加密用户名、密码、验证码，后再提交（key设置为3个，用逗号分隔）加密方式：DES（4.1.9及之前版本默认设置）
  #    # v4.2.0+ 开始支持 Base64 加密方式，方便移动端及第三方系统处理认证，可直接设置 Key 为 Base64（4.2.0+默认设置）
  #    #secretKey: thinkgem,jeesite,com
  #    secretKey: Base64
  #    #secretKey: ~

  # 记住我密钥设置，你可以通过 com.jeesite.test.RememberMeKeyGen 类快速生成一个秘钥。
  # 若不设置，则每次启动系统后自动生成一个新秘钥，这样会导致每次重启后，客户端记录的用户信息将失效。
  rememberMe:
    secretKey: ~

  #  # 指定获取客户端IP的Header名称，防止IP伪造。指定为空，则使用原生方法获取IP。
  #  remoteAddrHeaderName: X-Forwarded-For
  #
  #  # 允许的请求方法设定，解决安全审计问题（BPM设计器用到了PUT或DELETE方法）
  #  allowRequestMethods: GET, POST, OPTIONS, PUT, DELETE
  #
  #  # 是否允许账号多地登录，如果设置为false，同一个设备类型的其它地点登录的相同账号被踢下线
  #  isAllowMultiAddrLogin: true
  #
  #  # 是否允许多账号多设备登录，如果设置为false，其它地点登录的相同账号全部登录设备将被踢下线
  #  isAllowMultiDeviceLogin: true
  #
  #  # 是否允许刷新主框架页，如果设置为false，刷新主页将导致重新登录。如安全性比较高的，如银行个人首页不允许刷新。
  #  isAllowRefreshIndex: true
  #
  #  # 是否允许嵌入到外部网站iframe中（true：不限制，false：不允许）
  #  isAllowExternalSiteIframe: true
  #
  #  # 设定允许获取的资源列表（v4.2.3）
  #  #contentSecurityPolicy: "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; img-src 'self' 'unsafe-inline' 'unsafe-eval' data:"
  #
  #  # 是否允许跨域访问 CORS，如果允许，设置允许的域名。v4.2.3 开始支持多个域名和模糊匹配，例如：http://*.jeesite.com,http://*.jeesite.net
  #  #accessControlAllowOrigin: '*'
  #
  #  # 允许跨域访问时 CORS，可以获取和返回的方法和请求头
  #  #accessControlAllowMethods: GET, POST, OPTIONS
  #  #accessControlAllowHeaders: content-type, x-requested-with, x-ajax, x-token, x-remember
  #  #accessControlExposeHeaders: x-remember
  #
  #  # 是否允许接收跨域的Cookie凭证数据 CORS
  #  #accessControlAllowCredentials: false
  #
  #  # 允许的网站来源地址，不设置为全部地址（避免一些跨站点请求伪造 CSRF、防盗链）
  #  #allowReferers: http://127.0.0.1,http://localhost
  #
  #  # 是否在登录后生成新的Session（默认false）
  #  isGenerateNewSessionAfterLogin: false
  #
  #  # 内部系统访问过滤器，可设置多个允许的内部系统IP地址串，多个用逗号隔开
  #  innerFilter.allowIp: 127.0.0.1
  #
  #  # URI 权限过滤器定义（自定义添加参数时，请不要移除 ${adminPath}/** = user，否则会导致权限异常）
  #  filterChainDefinitions: |
  #    ${adminPath}/** = user


#======================================#
#======== FileUpload settings =========#
#======================================#

#视频转码
video:
  #
  #  # 视频格式转换  ffmpeg.exe 所放的路径
  ffmpegFile: /usr/local/bin/ffmpeg
  qtFaststartFile: /usr/bin/qtfaststart

file:
  enabled: true
  client:
    aliyun:
      endpoint: oss-cn-shenzhen.aliyuncs.com
      accessKeyId: LTAI5tJkqkY6aLfpMZpxRyeC
      accessKeySecret: ******************************
      bucketName: lenkor-oss-dev
      root: topdon-web/
      proxyEndpoint:
  #
  #  # 文件上传根路径，设置路径中不允许包含“userfiles”，在指定目录中系统会自动创建userfiles目录，如果不设置默认为contextPath路径
  #  baseDir: /var/log
  #
  #  # 上传文件的相对路径（支持：yyyy, MM, dd, HH, mm, ss, E）
  #  uploadPath: '{yyyy}{MM}/'
  #
  #  # 上传单个文件最大字节（500M），在这之上还有 > Tomcat限制 > Nginx限制，等，此设置会覆盖 spring.http.multipart.maxFileSize 设置
  maxFileSize: 800*1024*1024
  #
  #  # 设置允许上传的文件后缀（全局设置）
  #  imageAllowSuffixes: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,
  #  mediaAllowSuffixes: .flv,.swf,.mkv,webm,.mid,.mov,.mp3,.mp4,.m4v,.mpc,.mpeg,.mpg,.swf,.wav,.wma,.wmv,.avi,.rm,.rmi,.rmvb,.aiff,.asf,.ogg,.ogv,
  #  fileAllowSuffixes: .doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,
  #
  #  # 允许上传的文件内容类型（图片、word、excel、ppt）防止修改后缀恶意上传文件（默认不启用验证）
  ##  allowContentTypes: image/jpeg,image/gif,image/bmp,image/png,image/x-png,
  ##    application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,
  ##    application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
  ##    application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation
  #
  #  # 上传图片自动压缩宽高，指定为 -1 不进行压缩（全局设置）  v4.1.7
  imageMaxWidth: -1
  imageMaxHeight: -1
  #
  #  # 是否启用秒传
  #  checkmd5: true
  #
  #  # 是否开启分片上传
  #  chunked: true
  #  # 分片大小，单位字节（10M）
  #  chunkSize: '10*1024*1024'
  #  # 最大上传线程数
  #  threads: 3
  #
  #  # 是否启用检查点（支持断点续传，上传）
  #  checkpoint: true
  #
  #  # 是否用文件流方式下载（支持断点续传）
  #  isFileStreamDown: true

  #======================================#
  #========== Message settings ==========#
  #======================================#

  # 消息提醒中心（专业版）
  #msg:
  #  enabled: false
  #
  #  # 是否开启实时发送消息（保存消息后立即检查未读消息并发送），分布式部署下请单独配置消息发送服务，不建议开启此选项。
  #  realtime:
  #    # 是否开启
  #    enabled: true
  #    # 消息实时推送任务Bean名称
  #    beanName: msgLocalPushTask

  #  # 推送失败次数，如果推送次数超过了设定次数，仍不成功，则放弃并保存到历史
  #  pushFailNumber: 3

  # 邮件发送参数
  email:
    beanName: emailSendService
    fromAddress: <EMAIL>
    fromPassword: 55bedd8ec71e80dfed00
    fromHostName: smtp.139.com
    sslOnConnect: true
    sslSmtpPort: 465
#  
#  # 短信网关
#  sms:
#    beanName: smsSendService
#    url: http://localhost:80/msg/sms/send
#    data: username=jeesite&password=jeesite.com
#    prefix: 【JeeSite】
#    suffix: ~

#======================================#
#========== Project settings ==========#
#======================================#
logging:
  level:
    root: info
  config: classpath:config/logback-server.xml
  file:
    path: logs