package com.jeesite.modules.product.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 产品推荐管理Entity
 *
 * <AUTHOR>
 * @version 2022-04-26
 */
@Table(name = "product_relation", alias = "a", label = "产品推荐信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "product_id", attrName = "product.id", label = "产品"),
        @Column(name = "relation_product_id", attrName = "relationProduct.id", label = "推荐产品"),
        @Column(name = "create_date", attrName = "createDate", label = "创建时间", isUpdate = false, isQuery = false, isUpdateForce = true),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(name = "name", label = "产品名称", queryType = QueryType.LIKE),}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p1",
                on = "p1.id = a.relation_product_id", attrName = "relationProduct",
                columns = {@Column(name = "name", label = "产品名称", queryType = QueryType.LIKE),})
}, orderBy = "a.id DESC"
)
public class ProductRelation extends DataEntity<ProductRelation> {

    private static final long serialVersionUID = 1L;
    private Product product;        // 产品
    private Product relationProduct;        // 推荐产品

    public ProductRelation() {
        this(null);
    }

    public ProductRelation(String id) {
        super(id);
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public Product getRelationProduct() {
        return relationProduct;
    }

    public void setRelationProduct(Product relationProduct) {
        this.relationProduct = relationProduct;
    }

}