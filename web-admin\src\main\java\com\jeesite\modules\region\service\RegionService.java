package com.jeesite.modules.region.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.TreeService;
import com.jeesite.modules.region.entity.Region;
import com.jeesite.modules.region.dao.RegionDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 区域管理Service
 * <AUTHOR>
 * @version 2022-03-03
 */
@Service
@Transactional(readOnly=true)
public class RegionService extends TreeService<RegionDao, Region> {
	
	/**
	 * 获取单条数据
	 * @param region
	 * @return
	 */
	@Override
	public Region get(Region region) {
		return super.get(region);
	}
	
	/**
	 * 查询分页数据
	 * @param region 查询条件
	 * @param region.page 分页对象
	 * @return
	 */
	@Override
	public Page<Region> findPage(Region region) {
		return super.findPage(region);
	}
	
	/**
	 * 查询列表数据
	 * @param region
	 * @return
	 */
	@Override
	public List<Region> findList(Region region) {
		return super.findList(region);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param region
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(Region region) {
		super.save(region);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(region, region.getId(), "region_image");
	}
	
	/**
	 * 更新状态
	 * @param region
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(Region region) {
		super.updateStatus(region);
	}
	
	/**
	 * 删除数据
	 * @param region
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(Region region) {
		super.delete(region);
	}
	
}