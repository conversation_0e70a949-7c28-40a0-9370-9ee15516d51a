<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MyBatisCodeHelperDatasource">
    <option name="projectProfile">
      <ProjectProfile>
        <option name="controllerTemplateString" value="&#10;#* @vtlvariable name=&quot;tableName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;servicePackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfacePackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfaceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;controllerPackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;tableRemark&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;myDate&quot; type=&quot;java.util.Date&quot; *#&#10;#* @vtlvariable name=&quot;simpleDateFormat&quot; type=&quot;java.text.SimpleDateFormat&quot; *#&#10;package $!{controllerPackage};&#10;import $!{entityPackageName}.$!{entityClassName};&#10;###set($realServiceName = $!{serviceClassName}+'Impl')&#10;import $!{servicePackageName}.$!{serviceClassName};&#10;import org.springframework.web.bind.annotation.*;&#10;&#10;#set($serviceFirstLower = $!{serviceClassName.substring(0,1).toLowerCase()}+$!{serviceClassName.substring(1,$!{serviceClassName.length()})})&#10;import org.springframework.beans.factory.annotation.Autowired;&#10;&#10;/**&#10;* $!{tableRemark}($!{tableName})表控制层&#10;*&#10;* <AUTHOR> class $!{entityClassName}Controller {&#10;/**&#10;* 服务对象&#10;*/&#10;    @Autowired&#10;    private $!{serviceClassName} $!{serviceFirstLower};&#10;&#10;    /**&#10;    * 通过主键查询单条数据&#10;    *&#10;    * @param id 主键&#10;    * @return 单条数据&#10;    */&#10;    @GetMapping(&quot;selectOne&quot;)&#10;    public $!{entityClassName} selectOne(Integer id) {&#10;    return $!{serviceFirstLower}.selectByPrimaryKey(id);&#10;    }&#10;&#10;}" />
        <option name="generateService" value="true" />
        <option name="generateServiceInterface" value="true" />
        <option name="javaMapperPackage" value="com.topdon.website.mapper" />
        <option name="javaMapperPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
        <option name="javaModelPackage" value="com.topdon.website.entity" />
        <option name="javaModelPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
        <option name="lastDatabaseCrudChooseModuleName" value="platform-topdon-rest" />
        <option name="lombokAllArgConstructor" value="true" />
        <option name="lombokDataAnnotation" value="true" />
        <option name="lombokNoArgsConstructor" value="true" />
        <option name="moduleNameToPackageAndPathMap">
          <map>
            <entry key="breeze-unit-test">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.topdon.admin.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/java" />
                  <option name="javaModelPacakge" value="com.topdon.admin.entity" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.topdon.admin.service" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/java" />
                  <option name="javaServicePackage" value="com.topdon.admin.service.impl" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/java" />
                  <option name="xmlPackage" value="com.topdon.admin.mapper" />
                  <option name="xmlPath" value="$PROJECT_DIR$/web-admin/src/main/resources/mappings/modules" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
            <entry key="jeesite-web">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.topdon.admin.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/web-admin/src/main/java" />
                  <option name="javaModelPacakge" value="com.topdon.admin.entity" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/web-admin/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.topdon.admin.service" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/web-admin/src/main/java" />
                  <option name="javaServicePackage" value="com.topdon.admin.service.impl" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/web-admin/src/main/java" />
                  <option name="xmlPackage" value="mapper" />
                  <option name="xmlPath" value="$PROJECT_DIR$/web-admin/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
            <entry key="platform-topdon-rest">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.topdon.website.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
                  <option name="javaModelPacakge" value="com.topdon.website.entity" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.topdon.website.service" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
                  <option name="javaServicePackage" value="com.topdon.website.service.impl" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" />
                  <option name="xmlPackage" value="mapper" />
                  <option name="xmlPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
          </map>
        </option>
        <option name="mybatisPlusIdType" value="AUTO" />
        <option name="tableGenerateConfigs">
          <map>
            <entry key="topdon-dev:authorized_platforms">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="AuthorizedPlatforms" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:classification_compare">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="ClassificationCompare" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:classification_extension_link">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="ClassificationExtensionLink" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:email_subscribe">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="EmailSubscribe" />
                  <option name="moduleName" value="jeesite-web" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:information">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="Information" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:information_group">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="InformationGroup" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:menu_click_log">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="MenuClickLog" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:news">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="News" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:official_website">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="OfficialWebsite" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:product">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="Product" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:product_lines">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="ProductLines" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:product_property">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="ProductProperty" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:property_category">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="PropertyCategory" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:region">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="Region" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:seller_auth_product_lines_mapping">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="SellerAuthProductLinesMapping" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:seller_auth_region_mapping">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="SellerAuthRegionMapping" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:seller_auth_violations">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="SellerAuthViolations" />
                  <option name="moduleName" value="jeesite-web" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="topdon-dev:seller_authorization">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="id" />
                  <option name="javaModelName" value="SellerAuthorization" />
                  <option name="moduleName" value="platform-topdon-rest" />
                  <option name="mybatisplusIdType" value="AUTO" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
          </map>
        </option>
        <option name="userMybatisPlus" value="true" />
        <option name="xmlMapperPackage" value="mapper" />
        <option name="xmlMapperPath" value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/resources" />
      </ProjectProfile>
    </option>
  </component>
</project>