package com.topdon.website.mappers;

import com.topdon.website.model.AuthorizedDealer;
import com.topdon.website.model.News;
import com.topdon.website.model.Region;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class AuthorizedDealerMapper {

    public static final RowMapper<AuthorizedDealer> DETAIL = (rs, index) -> {
        AuthorizedDealer detail = new AuthorizedDealer();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setCover(rs.getString("cover"));
        detail.setCountry(Region.defaultFk(rs.getString("region_id"), rs.getString("region_name"), rs.getString("flag")));
        detail.setAddress(rs.getString("address"));
        detail.setLink(rs.getString("link"));
        detail.setEmail(rs.getString("email"));
        detail.setPhone(rs.getString("phone"));
        detail.setCreateAt(rs.getTimestamp("create_at").toLocalDateTime());
        return detail;
    };

}
