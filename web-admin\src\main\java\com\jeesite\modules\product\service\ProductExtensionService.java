package com.jeesite.modules.product.service;

import java.util.List;
import java.util.Map;

import com.beust.jcommander.internal.Maps;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mapper.JsonMapper;
import com.jeesite.modules.product.entity.ProductExtensionDetail;
import com.jeesite.modules.test.entity.TestData;
import com.jeesite.modules.test.entity.TestDataChild;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductExtension;
import com.jeesite.modules.product.dao.ProductExtensionDao;

/**
 * 产品参数管理Service
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Service
@Transactional(readOnly = true)
public class ProductExtensionService extends CrudService<ProductExtensionDao, ProductExtension> {

    /**
     * 获取单条数据
     *
     * @param productExtension
     * @return
     */
    @Override
    public ProductExtension get(ProductExtension productExtension) {
        ProductExtension entity = super.get(productExtension);
        if (entity != null) {
            Map<String, String> map = JsonMapper.fromJson(entity.getContent(), Map.class);
            List<ProductExtensionDetail> details = Lists.newArrayList();
            int index = 1;
            for (String key : map.keySet()) {
                ProductExtensionDetail detail = new ProductExtensionDetail();
                detail.setKey(key);
                detail.setId(String.valueOf(index));
                detail.setValue(map.get(key));
                index++;
                details.add(detail);
            }
            entity.setParams(details);
        }
        return entity;
    }

    /**
     * 查询分页数据
     *
     * @param productExtension      查询条件
     * @param productExtension.page 分页对象
     * @return
     */
    @Override
    public Page<ProductExtension> findPage(ProductExtension productExtension) {
        Page<ProductExtension> page = super.findPage(productExtension);
        return super.findPage(productExtension);
    }

    /**
     * 查询列表数据
     *
     * @param productExtension
     * @return
     */
    @Override
    public List<ProductExtension> findList(ProductExtension productExtension) {
        return super.findList(productExtension);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param productExtension
     */
    @Override
    @Transactional(readOnly = false)
    public void save(ProductExtension productExtension) {
        Map<String, String> param = Maps.newLinkedHashMap();
        for (ProductExtensionDetail detail : productExtension.getParams()) {
            if (DataEntity.STATUS_DELETE.equals(detail.getStatus())){
                continue;
            }
            param.put(detail.getKey(), detail.getValue());
        }
        productExtension.setContent(JsonMapper.toJson(param));
        super.save(productExtension);
    }

    /**
     * 更新状态
     *
     * @param productExtension
     */
    @Override
    @Transactional(readOnly = false)
    public void updateStatus(ProductExtension productExtension) {
        super.updateStatus(productExtension);
    }

    /**
     * 删除数据
     *
     * @param productExtension
     */
    @Override
    @Transactional(readOnly = false)
    public void delete(ProductExtension productExtension) {
        super.delete(productExtension);
    }

}