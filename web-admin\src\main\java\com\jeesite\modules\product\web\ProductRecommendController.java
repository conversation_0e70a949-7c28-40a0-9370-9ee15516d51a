package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductRecommend;
import com.jeesite.modules.product.service.ProductRecommendService;

/**
 * 产品推荐管理Controller
 * <AUTHOR>
 * @version 2022-07-13
 */
@Controller
@RequestMapping(value = "${adminPath}/product/productRecommend")
public class ProductRecommendController extends BaseController {

	@Autowired
	private ProductRecommendService productRecommendService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ProductRecommend get(String id, boolean isNewRecord) {
		return productRecommendService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("product:productRecommend:view")
	@RequestMapping(value = {"list", ""})
	public String list(ProductRecommend productRecommend, Model model) {
		model.addAttribute("productRecommend", productRecommend);
		return "modules/product/productRecommendList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("product:productRecommend:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ProductRecommend> listData(ProductRecommend productRecommend, HttpServletRequest request, HttpServletResponse response) {
		productRecommend.setPage(new Page<>(request, response));
		Page<ProductRecommend> page = productRecommendService.findPage(productRecommend);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("product:productRecommend:view")
	@RequestMapping(value = "form")
	public String form(ProductRecommend productRecommend, Model model) {
		model.addAttribute("productRecommend", productRecommend);
		return "modules/product/productRecommendForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("product:productRecommend:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ProductRecommend productRecommend) {
		productRecommendService.save(productRecommend);
		return renderResult(Global.TRUE, text("保存推荐产品成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("product:productRecommend:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ProductRecommend productRecommend) {
		productRecommendService.delete(productRecommend);
		return renderResult(Global.TRUE, text("删除推荐产品成功！"));
	}
	
}