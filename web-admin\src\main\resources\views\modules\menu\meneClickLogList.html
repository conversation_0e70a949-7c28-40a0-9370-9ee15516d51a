<% layout('/layouts/default.html', {title: '菜单日志', libs: ['dataGrid']}){ %>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('菜单日志')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i>
					${text('导出')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="" action="${ctx}/menu/click/log/listData" method="post" class="form-inline"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('开始时间')}：</label>
				<div class="control-inline">
					<#form:input path="startDate" value="${startDate}" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('结束时间')}：</label>
				<div class="control-inline">
					<#form:input path="endDate" value="${endDate}" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	$('#btnExport').click(function () {
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/menu/click/log/exportData',
			downloadFile: true
		});
	});

// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("点击时间")}', name:'clickTime', index:'a.click_time', width:150, align:"center"},
		{header:'${text("产品名称")}', name:'productName', index:'a.product_name', width:150, align:"center"},
		{header:'${text("菜单名称")}', name:'menuName', index:'a.menu_name', width:150, align:"center"},
		{header:'${text("IP归属地")}', name:'ipLocation', index:'a.ip_location', width:150, align:"center"},
		{header:'${text("IP地址")}', name:'ipAddress', index:'a.ip_address', width:150, align:"center"},
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
</script>