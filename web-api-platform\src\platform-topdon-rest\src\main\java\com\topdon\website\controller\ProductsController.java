package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.ProductQueryForm;
import com.topdon.website.form.TopdonPageForm;
import com.topdon.website.form.TopdonProductQueryForm;
import com.topdon.website.services.ProductServices;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("products")
public class ProductsController extends ControllerSupport {
    private final ProductServices productService;

    @Autowired
    public ProductsController(ProductServices productService) {
        this.productService = productService;
    }

    @GetMapping
    public AbstractRestResponse list(ProductQueryForm queryForm) {
        queryForm.setSearchView(true);
        return AbstractRestResponse.apply(productService.list(queryForm));
    }

    @GetMapping("/list")
    public AbstractRestResponse getList(ProductQueryForm queryForm) {
        return AbstractRestResponse.apply(productService.list(queryForm));
    }

    @GetMapping("/name")
    public AbstractRestResponse get(String productName,String draft){
        return AbstractRestResponse.apply(productService.getByName(productName,draft));
    }

    // 不用了，废了
    @GetMapping("/topdon")
    public AbstractRestResponse listTopdon(TopdonProductQueryForm queryForm, TopdonPageForm pageForm) {
        return AbstractRestResponse.apply(productService.listTopdon(queryForm, pageForm));
    }

}
