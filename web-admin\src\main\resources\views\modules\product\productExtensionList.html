<% layout('/layouts/default.html', {title: '产品参数管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('产品参数管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('product:productExtension:edit')){ %>
					<a href="${ctx}/product/productExtension/form" class="btn btn-default btnTool" title="${text('新增产品参数')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${productExtension}" action="${ctx}/product/productExtension/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('产品')}：</label>
					<div class="control-inline">
						<#form:input path="product.name" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("分组名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/product/productExtension/form?id='+row.id+'" class="btnList" data-title="${text("编辑产品参数")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("产品")}', name:'product.name', index:'a.product_name', width:150, align:"left"},
		{header:'${text("内容")}', name:'content', index:'a.content', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('product:productExtension:edit')){ %>
				actions.push('<a href="${ctx}/product/productExtension/form?id='+row.id+'" class="btnList" title="${text("编辑产品参数")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/product/productExtension/delete?id='+row.id+'" class="btnList" title="${text("删除产品参数")}" data-confirm="${text("确认要删除该产品参数吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>