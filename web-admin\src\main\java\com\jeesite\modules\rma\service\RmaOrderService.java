package com.jeesite.modules.rma.service;

import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.rma.dao.RmaOrderDao;
import com.jeesite.modules.rma.entity.RmaOrder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * RMA工单Service
 *
 * <AUTHOR>
 * @version 2024-07-10
 */
@Service
@Transactional(readOnly = true)
public class RmaOrderService extends CrudService<RmaOrderDao, RmaOrder> {

    /**
     * 获取单条数据
     *
     * @param rmaOrder
     * @return
     */
    @Override
    public RmaOrder get(RmaOrder rmaOrder) {
        RmaOrder result = super.get(rmaOrder);
        Optional.ofNullable(result).map(BaseEntity::getId).map(Integer::valueOf).map(this.dao::getByRmaOrderId).ifPresent(o -> result.setParams(o));
        return result;
    }

    /**
     * 查询分页数据
     *
     * @param rmaOrder      查询条件
     * @param rmaOrder.page 分页对象
     * @return
     */
    @Override
    public Page<RmaOrder> findPage(RmaOrder rmaOrder) {
        return super.findPage(rmaOrder);
    }

    /**
     * 查询列表数据
     *
     * @param rmaOrder
     * @return
     */
    @Override
    public List<RmaOrder> findList(RmaOrder rmaOrder) {
        return super.findList(rmaOrder);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param rmaOrder
     */
    @Override
    @Transactional(readOnly = false)
    public void save(RmaOrder rmaOrder) {
        super.save(rmaOrder);
    }

    /**
     * 更新状态
     *
     * @param rmaOrder
     */
    @Override
    @Transactional(readOnly = false)
    public void updateStatus(RmaOrder rmaOrder) {
        super.updateStatus(rmaOrder);
    }

    /**
     * 删除数据
     *
     * @param rmaOrder
     */
    @Override
    @Transactional(readOnly = false)
    public void delete(RmaOrder rmaOrder) {
        super.delete(rmaOrder);
    }

}