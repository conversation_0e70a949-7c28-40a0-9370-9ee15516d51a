package com.jeesite.modules.work.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.region.entity.Region;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.utils.DictUtils;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.Optional;

/**
 * 工单管理Entity
 *
 * <AUTHOR>
 * @version 2022-03-10
 */
@Table(name = "work_order", alias = "a", label = "工单信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "last_name", attrName = "lastName", label = "姓", isQuery = false),
        @Column(name = "first_name", attrName = "firstName", label = "名", isQuery = false),
        @Column(name = "region_id", attrName = "region.id", label = "区域", isQuery = false),
        @Column(name = "product_id", attrName = "productName", label = "产品", isQuery = false),
        @Column(name = "email", attrName = "email", label = "电子邮箱"),
        @Column(name = "vin", attrName = "vin", label = "Vehicle Info/VIN"),
        @Column(name = "serial_number", attrName = "serialNumber", label = "序列号"),
        @Column(name = "buy_channel", attrName = "buyChannel", label = "购买渠道"),
        @Column(name = "channel_info", attrName = "channelInfo", label = "渠道信息"),
        @Column(name = "describe", attrName = "describe", label = "问题描述"),
        @Column(name = "create_at", attrName = "createDate", label = "时间", isQuery = false, isUpdateForce = true),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Region.class, alias = "r",
                on = "r.code = a.region_id", attrName = "region",
                columns = {@Column(includeEntity = Region.class)})
}, orderBy = "a.id DESC"
)
@Data
public class WorkOrder extends DataEntity<WorkOrder> {

    public static final String DICT_BUY_CHANNEL = "buy_channel";
    private static final long serialVersionUID = 1L;
    private String lastName;        // 姓
    private String firstName;        // 名
    private Region region;        // 区域
    private String productName;        // 产品
    private String email;        // 电子邮箱
    private String vin;        // vin
    private String serialNumber;        // 序列号
    private String buyChannel;        // 购买渠道
    private String channelInfo;        // 渠道信息
    private String describe;        // 问题描述
    private Date createDate;        // 时间

    @ExcelFields({
            @ExcelField(title = "姓", attrName = "lastName", align = ExcelField.Align.LEFT, sort = 50),
            @ExcelField(title = "名", attrName = "firstName", align = ExcelField.Align.CENTER, sort = 60),
            @ExcelField(title = "区域", attrName = "region.name", align = ExcelField.Align.CENTER, sort = 70),
            @ExcelField(title = "产品", attrName = "productName", align = ExcelField.Align.CENTER, sort = 80),
            @ExcelField(title = "电子邮箱", attrName = "email", align = ExcelField.Align.CENTER, sort = 90),
            @ExcelField(title = "序列号", attrName = "serialNumber", align = ExcelField.Align.CENTER, sort = 100),
            @ExcelField(title = "购买渠道", attrName = "buyChannelName", align = ExcelField.Align.CENTER, sort = 100),
            @ExcelField(title = "渠道信息", attrName = "channelInfo", align = ExcelField.Align.CENTER, sort = 100),
            @ExcelField(title = "Vehicle Info/VIN", attrName = "vin", align = ExcelField.Align.CENTER, sort = 110),
            @ExcelField(title = "问题描述", attrName = "describe", align = ExcelField.Align.CENTER, sort = 120),
            @ExcelField(title = "申请时间", attrName = "createDate", align = ExcelField.Align.CENTER, sort = 130, dataFormat = "yyyy-MM-dd HH:mm:ss"),
    })
    public WorkOrder() {
        this(null);
    }

    public WorkOrder(String id) {
        super(id);
    }

    @Size(min = 0, max = 40, message = "姓长度不能超过 40 个字符")
    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Size(min = 0, max = 40, message = "名长度不能超过 40 个字符")
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Region getRegion() {
        return region;
    }

    public void setRegion(Region region) {
        this.region = region;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Size(min = 0, max = 50, message = "电子邮箱长度不能超过 50 个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = 40, message = "序列号长度不能超过 40 个字符")
    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    @Size(min = 0, max = 1024, message = "问题描述长度不能超过 1024 个字符")
    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getBuyChannelName() {
        return Optional.ofNullable(DictUtils.getDictData(DICT_BUY_CHANNEL, buyChannel)).map(DictData::getDictLabel).orElse("");
    }
}