package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.modules.product.entity.Product;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.ProductExtension;
import com.jeesite.modules.product.service.ProductExtensionService;

/**
 * 产品参数管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Controller
@RequestMapping(value = "${adminPath}/product/productExtension")
public class ProductExtensionController extends BaseController {

    @Autowired
    private ProductExtensionService productExtensionService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public ProductExtension get(String id, boolean isNewRecord) {
        return productExtensionService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("product:productExtension:view")
    @RequestMapping(value = {"list", ""})
    public String list(ProductExtension productExtension, Model model) {
        model.addAttribute("productExtension", productExtension);
        return "modules/product/productExtensionList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("product:productExtension:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<ProductExtension> listData(ProductExtension productExtension, HttpServletRequest request, HttpServletResponse response) {
        productExtension.setPage(new Page<>(request, response));
        Page<ProductExtension> page = productExtensionService.findPage(productExtension);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("product:productExtension:view")
    @RequestMapping(value = "form")
    public String form(ProductExtension productExtension, Model model) {
        model.addAttribute("productExtension", productExtension);
        return "modules/product/productExtensionForm";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("product:productExtension:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated ProductExtension productExtension) {
        productExtensionService.save(productExtension);
        return renderResult(Global.TRUE, text("保存产品参数成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("product:productExtension:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(ProductExtension productExtension) {
        productExtensionService.delete(productExtension);
        return renderResult(Global.TRUE, text("删除产品参数成功！"));
    }

}