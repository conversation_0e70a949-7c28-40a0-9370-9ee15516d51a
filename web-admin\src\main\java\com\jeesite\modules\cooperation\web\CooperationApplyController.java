package com.jeesite.modules.cooperation.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.cooperation.entity.CooperationApply;
import com.jeesite.modules.cooperation.service.CooperationApplyService;

import java.util.List;

/**
 * 申请管理Controller
 * <AUTHOR>
 * @version 2022-03-08
 */
@Controller
@RequestMapping(value = "${adminPath}/cooperation/cooperationApply")
public class CooperationApplyController extends BaseController {

	@Autowired
	private CooperationApplyService cooperationApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public CooperationApply get(String id, boolean isNewRecord) {
		return cooperationApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("cooperation:cooperationApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(CooperationApply cooperationApply, Model model) {
		model.addAttribute("cooperationApply", cooperationApply);
		return "modules/cooperation/cooperationApplyList";
	}


	/**
	 * 导出用户数据
	 */
	@RequiresPermissions("cooperation:cooperationApply:view")
	@RequestMapping(value = "exportData")
	public void exportData(CooperationApply cooperationApply, HttpServletResponse response) {
		List<CooperationApply> list = cooperationApplyService.findList(cooperationApply);
		String fileName = "申请数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("申请数据", CooperationApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("cooperation:cooperationApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<CooperationApply> listData(CooperationApply cooperationApply, HttpServletRequest request, HttpServletResponse response) {
		cooperationApply.setPage(new Page<>(request, response));
		Page<CooperationApply> page = cooperationApplyService.findPage(cooperationApply);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("cooperation:cooperationApply:view")
	@RequestMapping(value = "form")
	public String form(CooperationApply cooperationApply, Model model) {
		model.addAttribute("cooperationApply", cooperationApply);
		return "modules/cooperation/cooperationApplyForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("cooperation:cooperationApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated CooperationApply cooperationApply) {
		cooperationApplyService.save(cooperationApply);
		return renderResult(Global.TRUE, text("保存申请成功！"));
	}
	
}