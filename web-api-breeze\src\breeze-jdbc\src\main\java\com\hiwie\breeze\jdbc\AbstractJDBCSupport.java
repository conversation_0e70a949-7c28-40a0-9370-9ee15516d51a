package com.hiwie.breeze.jdbc;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.util.StringUtil;
import org.intellij.lang.annotations.Language;
import org.springframework.dao.support.DataAccessUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.RowMapperResultSetExtractor;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbstractJDBCSupport {

    private final JdbcTemplate db;

    private final NamedParameterJdbcTemplate namedDB;

    protected AbstractJDBCSupport(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        this.db = db;
        this.namedDB = namedDB;
    }

    protected static String like(String value) {
        return StringUtil.join("%", value, "%");
    }

    protected static String prefixLike(String value) {
        return StringUtil.join(value, "%");
    }

    protected static String suffixLike(String value) {
        return StringUtil.join("%", value);
    }

    protected int[] batchUpdate(@Language("SQL") String sql, List<MapSqlParameterSource> params) {
        return namedDB.batchUpdate(sql, params.toArray(new MapSqlParameterSource[0]));
    }

    protected <T> AbstractOption<T> option(@Language("SQL") String sql, RowMapper<T> mapper) {
        return AbstractOption.apply(DataAccessUtils.singleResult(db.query(sql, new RowMapperResultSetExtractor<>(mapper, 1))));
    }

    protected <T> AbstractOption<T> option(@Language("SQL") String sql, RowMapper<T> mapper, MapSqlParameterSource params) {
        return AbstractOption.apply(DataAccessUtils.singleResult(namedDB.query(sql, params, new RowMapperResultSetExtractor<>(mapper, 1))));
    }

    protected <T> T get(@Language("SQL") String sql, ResultSetExtractor<T> rse, MapSqlParameterSource params) {
        return namedDB.query(sql, params, rse);
    }

    protected <T> T get(@Language("SQL") String sql, RowMapper<T> mapper) {
        return DataAccessUtils.singleResult(db.query(sql, new RowMapperResultSetExtractor<>(mapper, 1)));
    }

    protected <T> T get(@Language("SQL") String sql, RowMapper<T> mapper, MapSqlParameterSource params) {
        return DataAccessUtils.singleResult(namedDB.query(sql, params, new RowMapperResultSetExtractor<>(mapper, 1)));
    }

    protected <T> List<T> list(@Language("SQL") String sql, RowMapper<T> mapper) {
        return db.query(sql, mapper);
    }

    protected <T> List<T> list(@Language("SQL") String sql, ResultSetExtractor<List<T>> rse) {
        return db.query(sql, rse);
    }

    protected <T> List<T> list(@Language("SQL") String sql, ResultSetExtractor<List<T>> rse, MapSqlParameterSource params) {
        return namedDB.query(sql, params, rse);
    }

    protected <T> List<T> list(@Language("SQL") String sql, Class<T> clazz, MapSqlParameterSource params) {
        return namedDB.queryForList(sql, params, clazz);
    }

    protected void execute(@Language("SQL") String sql) {
        db.execute(sql);
    }

    protected <T> List<T> list(@Language("SQL") String sql, RowMapper<T> mapper, MapSqlParameterSource params) {
        return namedDB.query(sql, params, mapper);
    }

    protected <T> T object(@Language("SQL") String sql, ResultSetExtractor<T> rse, MapSqlParameterSource params) {
        return namedDB.query(sql, params, rse);
    }

    protected <T> T object(@Language("SQL") String sql, Class<T> clazz, MapSqlParameterSource params) {
        return namedDB.queryForObject(sql, params, clazz);
    }

    protected <T> T object(@Language("SQL") String sql, Class<T> clazz) {
        return db.queryForObject(sql, clazz);
    }

    protected Number autoIncreaseInsert(@Language("SQL") String sql, MapSqlParameterSource params) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        namedDB.update(sql, params, keyHolder);
        return keyHolder.getKey();
    }

    protected Number autoIncreaseInsert(@Language("SQL") String sql, MapSqlParameterSource params, String keyColumnNames) {
        KeyHolder keyHolder = new GeneratedKeyHolder();
        namedDB.update(sql, params, keyHolder, new String[]{keyColumnNames});
        return keyHolder.getKey();
    }

    protected int update(@Language("SQL") String sql) {
        return db.update(sql);
    }

    protected int update(@Language("SQL") String sql, MapSqlParameterSource params) {
        return namedDB.update(sql, params);
    }

}
