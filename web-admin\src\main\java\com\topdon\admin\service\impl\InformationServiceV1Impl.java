package com.topdon.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeesite.common.entity.Page;
import com.topdon.admin.entity.Information;
import com.topdon.admin.mapper.InformationMapper;
import com.topdon.admin.service.InformationServiceV1;
import com.topdon.admin.vo.InformationVo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class InformationServiceV1Impl extends ServiceImpl<InformationMapper, Information> implements InformationServiceV1 {

    @Override
    public PageDTO<InformationVo> getPage(InformationVo information, Page<InformationVo> page) {
        return this.baseMapper.getPage(new PageDTO<>(page.getPageNo(), page.getPageSize()), information, page);
    }

    @Override
    public Long getCountByGroupId(Integer id, List<Integer> ids) {
        ArrayList<Integer> idList = new ArrayList<>() {{
            add(id);
        }};
        if (!ids.isEmpty()) {
            idList.addAll(ids);
        }
        return this.lambdaQuery()
                .in(Information::getInformationGroupId, idList)
                .count();
    }
}
