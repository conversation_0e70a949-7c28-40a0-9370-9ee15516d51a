package com.hiwie.breeze;

import com.google.common.collect.Lists;
import com.hiwie.breeze.util.ObjectUtil;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public abstract class AbstractOption<T> {

    public static <X> AbstractOption<X> apply(X x) {
        if (ObjectUtil.isNull(x)) {
            return None.apply();
        } else {
            return Some.apply(x);
        }
    }

    public static <X> AbstractOption<X> apply(boolean condition, X x) {
        if (condition) {
            return Some.apply(x);
        } else {
            return None.apply();
        }
    }

    public T getOrElse(T defaultValue) {
        if (this.isEmpty()) {
            return defaultValue;
        } else {
            return this.get();
        }
    }

    public T getOrElse(Supplier<T> supplier) {
        if (this.isEmpty()) {
            return supplier.get();
        } else {
            return this.get();
        }
    }

    public T orNull() {
        if (this.isEmpty()) {
            return null;
        } else {
            return this.get();
        }
    }

    public <X> AbstractOption<X> map(Function<T, X> function) {
        if (this.isEmpty()) {
            return None.apply();
        } else {
            return Some.apply(function.apply(this.get()));
        }
    }

    public AbstractOption<T> filter(Function<T, Boolean> function) {
        if (this.isEmpty() || function.apply(this.get())) {
            return this;
        } else {
            return None.apply();
        }
    }

    public AbstractOption<T> filterNot(Function<T, Boolean> function) {
        if (this.isEmpty() || !function.apply(this.get())) {
            return this;
        } else {
            return None.apply();
        }
    }

    public boolean nonEmpty() {
        return this.isDefined();
    }

    public <X> AbstractOption<X> flatMap(Function<T, AbstractOption<X>> function) {
        if (this.isEmpty()) {
            return None.apply();
        } else {
            return function.apply(this.get());
        }
    }

    public <X> X fold(Supplier<X> ifEmpty, Function<T, X> function) {
        if (this.isEmpty()) {
            return ifEmpty.get();
        } else {
            return function.apply(this.get());
        }
    }

    public <X> X fold(X ifEmpty, Function<T, X> function) {
        if (this.isEmpty()) {
            return ifEmpty;
        } else {
            return function.apply(this.get());
        }
    }

    public boolean contains(T ele) {
        return !this.isEmpty() && this.get().equals(ele);
    }

    public boolean exists(Function<T, Boolean> function) {
        return !this.isEmpty() && function.apply(this.get());
    }

    public boolean forall(Function<T, Boolean> function) {
        return this.isEmpty() || function.apply(this.get());
    }

    public void foreach(Consumer<T> consumer) {
        if (!this.isEmpty()) {
            consumer.accept(this.get());
        }
    }

    public <X> AbstractOption<X> collect(Function<T, X> function) {
        if (!this.isEmpty()) {
            return Some.apply(function.apply(this.get()));
        } else {
            return None.apply();
        }
    }

    public AbstractOption<T> orElse(Supplier<AbstractOption<T>> alternative) {
        if (this.isEmpty()) {
            return alternative.get();
        } else {
            return this;
        }
    }

    public List<T> toList() {
        if (this.isEmpty()) {
            return Lists.newArrayListWithCapacity(0);
        } else {
            return Lists.newArrayList(this.get());
        }
    }

    public <X> AbstractEither<X, T> toRight(Supplier<X> left) {
        if (this.isEmpty()) {
            return Left.apply(left.get());
        } else {
            return Right.apply(this.get());
        }
    }

    public <X> AbstractEither<T, X> toLeft(Supplier<X> right) {
        if (this.isEmpty()) {
            return Right.apply(right.get());
        } else {
            return Left.apply(this.get());
        }
    }

    public <X> AbstractEither<X, T> toRight(X left) {
        if (this.isEmpty()) {
            return Left.apply(left);
        } else {
            return Right.apply(this.get());
        }
    }

    public <X> AbstractEither<T, X> toLeft(X right) {
        if (this.isEmpty()) {
            return Right.apply(right);
        } else {
            return Left.apply(this.get());
        }
    }

    public abstract T get();

    public abstract boolean isEmpty();

    public boolean isDefined() {
        return !this.isEmpty();
    }

}
