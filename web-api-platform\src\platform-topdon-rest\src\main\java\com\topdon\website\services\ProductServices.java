package com.topdon.website.services;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.hiwie.breeze.*;
import com.hiwie.breeze.json.Json;
import com.topdon.website.SCConstants;
import com.topdon.website.form.ProductQueryForm;
import com.topdon.website.form.TopdonPageForm;
import com.topdon.website.form.TopdonProductQueryForm;
import com.topdon.website.helper.RequestUtil;
import com.topdon.website.helper.SignUtil;
import com.topdon.website.model.Product;
import com.topdon.website.model.TopdonResponse;
import com.topdon.website.repositories.ProductRepository;
import com.topdon.website.vo.ProductExtensionVo;
import com.topdon.website.vo.ProductVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Named
@Slf4j
public class ProductServices {

    private final ProductRepository productRepository;

    @Inject
    public ProductServices(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }

    public AbstractEither<ErrorMessage, List<Product>> list(ProductQueryForm queryForm) {
        return Right.apply(productRepository.list(queryForm));
    }

    public AbstractEither<ErrorMessage, TopdonResponse> listTopdon(TopdonProductQueryForm queryForm, TopdonPageForm pageForm) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("skuId", queryForm.getSkuId());
        params.put("softName", queryForm.getSoftName());
        params.put("bindCountries", queryForm.getBindCountries());
        params.put("bindTimes", queryForm.getBindTimes());
        params.put("current", pageForm.getCurrent());
        params.put("size", pageForm.getSize());
        return RequestUtil.postWithSign("/product/page", params);
    }

    public AbstractEither<ErrorMessage, TopdonResponse> getTopdon(String id, String snCode, String topdonToken) {
        String appKey = "6DCC55A836A246BEBAD583EBBA995ED1";
        String appSecret = "DAC450E89BEB43B5B02CD6BE82B8C76E";

        String nonceStr = SignUtil.randomString(10);
        long timestamp = System.currentTimeMillis();
        String signTemp = null;
        signTemp = "appkey=" + appKey + "&nonceStr=" + nonceStr + "&timestamp=" + timestamp;
        String hash = DigestUtils.md5Hex(signTemp + "&appSecret=" + appSecret).toUpperCase();
        String uri = "/api/v1/pay/outMallOrder/mallDetaiols?" + signTemp + "&sign=" + hash;

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", Optional.ofNullable(topdonToken).filter(o -> StrUtil.startWithIgnoreCase(o, "Bearer")).orElseGet(() -> "Bearer " + topdonToken));

        Map<String, Object> params = Maps.newHashMap();
        params.put("productId", id);
        params.put("sn", snCode);
        return RequestUtil.post(uri, Some.apply(params), Some.apply(headers)).fold(Left::apply, o -> {
            log.info("api/v1/pay/outMallOrder/mallDetaiols:{}", o);
            try {
                ObjectMapper jsonMapper = Json.MAPPER;
                jsonMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
                return Right.apply(jsonMapper.readValue(o, TopdonResponse.class));
            } catch (Exception e) {
                log.error("api/v1/pay/outMallOrder/mallDetaiols json conver error!", e);
                return Left.apply(SCConstants.API_ERROR);
            }
        });
    }


    public AbstractEither<ErrorMessage, List<Product>> relation(String productName) {
        return Right.apply(productRepository.relation(productName));
    }

    public AbstractEither<ErrorMessage, Product> get(String id) {
        return productRepository.option(id).toRight(Product.Errors.NOT_FOUNT);
    }

    public AbstractEither<ErrorMessage, List<ProductExtensionVo>> extension(String productName, Boolean draft) {
        return Right.apply(productRepository.extension(productName,draft));
    }

    public AbstractEither<ErrorMessage, List<Map<String, Object>>> overview(String productName) {
        List<Map<String, Object>> overview = productRepository.overview(productName);
        List<Map<String, Object>> products = productRepository.compare(productName);
        Map<String, Object> compare = Maps.newHashMap();
        compare.put("type", "compare");
        compare.put("title", "COMPARISON");
        compare.put("product", products);
        overview.add(compare);
        return Right.apply(overview);
    }

    public AbstractEither<ErrorMessage, List<Product>> recommend() {
        return Right.apply(productRepository.recommend());
    }

    public AbstractEither<ErrorMessage, ProductVo> getByName(String productName, String draft) {
        List<ProductVo> byName = productRepository.getByName(productName,draft);
        if(byName.isEmpty()){
            return Right.apply(null);
        }
        return Right.apply(byName.get(0));
    }
}
