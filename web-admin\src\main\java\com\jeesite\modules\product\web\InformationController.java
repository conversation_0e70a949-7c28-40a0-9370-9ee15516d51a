package com.jeesite.modules.product.web;

import cn.hutool.core.util.StrUtil;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.Information;
import com.jeesite.modules.product.service.InformationService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 产品资料管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Controller
@RequestMapping(value = "${adminPath}/product/information")
public class InformationController extends BaseController {

    @Autowired
    private InformationService informationService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public Information get(String id, boolean isNewRecord) {
        return informationService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("product:information:view")
    @RequestMapping(value = {"list", ""})
    public String list(Information information, Model model) {
        model.addAttribute("information", information);
        return "modules/product/informationList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("product:information:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<Information> listData(Information information, HttpServletRequest request, HttpServletResponse response) {
        information.setPage(new Page<>(request, response));
        if (StrUtil.isBlank(information.getOrderBy())) {
            information.setOrderBy("group1 asc,group2 asc,i.sort asc");
        }
        Page<Information> page = informationService.findPage(information);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("product:information:view")
    @RequestMapping(value = "form")
    public String form(Information information, Model model) {
        model.addAttribute("information", information);
        return "modules/product/informationForm";
    }

    /**
     * 复制表单
     */
    @RequiresPermissions("product:information:view")
    @RequestMapping(value = "copy")
    public String copy(Information information, Model model) {
        information.setId(null);
        if (information.getSort() != null) {
            information.setSort(information.getSort() + 1);
            information.setMediaUrl("");
            information.setDownloadUrl("");
        }
        model.addAttribute("information", information);
        return "modules/product/informationForm";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("product:information:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated Information information, HttpServletRequest request) {
        informationService.save(information);
        return renderResult(Global.TRUE, text("保存产品资料成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("product:information:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(Information information) {
        informationService.delete(information);
        return renderResult(Global.TRUE, text("删除产品资料成功！"));
    }

}