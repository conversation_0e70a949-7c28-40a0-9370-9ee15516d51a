package com.topdon.website.model.graphql.model;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;
import com.topdon.website.model.ShopifyCartLine;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class Checkout {

    public static String NODE_NAME = "checkout";
    public static final String ENTITY_NAME = "CHECKOUT";

    private String id;
    private String webUrl;
    private Money paymentDue;
    private Money subtotalPrice;
    private Money totalDuties;
    private Money totalPrice;
    private Money totalTax;
    private Boolean requiresShipping;
    private String email;
    private Money lineItemsSubtotalPrice;
    private List<Map<String, Money>> shippingDiscountAllocations;
    private List<ShopifyCartLine> lineItems;

    public static class Errors {
        public static final ErrorMessage DISCOUNT_UNAVAILABLE = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "DISCOUNT_UNAVAILABLE");
        public static final ErrorMessage AT_LEAST_ONE = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "AT_LEAST_ONE");
        public static final ErrorMessage VARIANT_DISABLED = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "VARIANT_DISABLED");
    }
}
