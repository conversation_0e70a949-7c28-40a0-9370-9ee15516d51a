package com.topdon.website.service.impl;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.entity.Product;
import com.topdon.website.service.ProductService;
import com.topdon.website.vo.ProductPropertyVo;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.mapper.ProductPropertyMapper;
import com.topdon.website.entity.ProductProperty;
import com.topdon.website.service.ProductPropertyService;

import javax.annotation.Resource;

@Service
public class ProductPropertyServiceImpl extends ServiceImpl<ProductPropertyMapper, ProductProperty> implements ProductPropertyService {

    @Resource
    private ProductService productService;

    @Override
    public AbstractEither<ErrorMessage, List<Product>> getCompareProductList(String classificationCode, boolean draft) {
        return Right.apply(productService.getCompareProductList(classificationCode, draft));
    }

    @Override
    public AbstractEither<ErrorMessage, Map<Integer,String>> getList(String classificationCode, String productId, boolean draft) {
        Map<Integer,String> map = new HashMap<>();
        for (ProductPropertyVo productPropertyVo : baseMapper.getList(classificationCode, productId, draft)) {
            map.put(productPropertyVo.getCategoryId(),productPropertyVo.getVal());
        }
        return Right.apply(map);
    }

}
