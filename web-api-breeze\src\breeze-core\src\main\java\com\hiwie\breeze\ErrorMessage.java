package com.hiwie.breeze;

import com.google.common.collect.Maps;
import com.hiwie.breeze.util.AssertUtil;

import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 */
public class ErrorMessage {

    private final String code;
    private AbstractOption<Map<String, Object>> parameters;

    public ErrorMessage(String module, String entity, String info, AbstractOption<Map<String, Object>> parameters) {
        String patternString = "^[A-Z_A-Z]{1,64}$";
        AssertUtil.isTrue(module.matches(patternString) && entity.matches(patternString) && info.matches(patternString), "parameters dose NOT match the pattern " + patternString);
        this.parameters = parameters;
        StringJoiner joiner = new StringJoiner(".");
        joiner.add("ERROR");
        joiner.add(module);
        joiner.add(entity);
        joiner.add(info);
        this.code = joiner.toString();
    }

    private ErrorMessage(String code, AbstractOption<Map<String, Object>> parameters) {
        this.code = code;
        this.parameters = parameters;
    }

    public ErrorMessage(String module, String entity, String info) {
        this(module, entity, info, None.apply());
    }

    @Override
    public int hashCode() {
        return 31 * 17 + code.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof ErrorMessage) {
            return ((ErrorMessage) o).getCode().equals(code);
        }
        return false;
    }

    public ErrorMessage applyParameters(Map<String, Object> parameters) {
        return new ErrorMessage(code, Some.apply(parameters));
    }

    public ErrorMessage applyParameter(String key, Object value) {
        Map<String, Object> parameters = Maps.newHashMapWithExpectedSize(1);
        parameters.put(key, value);
        return new ErrorMessage(code, Some.apply(parameters));
    }

    public String getCode() {
        return this.code;
    }

    public AbstractOption<Map<String, Object>> getParametersOption() {
        return this.parameters;
    }

    @Override
    public String toString() {
        return "ErrorMessage(" + getCode() + ")";
    }

}
