<% layout('/layouts/default.html', {title: '产品对比管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('对比分类管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('classification:classification:edit')){ %>
					<button class="btn btn-default btnTool" id="openClassificationModal" data-click-binded="false" data-toggle="modal" data-target="#addClassificationModal"><i class="fa fa-plus"></i> ${text('新增')}</button>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" action="${ctx}/classification/productCompare/getClassificationList" method="post" class="form-inline hide">
			</#form:form>
			<table id="dataGrid"></table>
	</div>

	<div class="modal fade" id="addClassificationModal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">添加比较分类</h4>
				</div>
				<div class="modal-body row">
					<div class="col-xs-8">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('分类')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="classification" title="${text('分类')}"
								path="parent.id" labelPath="parent.name"
								url="${ctx}/classification/classification/treeData"
								class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
								<div class="text-danger hidden" id="selectClassification">请选择分类</div>
								<div class="text-danger hidden" id="saveClassificationError"></div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer mt3">
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
					<button type="button" class="btn btn-primary" onclick="addClassificationCompare()">添加比较分类</button>
				</div>
			</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div>
</div>
<script>

</script>
</div>
<% } %>
<script>

	$('#openClassificationModal').click(function () {
		$('#saveClassificationError').addClass('hidden')
		$('#classificationCode').val('')
		$('#classificationName').val('')
	})

	function addClassificationCompare() {
		var classificationCode = $('#classificationCode').val()
		if(!classificationCode){
			$('#selectClassification').removeClass('hidden')
			return
		}else {
			$('#selectClassification').addClass('hidden')
		}

		$.post(ctx + '/classification/productCompare/save' , {classificationCode: classificationCode} , function (res) {
			if(res){
				$('#saveClassificationError').removeClass('hidden')
				$('#saveClassificationError').text(res)
			}else{
				$('#addClassificationModal').modal('hide')
				js.showMessage('保存成功')
				$('#dataGrid').dataGrid('refreshTreeChildren','', '')
			}
		})

	}

	function deleteClassificationCompare(id) {
		$.post(ctx + '/classification/productCompare/delete?compareId=' + id, function (res) {
			if(res){
				js.showMessage(res)
			}else{
				js.showMessage('删除成功')
				$('#dataGrid').dataGrid('refreshTreeChildren','', '')
			}
		})
	}

// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	columnModel: [
		{header:'${text("名称")}', name:'name', index:'a.name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			if (row.classificationCompareId){
				return '( '+row.code+' ) '+'<a href="${ctx}/classification/productCompare/form?compareId='+row.classificationCompareId+'" class="btnList" ">'+(val||row.code)+'</a>';
			}else {
				return '( '+row.code+' ) '+(val||row.code)+'</a>';
			}
		}},
		{header:'${text("创建时间")}', name:'classificationCompareCreateDate', index:'a.classificationCompareCreateDate', width:150, align:"center"},
		{header:'${text("发布时间")}', name:'classificationCompareReleaseDate', index:'a.classificationCompareReleaseDate', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'classificationCompareUpdateDate', index:'a.classificationCompareUpdateDate', width:150, align:"center"},
		{header:'${text("状态")}', name:'classificationCompareDraft', index:'a.classificationCompareDraft', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			if (row.classificationCompareDraft == null){
				return '';
			} else if (row.classificationCompareDraft == 1){
				return '草稿'
			}else {
				return '已发布';
			}
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('classification:classification:edit')){ %>
				if(row.classificationCompareId){
					actions.push('<a href="${ctx}/classification/productCompare/form?compareId='+row.classificationCompareId+'" class="btnList" title="${text("编辑比较产品")}"><i class="fa fa-pencil"></i></a>&nbsp;');
					actions.push('<a href="${ctx}/classification/productCompare/delete?compareId='+row.classificationCompareId+'" class="btnList" title="${text("删除比较分类")}" data-confirm="${text("确认要删除该比较分类吗？")}" ><i class="fa fa-trash-o"></i></a>&nbsp;');
				}
			<% } %>
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'name,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
</script>