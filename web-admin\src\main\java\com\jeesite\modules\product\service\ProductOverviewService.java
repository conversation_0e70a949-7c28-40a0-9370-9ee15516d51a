package com.jeesite.modules.product.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductOverview;
import com.jeesite.modules.product.dao.ProductOverviewDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 产品详情管理Service
 * <AUTHOR>
 * @version 2022-04-21
 */
@Service
@Transactional(readOnly=true)
public class ProductOverviewService extends CrudService<ProductOverviewDao, ProductOverview> {
	
	/**
	 * 获取单条数据
	 * @param productOverview
	 * @return
	 */
	@Override
	public ProductOverview get(ProductOverview productOverview) {
		return super.get(productOverview);
	}
	
	/**
	 * 查询分页数据
	 * @param productOverview 查询条件
	 * @param productOverview.page 分页对象
	 * @return
	 */
	@Override
	public Page<ProductOverview> findPage(ProductOverview productOverview) {
		return super.findPage(productOverview);
	}
	
	/**
	 * 查询列表数据
	 * @param productOverview
	 * @return
	 */
	@Override
	public List<ProductOverview> findList(ProductOverview productOverview) {
		return super.findList(productOverview);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param productOverview
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ProductOverview productOverview) {
		super.save(productOverview);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(productOverview, productOverview.getId(), "productOverview_image");
	}
	
	/**
	 * 更新状态
	 * @param productOverview
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ProductOverview productOverview) {
		super.updateStatus(productOverview);
	}
	
	/**
	 * 删除数据
	 * @param productOverview
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ProductOverview productOverview) {
		super.delete(productOverview);
	}
	
}