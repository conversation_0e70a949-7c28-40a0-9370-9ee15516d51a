package com.topdon.website.model;

/**
 * 分类拓展链接模型
 */
public class ClassificationExtensionLink {
    private String id;
    private String classificationCode; // 分类编码
    private String iconDefault; // 图标(默认)
    private String iconHover; // 图标(鼠标移入)
    private String navText; // 导航文案
    private String categoryText; // 二级品类文案
    private Integer sortOrder; // 排序
    private String isDisplay; // 是否展示
    private String jumpLink; // 跳转链接

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClassificationCode() {
        return classificationCode;
    }

    public void setClassificationCode(String classificationCode) {
        this.classificationCode = classificationCode;
    }

    public String getIconDefault() {
        return iconDefault;
    }

    public void setIconDefault(String iconDefault) {
        this.iconDefault = iconDefault;
    }

    public String getIconHover() {
        return iconHover;
    }

    public void setIconHover(String iconHover) {
        this.iconHover = iconHover;
    }

    public String getNavText() {
        return navText;
    }

    public void setNavText(String navText) {
        this.navText = navText;
    }

    public String getCategoryText() {
        return categoryText;
    }

    public void setCategoryText(String categoryText) {
        this.categoryText = categoryText;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIsDisplay() {
        return isDisplay;
    }

    public void setIsDisplay(String isDisplay) {
        this.isDisplay = isDisplay;
    }

    public String getJumpLink() {
        return jumpLink;
    }

    public void setJumpLink(String jumpLink) {
        this.jumpLink = jumpLink;
    }
}
