package com.hiwie.breeze.cache.tests;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Some;
import com.hiwie.breeze.cache.Cache;
import com.hiwie.breeze.cache.Counter;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import redis.clients.jedis.JedisPool;

import java.time.Duration;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
@DisplayName("Counter test")
@RunWith(JUnitPlatform.class)
class CounterTest {

    private static Cache cache;

    private static Counter counter;

    private static int limit = 3;

    @BeforeAll
    static void init() {
        JedisPool pool = new JedisPool(new GenericObjectPoolConfig(), "dev.11ejia.com", 6379, 2000, "ahuwqoiye12c7b9484c2b94", 0, "breeze_test");
        cache = new Cache(pool);
        counter = new Counter("test.counter", 0, Some.apply(Duration.parse("PT5S")), true, limit);
    }

    @Test
    @DisplayName("increase")
    void increase() {
        long count = cache.apply(counter.increase(UUID.randomUUID().toString()));
        assertEquals(1, count);
    }

    @Test
    @DisplayName("decrease")
    void decrease() {
        long count = cache.apply(counter.decrease(UUID.randomUUID().toString()));
        assertEquals(-1, count);
    }

    @Test
    @DisplayName("limit")
    void limit() {
        String key = UUID.randomUUID().toString();
        for (int i = 0; i < limit; i++) {
            cache.apply(counter.increase(key));
        }
        long count = cache.apply(counter.get(key));
        assertEquals(limit, count);
        AbstractOption<ErrorMessage> option = cache.apply(counter.isLimitReached(key));
        assertTrue(option.isEmpty());
    }

}