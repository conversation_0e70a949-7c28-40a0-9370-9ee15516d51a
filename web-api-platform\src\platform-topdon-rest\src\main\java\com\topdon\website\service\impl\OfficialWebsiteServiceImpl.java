package com.topdon.website.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.mapper.OfficialWebsiteMapper;
import com.topdon.website.entity.OfficialWebsite;
import com.topdon.website.service.OfficialWebsiteService;

@Service
public class OfficialWebsiteServiceImpl extends ServiceImpl<OfficialWebsiteMapper, OfficialWebsite> implements OfficialWebsiteService {

    @Override
    public AbstractEither<ErrorMessage, List<Tree<String>>> getList() {
        List<TreeNode<String>> treeNodeList = list().stream()
                .map(item ->
                        {
                            TreeNode<String> node = new TreeNode<>(item.getCode(), item.getParentCode(), item.getName(), item.getTreeSort());
                            node.setExtra(Dict.create().set("url", item.getUrl()));
                            return node;
                        }
                )
                .collect(Collectors.toList());

        return Right.apply(TreeUtil.build(treeNodeList, "0"));
    }
}
