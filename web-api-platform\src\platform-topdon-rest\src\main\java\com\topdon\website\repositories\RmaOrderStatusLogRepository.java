package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.mappers.RmaOrderStatusLogMapper;
import com.topdon.website.model.RmaOrderStatusLog;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 16:44
 */
@Named
public class RmaOrderStatusLogRepository extends MysqlJDBCSupport {

    @Inject
    protected RmaOrderStatusLogRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public List<RmaOrderStatusLog> getByRmaOrderId(Integer rmaOrderId) {
        @Language("SQL") String sql = "select id,rma_order_id,ticket_number,status,status_modified_time  from rma_order_status_log where rma_order_id=:rmaOrderId";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("rmaOrderId", rmaOrderId);
        return list(sql, RmaOrderStatusLogMapper.DETAIL, params);
    }

    public long add(int rmOrderId, String ticketNumber, String status, Date modifyTime) {
        @Language("SQL") String sql = "insert into rma_order_status_log(rma_order_id,ticket_number,status,status_modified_time,create_time) " +
                "VALUES (:rmOrderId,:ticketNumber,:status,:modifyTime,now())";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("rmOrderId", rmOrderId);
        params.addValue("ticketNumber", ticketNumber);
        params.addValue("status", status);
        params.addValue("modifyTime", modifyTime);
        return autoIncreaseInsert(sql, params).longValue();
    }
}
