<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.ProductPropertyMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.ProductProperty">
    <!--@mbg.generated-->
    <!--@Table product_property-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="classification_compare_id" jdbcType="INTEGER" property="classificationCompareId" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="val" jdbcType="VARCHAR" property="val" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="draft" jdbcType="BOOLEAN" property="draft" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="TIMESTAMP" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, classification_compare_id, category_id, val, sort, draft, create_time, 
    create_by, update_time, update_by
  </sql>
</mapper>