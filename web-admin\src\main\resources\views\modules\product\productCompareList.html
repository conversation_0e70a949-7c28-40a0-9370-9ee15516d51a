<% layout('/layouts/default.html', {title: '产品对比管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('产品对比管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('product:productCompare:edit')){ %>
					<a href="${ctx}/product/productCompare/form" class="btn btn-default btnTool" title="${text('新增产品对比')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${productCompare}" action="${ctx}/product/productCompare/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('产品')}：</label>
					<div class="control-inline">
						<#form:input path="product.name" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="56" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("产品")}', name:'product.name', index:'a.product_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/product/productCompare/form?id='+row.id+'" class="btnList" data-title="${text("编辑产品详情")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("名称")}', name:'name', index:'a.name', width:150, align:"left"},
		{header:'${text("图片")}', name:'image', index:'a.image', width:150, align:"left"},
		{header:'${text("类型")}', name:'type', index:'a.type', width:150, align:"left"},
		{header:'${text("其他参数")}', name:'param', index:'a.param', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('product:productCompare:edit')){ %>
				actions.push('<a href="${ctx}/product/productCompare/form?id='+row.id+'" class="btnList" title="${text("编辑产品对比")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/product/productCompare/delete?id='+row.id+'" class="btnList" title="${text("删除产品对比")}" data-confirm="${text("确认要删除该产品对比吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>