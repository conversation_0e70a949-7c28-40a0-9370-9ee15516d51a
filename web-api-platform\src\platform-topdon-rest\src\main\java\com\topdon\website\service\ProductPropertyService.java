package com.topdon.website.service;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.Product;
import com.topdon.website.entity.ProductProperty;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

public interface ProductPropertyService extends IService<ProductProperty>{

    AbstractEither<ErrorMessage, List<Product>> getCompareProductList(String classificationCode, boolean draft);

    AbstractEither<ErrorMessage, Map<Integer,String>> getList(String classificationCode, String productId, boolean draft);
}
