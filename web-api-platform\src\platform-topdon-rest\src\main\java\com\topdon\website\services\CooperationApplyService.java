package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.form.CooperationApplyCreateForm;
import com.topdon.website.form.SubscribeForm;
import com.topdon.website.model.Subscribe;
import com.topdon.website.repositories.CooperationApplyRepository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class CooperationApplyService {
    private final CooperationApplyRepository cooperationApplyRepository;
    private final TransactionTemplate transactionTemplate;
    private final EmailSubscribeService subscribeService;


    @Inject
    public CooperationApplyService(CooperationApplyRepository cooperationApplyRepository, TransactionTemplate transactionTemplate, EmailSubscribeService subscribeService) {
        this.cooperationApplyRepository = cooperationApplyRepository;
        this.transactionTemplate = transactionTemplate;
        this.subscribeService = subscribeService;
    }

    public AbstractEither<ErrorMessage, Long> create(CooperationApplyCreateForm createForm) {
        return transactionTemplate.execute(status -> {
            if (createForm.isSubscribe()) {
                subscribeService.create(SubscribeForm.apply(createForm.getEmail(), createForm.getSiteFrom(), Subscribe.SubscribeFrom.COOP));
            }
            return Right.apply(cooperationApplyRepository.create(createForm));
        });

    }
}
