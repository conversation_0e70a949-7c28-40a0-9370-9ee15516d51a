package com.topdon.website.mappers;

import com.topdon.website.model.Banner;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class BannerMapper {

    public static final RowMapper<Banner> DETAIL = (rs, index) -> {
        Banner detail = new Banner();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setImage(rs.getString("image"));
        detail.setLink(rs.getString("link"));
        detail.setMobileLink(rs.getString("mobile_link"));
        detail.setTitle(rs.getString("title"));
        detail.setMobileImage(rs.getString("mobile_image"));
        detail.setDesc(rs.getString("desc"));
        detail.setButtonTitle(rs.getString("button_title"));
        return detail;
    };

}
