package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 官网站点
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "official_website")
public class OfficialWebsite {
    @TableId(value = "code", type = IdType.AUTO)
    private String code;

    @TableField(value = "`name`")
    private String name;

    /**
     * 跳转链接
     */
    @TableField(value = "url")
    private String url;

    @TableField(value = "parent_code")
    private String parentCode;

    @TableField(value = "parent_codes")
    private String parentCodes;

    @TableField(value = "tree_sort")
    private Long treeSort;

    @TableField(value = "tree_sorts")
    private String treeSorts;

    @TableField(value = "tree_leaf")
    private String treeLeaf;

    @TableField(value = "tree_level")
    private Short treeLevel;

    @TableField(value = "tree_names")
    private String treeNames;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "update_date")
    private Date updateDate;
}