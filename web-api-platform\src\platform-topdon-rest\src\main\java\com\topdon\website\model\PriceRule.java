package com.topdon.website.model;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.ZonedDateTime;

public class PriceRule {
    private long id;
    private String title;
    private String value_type;
    private double value;
    private String customer_selection;
    private String target_type;
    private String target_selection;
    private String allocation_method;
    private String allocation_limit;
    private String once_per_customer;
    private String usage_limit;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private ZonedDateTime starts_at;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private ZonedDateTime ends_at;
    private String[] entitled_product_ids;
    private String[] entitled_variant_ids;
    private String[] entitled_country_ids;
    private String[] prerequisite_product_ids;
    private String[] prerequisite_variant_ids;
    private String[] prerequisite_collection_ids;
    private String[] prerequisite_saved_search_ids;
    private String[] prerequisite_customer_ids;
    private String prerequisite_subtotal_range;
    private String prerequisite_quantity_range;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getValue_type() {
        return value_type;
    }

    public void setValue_type(String value_type) {
        this.value_type = value_type;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    public String getCustomer_selection() {
        return customer_selection;
    }

    public void setCustomer_selection(String customer_selection) {
        this.customer_selection = customer_selection;
    }

    public String getTarget_type() {
        return target_type;
    }

    public void setTarget_type(String target_type) {
        this.target_type = target_type;
    }

    public String getTarget_selection() {
        return target_selection;
    }

    public void setTarget_selection(String target_selection) {
        this.target_selection = target_selection;
    }

    public String getAllocation_method() {
        return allocation_method;
    }

    public void setAllocation_method(String allocation_method) {
        this.allocation_method = allocation_method;
    }

    public String getAllocation_limit() {
        return allocation_limit;
    }

    public void setAllocation_limit(String allocation_limit) {
        this.allocation_limit = allocation_limit;
    }

    public String getOnce_per_customer() {
        return once_per_customer;
    }

    public void setOnce_per_customer(String once_per_customer) {
        this.once_per_customer = once_per_customer;
    }

    public String getUsage_limit() {
        return usage_limit;
    }

    public void setUsage_limit(String usage_limit) {
        this.usage_limit = usage_limit;
    }

    public ZonedDateTime getStarts_at() {
        return starts_at;
    }

    public void setStarts_at(ZonedDateTime starts_at) {
        this.starts_at = starts_at;
    }

    public ZonedDateTime getEnds_at() {
        return ends_at;
    }

    public void setEnds_at(ZonedDateTime ends_at) {
        this.ends_at = ends_at;
    }

    public String[] getEntitled_product_ids() {
        return entitled_product_ids;
    }

    public void setEntitled_product_ids(String[] entitled_product_ids) {
        this.entitled_product_ids = entitled_product_ids;
    }

    public String[] getEntitled_variant_ids() {
        return entitled_variant_ids;
    }

    public void setEntitled_variant_ids(String[] entitled_variant_ids) {
        this.entitled_variant_ids = entitled_variant_ids;
    }

    public String[] getEntitled_country_ids() {
        return entitled_country_ids;
    }

    public void setEntitled_country_ids(String[] entitled_country_ids) {
        this.entitled_country_ids = entitled_country_ids;
    }

    public String[] getPrerequisite_product_ids() {
        return prerequisite_product_ids;
    }

    public void setPrerequisite_product_ids(String[] prerequisite_product_ids) {
        this.prerequisite_product_ids = prerequisite_product_ids;
    }

    public String[] getPrerequisite_variant_ids() {
        return prerequisite_variant_ids;
    }

    public void setPrerequisite_variant_ids(String[] prerequisite_variant_ids) {
        this.prerequisite_variant_ids = prerequisite_variant_ids;
    }

    public String[] getPrerequisite_collection_ids() {
        return prerequisite_collection_ids;
    }

    public void setPrerequisite_collection_ids(String[] prerequisite_collection_ids) {
        this.prerequisite_collection_ids = prerequisite_collection_ids;
    }

    public String[] getPrerequisite_saved_search_ids() {
        return prerequisite_saved_search_ids;
    }

    public void setPrerequisite_saved_search_ids(String[] prerequisite_saved_search_ids) {
        this.prerequisite_saved_search_ids = prerequisite_saved_search_ids;
    }

    public String[] getPrerequisite_customer_ids() {
        return prerequisite_customer_ids;
    }

    public void setPrerequisite_customer_ids(String[] prerequisite_customer_ids) {
        this.prerequisite_customer_ids = prerequisite_customer_ids;
    }

    public String getPrerequisite_subtotal_range() {
        return prerequisite_subtotal_range;
    }

    public void setPrerequisite_subtotal_range(String prerequisite_subtotal_range) {
        this.prerequisite_subtotal_range = prerequisite_subtotal_range;
    }

    public String getPrerequisite_quantity_range() {
        return prerequisite_quantity_range;
    }

    public void setPrerequisite_quantity_range(String prerequisite_quantity_range) {
        this.prerequisite_quantity_range = prerequisite_quantity_range;
    }
}
