package com.topdon.website.model;

import java.util.List;

public class FooterMenu {
    private String code;
    private String name;
    private String link;
    private String mobileLink;
    private String names;
    private List<FooterMenu> children;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getNames() {
        return names;
    }

    public void setNames(String names) {
        this.names = names;
    }

    public List<FooterMenu> getChildren() {
        return children;
    }

    public void setChildren(List<FooterMenu> children) {
        this.children = children;
    }

    public String getMobileLink() {
        return mobileLink;
    }

    public void setMobileLink(String mobileLink) {
        this.mobileLink = mobileLink;
    }
}
