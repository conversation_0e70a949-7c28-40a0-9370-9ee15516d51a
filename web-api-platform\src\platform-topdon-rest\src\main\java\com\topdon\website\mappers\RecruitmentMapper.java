package com.topdon.website.mappers;

import com.topdon.website.model.Recruitment;
import com.topdon.website.model.Region;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class RecruitmentMapper {

    public static final RowMapper<Recruitment> DETAIL = (rs, index) -> {
        Recruitment detail = new Recruitment();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setPeople(rs.getInt("people"));
        detail.setPosition(rs.getString("position"));
        detail.setCreateAt(rs.getTimestamp("create_at").toLocalDateTime());
        detail.setContent(rs.getString("content"));
        detail.setCategory(Recruitment.Category.valueOf(rs.getString("category")));
        detail.setType(Recruitment.Type.valueOf(rs.getString("type")));
        return detail;
    };

}
