package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 区域
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "region")
public class Region {
    @TableId(value = "code", type = IdType.AUTO)
    private String code;

    /**
     * 名字
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 国旗
     */
    @TableField(value = "flag")
    private String flag;

    /**
     * 父级编号

     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 所有父级编号
     */
    @TableField(value = "parent_codes")
    private String parentCodes;

    /**
     * 本级排序号（升序）
     */
    @TableField(value = "tree_sort")
    private Long treeSort;

    /**
     * 所有级别排序号
     */
    @TableField(value = "tree_sorts")
    private String treeSorts;

    /**
     * 是否最末级
     */
    @TableField(value = "tree_leaf")
    private String treeLeaf;

    /**
     * 层次级别
     */
    @TableField(value = "tree_level")
    private Short treeLevel;

    /**
     * 全节点名
     */
    @TableField(value = "tree_names")
    private String treeNames;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;
}