package com.topdon.admin.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.DefaultNodeParser;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.admin.entity.Region;
import com.topdon.admin.mapper.RegionMapper;
import com.topdon.admin.service.RegionServiceV1;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
@Service
public class RegionServiceV1Impl extends ServiceImpl<RegionMapper, Region> implements RegionServiceV1 {

    @Override
    public List<Tree<String>> getTree() {

        List<TreeNode<String>> treeNodeList = list()
                .stream()
                .map(item ->
                        new TreeNode<>(item.getCode(), item.getParentCode(), item.getName(), item.getTreeSort())
                                .setExtra(
                                        Dict.create()
                                )
                )
                .collect(Collectors.toList());

        List<Tree<String>> build = TreeUtil.build(treeNodeList,"0",new DefaultNodeParser<>());
        if (build == null) {
            return new ArrayList<>();
        }
        return build;
    }
}
