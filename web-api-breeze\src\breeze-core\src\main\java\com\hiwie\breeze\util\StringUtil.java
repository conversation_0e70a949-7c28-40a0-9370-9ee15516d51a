package com.hiwie.breeze.util;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;

import java.security.SecureRandom;

/**
 * <AUTHOR>
 */
public class StringUtil extends StringUtils {

    public static final SecureRandom RANDOM = new SecureRandom();

    private static final char[] NUMBERS_FOR_ENCODER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    private StringUtil() {
    }

    private static char[] encodeDecimal(final byte[] data) {
        final int l = data.length;
        final char[] out = new char[l];
        for (int i = 0; i < l; i++) {
            out[i] = NUMBERS_FOR_ENCODER[Byte.toUnsignedInt(data[i]) % NUMBERS_FOR_ENCODER.length];
        }
        return out;
    }

    public static String secureRandom(final int length, Encoder encoder) {
        if (encoder == Encoder.HEX) {
            AssertUtil.isTrue(length % 2 == 0, length + " is not an even number");
            byte[] values = new byte[length / 2];
            RANDOM.nextBytes(values);
            return Hex.encodeHexString(values);
        } else if (encoder == Encoder.NUMBER) {
            byte[] values = new byte[length];
            RANDOM.nextBytes(values);
            return new String(encodeDecimal(values));
        } else {
            throw new IllegalArgumentException("unknown encoder " + encoder);
        }
    }

    public static boolean safeEqual(String a, String b) {
        byte[] byteA = a.getBytes();
        byte[] byteB = b.getBytes();
        if (byteA.length != byteB.length) {
            return false;
        }
        int result = 0;
        for (int i = 0; i < byteA.length; i++) {
            result |= byteA[i] ^ byteB[i];
        }
        return result == 0;
    }

    public enum Encoder {
        /**
         * 编码格式
         */
        HEX, NUMBER
    }

}