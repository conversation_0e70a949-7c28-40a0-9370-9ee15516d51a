<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.RegionMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.Region">
    <!--@mbg.generated-->
    <!--@Table region-->
    <id column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="parent_codes" jdbcType="VARCHAR" property="parentCodes" />
    <result column="tree_sort" jdbcType="DECIMAL" property="treeSort" />
    <result column="tree_sorts" jdbcType="VARCHAR" property="treeSorts" />
    <result column="tree_leaf" jdbcType="CHAR" property="treeLeaf" />
    <result column="tree_level" jdbcType="DECIMAL" property="treeLevel" />
    <result column="tree_names" jdbcType="VARCHAR" property="treeNames" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    code, `name`, flag, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, 
    tree_level, tree_names, create_by, create_date, update_by, update_date, remarks
  </sql>
</mapper>