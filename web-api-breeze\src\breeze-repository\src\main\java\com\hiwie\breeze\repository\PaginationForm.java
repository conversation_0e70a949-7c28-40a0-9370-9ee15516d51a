package com.hiwie.breeze.repository;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
public class PaginationForm {

    @Min(0)
    private long drop;

    @Max(100)
    private int take;

    public PaginationForm(long drop, int take) {
        this.drop = drop;
        this.take = take;
    }

    public PaginationForm next() {
        drop += take;
        return this;
    }

    public long getDrop() {
        return drop;
    }

    public void setDrop(long drop) {
        this.drop = drop;
    }

    public int getTake() {
        return take;
    }

    public void setTake(int take) {
        this.take = take;
    }

}
