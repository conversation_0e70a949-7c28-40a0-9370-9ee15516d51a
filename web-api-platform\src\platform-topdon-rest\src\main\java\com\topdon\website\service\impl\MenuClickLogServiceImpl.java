package com.topdon.website.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.entity.MenuClickLog;
import com.topdon.website.mapper.MenuClickLogMapper;
import com.topdon.website.service.MenuClickLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
@Service
public class MenuClickLogServiceImpl extends ServiceImpl<MenuClickLogMapper, MenuClickLog> implements MenuClickLogService{

    @Autowired
    private IpLookupService ipLookupService;

    @Async
    @Override
    public void logClick(String ip, String menuName, String productName, String userAgent) {
        MenuClickLog log = new MenuClickLog();
        log.setIpAddress(ip);
        log.setMenuName(menuName);
        log.setProductName(productName);
        log.setClickTime(new Date());
        log.setUserAgent(userAgent);
        log.setIpLocation(ipLookupService.getLocation(ip));
        save(log);
    }
}
