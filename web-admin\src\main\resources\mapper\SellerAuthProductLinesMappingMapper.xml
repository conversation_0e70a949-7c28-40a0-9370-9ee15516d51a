<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.SellerAuthProductLinesMappingMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.SellerAuthProductLinesMapping">
    <!--@mbg.generated-->
    <!--@Table seller_auth_product_lines_mapping-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_auth_id" jdbcType="INTEGER" property="sellerAuthId" />
    <result column="product_lines_id" jdbcType="INTEGER" property="productLinesId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, seller_auth_id, product_lines_id
  </sql>
</mapper>