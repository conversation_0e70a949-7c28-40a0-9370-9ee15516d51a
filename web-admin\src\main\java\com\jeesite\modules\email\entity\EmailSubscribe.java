package com.jeesite.modules.email.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 订阅管理Entity
 *
 * <AUTHOR>
 * @version 2023-02-14
 */
@Table(name = "email_subscribe", alias = "a", label = "订阅信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "email", attrName = "email", label = "邮箱", queryType = QueryType.LIKE),
        @Column(name = "from", attrName = "from", label = "功能"),
        @Column(name = "create_at", attrName = "createAt", label = "订阅时间", isUpdateForce = true),
        @Column(name = "site", attrName = "site", label = "订阅来源"),
        @Column(name = "copied_discount_code", attrName = "copiedDiscountCode", label = "是否复制折扣码"),
}, orderBy = "a.id DESC"
)
public class EmailSubscribe extends DataEntity<EmailSubscribe> {

    private static final long serialVersionUID = 1L;
    private String email;        // 邮箱
    private String from;        // 功能
    private Date createAt;        // 订阅时间
    private String site;        // 订阅来源
    private Integer copiedDiscountCode; // 是否复制折扣码 0否 1是

    @ExcelFields({
            @ExcelField(title = "电子邮箱", attrName = "email", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "订阅来源", attrName = "site", align = ExcelField.Align.CENTER, dictType = "site_from", sort = 20),
            @ExcelField(title = "功能来源", attrName = "from", align = ExcelField.Align.CENTER, dictType = "from", sort = 30),
            @ExcelField(title = "订阅时间", attrName = "createAt", align = ExcelField.Align.CENTER, sort = 40, dataFormat = "yyyy-MM-dd HH:mm:ss"),
            @ExcelField(title = "是否复制折扣码", attrName = "copiedDiscountCodeStr", align = ExcelField.Align.CENTER, sort = 50),
    })
    public EmailSubscribe() {
        this(null);
    }

    public EmailSubscribe(String id) {
        super(id);
    }

    @NotBlank(message = "邮箱不能为空")
    @Size(min = 0, max = 256, message = "邮箱长度不能超过 256 个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = 10, message = "功能长度不能超过 10 个字符")
    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    @Size(min = 0, max = 20, message = "订阅来源长度不能超过 20 个字符")
    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public Integer getCopiedDiscountCode() {
        return copiedDiscountCode;
    }

    public void setCopiedDiscountCode(Integer copiedDiscountCode) {
        this.copiedDiscountCode = copiedDiscountCode;
    }

    public String getCopiedDiscountCodeStr() {
        if (copiedDiscountCode == null) {
            return "";
        } else if (copiedDiscountCode == 0) {
            return "否";
        } else {
            return "是";
        }
    }
}