<% layout('/layouts/default.html', {title: '新闻管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('新闻管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('news:news:edit')){ %>
					<a href="${ctx}/news/news/form" class="btn btn-default btnTool" title="${text('新增新闻')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${news}" action="${ctx}/news/news/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('标题')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="120" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('分组')}：</label>
					<div class="control-inline width-120">
						<#form:select path="category" dictType="news_type" blankOption="true" class="form-control"/>
					</div>
				</div>
			<div class="form-group">
				<label class="control-label">${text('状态')}：</label>
				<div class="control-inline width-120">
					<#form:select path="newsStatus" dictType="sys_news_status" blankOption="true" class="form-control"/>
				</div>
			</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("标题")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/news/news/form?id='+row.id+'" class="btnList" data-title="${text("编辑新闻")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("介绍")}', name:'introduction', index:'a.introduction', width:150, align:"left"},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_at', width:150, align:"center"},
		{header:'${text("发布时间")}', name:'publishAt', index:'a.publish_at', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_at', width:150, align:"center"},
		{header:'${text("分组")}', name:'category', index:'a.category', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('news_type')}, val, '${text("未知")}', true);
		}},
		{header:'${text("是否一行显示")}', name:'isMain', index:'a.is_main', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
		}},
		{header:'${text("是否置顶")}', name:'isTop', index:'a.is_top', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_yes_no')}, val, '${text("未知")}', true);
			}},
		{header:'${text("状态")}', name:'newsStatus', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel(${@DictUtils.getDictListJson('sys_news_status')}, val, '${text("未知")}', true);
			}},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('news:news:edit')){ %>
				actions.push('<a href="${ctx}/news/news/form?id='+row.id+'" class="btnList" title="${text("编辑新闻")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/news/news/delete?id='+row.id+'" class="btnList" title="${text("删除新闻")}" data-confirm="${text("确认要删除该新闻吗？")}"><i class="fa fa-trash-o"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>