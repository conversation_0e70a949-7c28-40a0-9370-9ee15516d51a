package com.jeesite.modules.news.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 新闻管理Entity
 * <AUTHOR>
 * @version 2022-03-03
 */
@Table(name="news", alias="a", label="新闻信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="name", attrName="name", label="标题", queryType=QueryType.LIKE),
		@Column(name="content", attrName="content", label="内容", isQuery=false),
		@Column(name="cover", attrName="cover", label="封面", isQuery=false),
		@Column(name="introduction", attrName="introduction", label="介绍", isQuery=false),
		@Column(name="news_status", attrName="newsStatus", label="状态"),
		@Column(name="create_at", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="update_at", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="publish_at", attrName="publishAt", label="发布时间", isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="category", attrName="category", label="分组"),
		@Column(name="is_top", attrName="isTop", label="是否置顶"),
		@Column(name="is_main", attrName="isMain", label="是否一行显示", isQuery=false, isUpdateForce=true),
	}, orderBy="a.id DESC"
)
public class News extends DataEntity<News> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 标题
	private String content;		// 内容
	private String cover;		// 封面
	private String introduction;		// 介绍
	private String category;		// 分组
	private Date publishAt;		// 发布时间
	private Integer isMain;		// 是否一行显示
	private Integer isTop;		// 是否一行显示
	private String newsStatus;

	public News() {
		this(null);
	}

	public News(String id){
		super(id);
	}
	
	@NotBlank(message="标题不能为空")
	@Size(min=0, max=120, message="标题长度不能超过 120 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Size(min=0, max=500000, message="内容长度不能超过 500000 个字符")
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	@Size(min=0, max=255, message="封面长度不能超过 255 个字符")
	public String getCover() {
		return cover;
	}

	public void setCover(String cover) {
		this.cover = cover;
	}
	
	@Size(min=0, max=255, message="介绍长度不能超过 255 个字符")
	public String getIntroduction() {
		return introduction;
	}

	public void setIntroduction(String introduction) {
		this.introduction = introduction;
	}

	@Size(min=0, max=30, message="分组长度不能超过 30 个字符")
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
	
	public Integer getIsMain() {
		return isMain;
	}

	public void setIsMain(Integer isMain) {
		this.isMain = isMain;
	}

	public Date getPublishAt() {
		return publishAt;
	}

	public void setPublishAt(Date publishAt) {
		this.publishAt = publishAt;
	}

	public Integer getIsTop() {
		return isTop;
	}

	public void setIsTop(Integer isTop) {
		this.isTop = isTop;
	}

	public String getNewsStatus() {
		return newsStatus;
	}

	public void setNewsStatus(String newsStatus) {
		this.newsStatus = newsStatus;
	}
}