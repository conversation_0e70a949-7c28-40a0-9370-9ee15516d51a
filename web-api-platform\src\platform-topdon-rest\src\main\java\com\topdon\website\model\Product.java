package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;
import java.util.List;

public class Product {
    public static final String ENTITY_NAME = "PRODUCT";
    private String id;
    private String name;
    private String description;
    private String cover;
    private String searchCover;
    private String mobileCover;
    private Integer newProduct;
    private Integer discontinued;
    private LocalDateTime createAt;
    private Classification classification;
    private String classIdConcat;
    private List<Classification> classificationList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public Classification getClassification() {
        return classification;
    }

    public void setClassification(Classification classification) {
        this.classification = classification;
    }

    public String getClassIdConcat() {
        return classIdConcat;
    }

    public void setClassIdConcat(String classIdConcat) {
        this.classIdConcat = classIdConcat;
    }

    public List<Classification> getClassificationList() {
        return classificationList;
    }

    public void setClassificationList(List<Classification> classificationList) {
        this.classificationList = classificationList;
    }

    public String getSearchCover() {
        return searchCover;
    }

    public void setSearchCover(String searchCover) {
        this.searchCover = searchCover;
    }

    public String getMobileCover() {
        return mobileCover;
    }

    public void setMobileCover(String mobileCover) {
        this.mobileCover = mobileCover;
    }

    public Integer getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(Integer newProduct) {
        this.newProduct = newProduct;
    }

    public Integer getDiscontinued() {
        return discontinued;
    }

    public void setDiscontinued(Integer discontinued) {
        this.discontinued = discontinued;
    }

    public static Product apply(String id, String name) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        return product;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUNT = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUNT");
    }
}
