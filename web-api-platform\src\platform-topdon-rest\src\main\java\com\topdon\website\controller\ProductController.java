package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.service.ProductService;
import com.topdon.website.services.ProductServices;
import com.topdon.website.services.ShopifyProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("product")
public class ProductController extends ControllerSupport {
    private final ProductServices productServices;
    private final ProductService productService;
    private final ShopifyProductService shopifyProductService;

    @Autowired
    public ProductController(ProductServices productServices, ProductService productService, ShopifyProductService shopifyProductService) {
        this.productServices = productServices;
        this.productService = productService;
        this.shopifyProductService = shopifyProductService;
    }

    /**
     * 根据产品名称查询
     */
    @GetMapping("/info")
    public AbstractRestResponse getProductInfoByName(@RequestParam String name) {
        return AbstractRestResponse.apply(productService.getProductInfoByName(name));
    }

    @GetMapping("/extension")
    public AbstractRestResponse extension(String productName,@RequestParam(defaultValue = "false") Boolean draft) {
        return AbstractRestResponse.apply(productServices.extension(productName,draft));
    }

    @GetMapping("/overview")
    public AbstractRestResponse overview(String productName) {
        return AbstractRestResponse.apply(productServices.overview(productName));
    }

    @GetMapping("/shopify/overview")
    public AbstractRestResponse shopifyOverview(String productName,@RequestParam(defaultValue = "false",required = false) String draft) {
        return AbstractRestResponse.apply(shopifyProductService.overview(productName,draft));
    }


    @GetMapping("/topdon/overview")
    public AbstractRestResponse topdonOverview(String id, String snCode, String topdonToken) {
        return AbstractRestResponse.apply(productServices.getTopdon(id, snCode, topdonToken));
    }

    @GetMapping("/relations")
    public AbstractRestResponse relation(String productName) {
        return AbstractRestResponse.apply(productServices.relation(productName));
    }

    @GetMapping("/recommends")
    public AbstractRestResponse recommend() {
        return AbstractRestResponse.apply(productServices.recommend());
    }
}
