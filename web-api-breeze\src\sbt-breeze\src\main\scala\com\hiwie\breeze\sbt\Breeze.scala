package com.hiwie.breeze.sbt

import sbt.Keys.{autoScalaLibrary, crossPaths, javacOptions, name, _}
import sbt.{ModuleID, Project, file, _}

object Breeze {

  val breeze_version = "1.0.0-SNAPSHOT"

  def BreezeProject(projectName: String): Project = Project(projectName, file("src/" + projectName)).settings(
    name := projectName,
    javacOptions ++= Seq("-source", "1.8", "-target", "1.8", "-encoding", "utf8", "-g", "-Xlint"),
    javacOptions in doc := Seq("-source", "1.8", "-encoding", "utf8"),
    autoScalaLibrary := false,
    crossPaths := false
  )

  val cache: ModuleID = breeze("cache")
  val jdbc: ModuleID = breeze("jdbc")
  val json: ModuleID = breeze("json")
  val bpm: ModuleID = breeze("bpm")
  var import_module: ModuleID = breeze("import")
  val scheduler: ModuleID = breeze("scheduler")
  val hikari_cp: ModuleID = "com.zaxxer" % "HikariCP" % "3.2.0"
  val mysql_driver: ModuleID = "mysql" % "mysql-connector-java" % "8.0.11"

  private def breeze(artifact: String): ModuleID = "com.hiwie" % s"breeze-$artifact" % breeze_version

}
