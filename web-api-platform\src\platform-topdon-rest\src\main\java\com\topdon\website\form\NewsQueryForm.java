package com.topdon.website.form;

import com.hiwie.breeze.AbstractOption;
import com.topdon.website.model.News;

public class NewsQueryForm {

    private News.Category category;
    private String name;

    public AbstractOption<String> getName() {
        return AbstractOption.apply(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public AbstractOption<News.Category> getCategory() {
        return AbstractOption.apply(category);
    }

    public void setCategory(News.Category category) {
        this.category = category;
    }
}
