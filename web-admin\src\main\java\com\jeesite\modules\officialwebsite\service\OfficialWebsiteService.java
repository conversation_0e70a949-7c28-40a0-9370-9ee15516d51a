package com.jeesite.modules.officialwebsite.service;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.TreeService;
import com.jeesite.modules.officialwebsite.dao.OfficialWebsiteDao;
import com.jeesite.modules.officialwebsite.entity.OfficialWebsite;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 官网站点管理Service
 * <AUTHOR>
 * @version 2022-03-03
 */
@Service
@Transactional(readOnly=true)
public class OfficialWebsiteService extends TreeService<OfficialWebsiteDao, OfficialWebsite> {
	
	/**
	 * 获取单条数据
	 * @param officialWebsite
	 * @return
	 */
	@Override
	public OfficialWebsite get(OfficialWebsite officialWebsite) {
		return super.get(officialWebsite);
	}
	
	/**
	 * 查询分页数据
	 * @param officialWebsite 查询条件
	 * @param region.page 分页对象
	 * @return
	 */
	@Override
	public Page<OfficialWebsite> findPage(OfficialWebsite officialWebsite) {
		return super.findPage(officialWebsite);
	}
	
	/**
	 * 查询列表数据
	 * @param officialWebsite
	 * @return
	 */
	@Override
	public List<OfficialWebsite> findList(OfficialWebsite officialWebsite) {
		return super.findList(officialWebsite);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param officialWebsite
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(OfficialWebsite officialWebsite) {
		super.save(officialWebsite);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(officialWebsite, officialWebsite.getId(), "region_image");
	}
	
	/**
	 * 更新状态
	 * @param officialWebsite
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(OfficialWebsite officialWebsite) {
		super.updateStatus(officialWebsite);
	}
	
	/**
	 * 删除数据
	 * @param officialWebsite
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(OfficialWebsite officialWebsite) {
		super.delete(officialWebsite);
	}
	
}