package com.topdon.website.mappers;

import com.topdon.website.model.Cart;
import org.springframework.jdbc.core.RowMapper;

public class CartMapper {
    public static final RowMapper<Cart> DETAIL = (rs, index) -> {
        Cart detail = new Cart();
        detail.setId(rs.getLong("id"));
        detail.setProductId(rs.getLong("product_id"));
        detail.setSn(rs.getString("sn"));
        detail.setSnName(rs.getString("sn_name"));
        detail.setUserId(rs.getString("user_id"));
        detail.setShopifyProductId(rs.getString("shopify_product_id"));
        detail.setSkuId(rs.getLong("skuId"));
        detail.setShopifyVariantId(rs.getString("shopify_variant_id"));
        detail.setPrice(rs.getDouble("price"));
        detail.setCount(rs.getInt("count"));
        detail.setMsrpPrice(rs.getDouble("msrp_price"));
        return detail;
    };
}
