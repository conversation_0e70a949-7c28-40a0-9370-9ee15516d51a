package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 授权卖家管理
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "seller_authorization")
public class SellerAuthorization {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 授权证书编号
     */
    @TableField(value = "auth_cert_no")
    private String authCertNo;

    /**
     * 被授权主体
     */
    @TableField(value = "authorized_entity")
    private String authorizedEntity;

    /**
     * 发布状态 0草稿 1正式
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 授权类型 0普通 1独家
     */
    @TableField(value = "auth_type")
    private Integer authType;

    /**
     * 授权起始日期
     */
    @TableField(value = "auth_start_date")
    private Date authStartDate;

    /**
     * 授权结束日期
     */
    @TableField(value = "auth_end_date")
    private Date authEndDate;

    /**
     * 联系电话
     */
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @TableField(value = "contact_email")
    private String contactEmail;

    /**
     * 联系地址
     */
    @TableField(value = "contact_address")
    private String contactAddress;

    /**
     * 业务员
     */
    @TableField(value = "sales_rep")
    private String salesRep;

    /**
     * 经销商层级
     */
    @TableField(value = "dealer_level")
    private Integer dealerLevel;

    /**
     * 经销商编码
     */
    @TableField(value = "dealer_code")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @TableField(value = "dealer_name")
    private String dealerName;

    /**
     * 所属上级经销商
     */
    @TableField(value = "parent_dealer")
    private String parentDealer;

    /**
     * 其他
     */
    @TableField(value = "other_msg")
    private String otherMsg;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "update_date")
    private Date updateDate;
}