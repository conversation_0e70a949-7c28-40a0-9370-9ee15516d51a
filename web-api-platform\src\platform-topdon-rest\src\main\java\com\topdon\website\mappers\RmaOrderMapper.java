package com.topdon.website.mappers;

import com.topdon.website.model.Cart;
import com.topdon.website.model.RmaOrder;
import org.springframework.jdbc.core.RowMapper;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 16:53
 */
public class RmaOrderMapper {

    public static final RowMapper<RmaOrder> DETAIL = (rs, index) -> {
        RmaOrder detail = new RmaOrder();
        detail.setId(rs.getInt("id"));
        detail.setTicketNumber(rs.getString("ticketNumber"));
        detail.setSn(rs.getString("sn"));
        detail.setProductId(rs.getInt("product_id"));
        detail.setIssueType(rs.getString("issue_type"));
        detail.setClassificationId(rs.getString("classification_id"));
        detail.setProductName(rs.getString("product_name"));
        detail.setDescription(rs.getString("description"));
        detail.setOrderNo(rs.getString("order_no"));
        detail.setCountry(rs.getString("country"));
        detail.setStateRegion(rs.getString("state_region"));
        detail.setCity(rs.getString("city"));
        detail.setAddress1(rs.getString("address1"));
        detail.setPostalCode(rs.getString("postal_code"));
        detail.setSellerName(rs.getString("seller_name"));
        detail.setChannel(rs.getString("channel"));
        detail.setTicketStatus(rs.getString("ticket_status"));
        detail.setStatusModifiedTime(rs.getDate("status_modified_time"));
        detail.setSolution(rs.getString("solution"));
        detail.setEmail(rs.getString("email"));
        detail.setPhone(rs.getString("phone"));
        detail.setHuoWuLiuDanHao(rs.getString("huo_wu_liu_dan_hao"));
        detail.setTuiHuiHenZongHao(rs.getString("tui_hui_gen_zong_hao"));
        detail.setChuLiFangAn(rs.getString("chu_li_fang_an"));
        detail.setJingXiaoShangMingCheng(rs.getString("jing_xiao_shang_ming_cheng"));
        detail.setPlatform(rs.getString("platform"));
        return detail;
    };
}
