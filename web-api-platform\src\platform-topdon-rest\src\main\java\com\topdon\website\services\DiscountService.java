package com.topdon.website.services;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.topdon.website.model.AutomaticDiscount;
import com.topdon.website.model.Cart;
import org.mountcloud.graphql.GraphqlClient;
import org.mountcloud.graphql.request.query.DefaultGraphqlQuery;
import org.mountcloud.graphql.request.query.GraphqlQuery;
import org.mountcloud.graphql.request.result.ResultAttributtes;
import org.mountcloud.graphql.response.GraphqlResponse;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Named
public class DiscountService {

    private final CartService cartService;
    private final GraphqlClient graphqlClient;

    @Inject
    public DiscountService(CartService cartService, GraphqlClient graphqlClient) {
        this.cartService = cartService;
        this.graphqlClient = graphqlClient;
    }


    public AbstractEither<ErrorMessage, Map<String,Object>> getDiscountAmount(List<Long> cartIds) {

        return getAutoDiscountBase().fold(Left::apply, automaticDiscount -> {
            List<Cart> carts = Lists.newArrayListWithCapacity(cartIds.size());
            cartIds.forEach(cartId -> cartService.get(cartId).ifRight(carts::add));
            Map<String,Object> result = Maps.newHashMapWithExpectedSize(2);
            result.put("code",automaticDiscount.getTitle());

            if (automaticDiscount.getDiscountForType() == AutomaticDiscount.DiscountForType.ORDER) {
                if (automaticDiscount.getMinimumRequirementType() == AutomaticDiscount.MinimumRequirementType.QUANTITY) {
                    if (cartIds.size() >= Integer.parseInt(automaticDiscount.getMinimumRequirement())) {
                        if (automaticDiscount.getDiscountType() == AutomaticDiscount.DiscountType.FIX_AMOUNT) {
                            result.put("amount",Double.parseDouble(automaticDiscount.getDiscountValue()));
                        } else {
                            double sum = carts.stream().map(Cart::getPrice).reduce(Double::sum).get();
                            result.put("amount",sum * Double.parseDouble(automaticDiscount.getDiscountValue()));
                        }
                    } else {
                        result.put("amount",0);
                    }
                } else {
                    double sum = carts.stream().map(Cart::getPrice).reduce(Double::sum).get();
                    if (sum >= Integer.parseInt(automaticDiscount.getMinimumRequirement())) {
                        result.put("amount",Double.parseDouble(automaticDiscount.getDiscountValue()));
                    } else {
                        result.put("amount",0);
                    }
                }
            } else {
                AtomicReference<Double> amount = new AtomicReference<>((double) 0);
                for (Cart cart : carts) {
                    //检查是否每个商品都参与该折扣
                    if (!automaticDiscount.isAppliesOnEachItem()) {
                        if (amount.get() > 0) {
                            break;
                        }
                    }
                    getAutoDiscount(cart.getShopifyProductId()).ifRight(discount -> {
                        boolean ifVariantEnable = discount.getVariants() != null && discount.getVariants().stream().anyMatch(s -> s.equalsIgnoreCase(cart.getShopifyHQLVariantId()));
                        boolean ifProductEnable = discount.getProducts() != null && discount.getProducts().stream().anyMatch(s -> s.equalsIgnoreCase(cart.getShopifyHQLProductId()));
                        //只要有一个条件满足，则说明该产品满足折扣条件
                        if (ifVariantEnable || ifProductEnable || discount.isCanDiscount()) {
                            if (discount.getMinimumRequirementType() == AutomaticDiscount.MinimumRequirementType.QUANTITY) {
                                int quantity = carts.stream().map(Cart::getCount).reduce(Integer::sum).get();
                                if (quantity >= Integer.parseInt(discount.getMinimumRequirement())) {
                                    if (discount.getDiscountType() == AutomaticDiscount.DiscountType.FIX_AMOUNT) {
                                        amount.updateAndGet(v -> v + Double.parseDouble(discount.getDiscountValue()));
                                    } else {
                                        double sum = cart.getPrice() * cart.getCount();
                                        amount.updateAndGet(v -> v + sum * Double.parseDouble(discount.getDiscountValue()));
                                    }
                                } else {
                                    amount.updateAndGet(v -> v + 0);
                                }
                            } else {
                                double sum = cart.getPrice() * cart.getCount();
                                if (sum >= Double.parseDouble(discount.getMinimumRequirement())) {
                                    if (discount.getDiscountType() == AutomaticDiscount.DiscountType.FIX_AMOUNT) {
                                        amount.updateAndGet(v -> v + Double.parseDouble(discount.getDiscountValue()));
                                    } else {
                                        amount.updateAndGet(v -> v + sum * Double.parseDouble(discount.getDiscountValue()));
                                    }
                                } else {
                                    amount.updateAndGet(v -> v + 0);
                                }
                            }
                        }

                    });
                }
                result.put("amount",amount.get());

            }
            return Right.apply(result);
        });

    }

    public AbstractEither<ErrorMessage, AutomaticDiscount> getAutoDiscountBase() {
        GraphqlQuery query = new DefaultGraphqlQuery("automaticDiscountNodes");
        query.addParameter("first", 1);
        query.addParameter("query", "status:'active'");
        ResultAttributtes root = new ResultAttributtes("edges");
        ResultAttributtes node = new ResultAttributtes("node");

        node.addResultAttributes("id");
        ResultAttributtes automaticDiscount = new ResultAttributtes("automaticDiscount");

        ResultAttributtes basic = new ResultAttributtes("... on DiscountAutomaticBasic");
        basic.addResultAttributes("title", "summary", "discountClass");
        ResultAttributtes minimumRequirement = new ResultAttributtes("minimumRequirement");
        ResultAttributtes quantity = new ResultAttributtes("... on DiscountMinimumQuantity");
        quantity.addResultAttributes("greaterThanOrEqualToQuantity");
        ResultAttributtes subtotal = new ResultAttributtes("... on DiscountMinimumSubtotal");
        ResultAttributtes greaterThanOrEqualToSubtotal = new ResultAttributtes("greaterThanOrEqualToSubtotal");
        ResultAttributtes moneyV2 = new ResultAttributtes("... on MoneyV2");
        moneyV2.addResultAttributes("amount");
        greaterThanOrEqualToSubtotal.addResultAttributes(moneyV2);
        subtotal.addResultAttributes(greaterThanOrEqualToSubtotal);
        minimumRequirement.addResultAttributes(quantity, subtotal);

        ResultAttributtes customerGets = new ResultAttributtes("customerGets");
        ResultAttributtes value = new ResultAttributtes("value");
        ResultAttributtes amountObject = new ResultAttributtes("... on DiscountAmount");
        ResultAttributtes amount = new ResultAttributtes("amount");
        ResultAttributtes otherMoneyV2 = new ResultAttributtes("... on MoneyV2");
        ResultAttributtes otherAmount = new ResultAttributtes("amount");
        otherMoneyV2.addResultAttributes(otherAmount);
        amount.addResultAttributes(otherMoneyV2);
        amountObject.addResultAttributes(amount);
        amountObject.addResultAttributes("appliesOnEachItem");
        ResultAttributtes percentageObject = new ResultAttributtes("... on DiscountPercentage");
        percentageObject.addResultAttributes("percentage");
        value.addResultAttributes(amountObject, percentageObject);
        customerGets.addResultAttributes(value);
        basic.addResultAttributes(minimumRequirement, customerGets);
        automaticDiscount.addResultAttributes(basic);
        node.addResultAttributes(automaticDiscount);
        root.addResultAttributes(node);
        query.addResultAttributes(root);

        try {
            GraphqlResponse response = graphqlClient.doQuery(query);
            Map result = response.getData();

            AutomaticDiscount discount = AutomaticDiscount.apply(result);
            return Right.apply(discount);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Left.apply(AutomaticDiscount.Errors.NOT_FOUND);
    }

    public AbstractEither<ErrorMessage, AutomaticDiscount> getAutoDiscount(String productId) {
        GraphqlQuery query = new DefaultGraphqlQuery("automaticDiscountNodes");
        query.addParameter("first", 1);
        query.addParameter("query", "status:'active'");
        ResultAttributtes root = new ResultAttributtes("edges");
        ResultAttributtes node = new ResultAttributtes("node");

        node.addResultAttributes("id");
        ResultAttributtes automaticDiscount = new ResultAttributtes("automaticDiscount");

        ResultAttributtes basic = new ResultAttributtes("... on DiscountAutomaticBasic");
        basic.addResultAttributes("title", "summary", "discountClass");
        ResultAttributtes minimumRequirement = new ResultAttributtes("minimumRequirement");
        ResultAttributtes quantity = new ResultAttributtes("... on DiscountMinimumQuantity");
        quantity.addResultAttributes("greaterThanOrEqualToQuantity");
        ResultAttributtes subtotal = new ResultAttributtes("... on DiscountMinimumSubtotal");
        ResultAttributtes greaterThanOrEqualToSubtotal = new ResultAttributtes("greaterThanOrEqualToSubtotal");
        ResultAttributtes moneyV2 = new ResultAttributtes("... on MoneyV2");
        moneyV2.addResultAttributes("amount");
        greaterThanOrEqualToSubtotal.addResultAttributes(moneyV2);
        subtotal.addResultAttributes(greaterThanOrEqualToSubtotal);
        minimumRequirement.addResultAttributes(quantity, subtotal);


        ResultAttributtes customerGets = new ResultAttributtes("customerGets");
        ResultAttributtes items = new ResultAttributtes("items");

        ResultAttributtes itemsProducts = new ResultAttributtes("... on DiscountProducts");

        ResultAttributtes variants = new ResultAttributtes("productVariants(first:20)");
        ResultAttributtes variantsNodes = new ResultAttributtes("nodes");
        variantsNodes.addResultAttributes("id", "title");
        variants.addResultAttributes(variantsNodes);

        ResultAttributtes products = new ResultAttributtes("products(first:20)");
        ResultAttributtes productsNodes = new ResultAttributtes("nodes");
        productsNodes.addResultAttributes("id", "title");
        products.addResultAttributes(productsNodes);

        itemsProducts.addResultAttributes(variants, products);

        ResultAttributtes itemsCollections = new ResultAttributtes("... on DiscountCollections");

        ResultAttributtes collections = new ResultAttributtes("collections(first:10)");
        ResultAttributtes collectionsNodes = new ResultAttributtes("nodes");
        collectionsNodes.addResultAttributes("hasProduct(id:\\\"gid://shopify/Product/" + productId + "\\\")");
        collections.addResultAttributes(collectionsNodes);
        itemsCollections.addResultAttributes(collections);

        items.addResultAttributes(itemsProducts, itemsCollections);


        ResultAttributtes value = new ResultAttributtes("value");
        ResultAttributtes amountObject = new ResultAttributtes("... on DiscountAmount");
        ResultAttributtes amount = new ResultAttributtes("amount");
        ResultAttributtes otherMoneyV2 = new ResultAttributtes("... on MoneyV2");
        ResultAttributtes otherAmount = new ResultAttributtes("amount");
        otherMoneyV2.addResultAttributes(otherAmount);
        amount.addResultAttributes(otherMoneyV2);
        amountObject.addResultAttributes(amount);
        amountObject.addResultAttributes("appliesOnEachItem");
        ResultAttributtes percentageObject = new ResultAttributtes("... on DiscountPercentage");
        percentageObject.addResultAttributes("percentage");
        value.addResultAttributes(amountObject, percentageObject);
        customerGets.addResultAttributes(items, value);
        basic.addResultAttributes(minimumRequirement, customerGets);
        automaticDiscount.addResultAttributes(basic);
        node.addResultAttributes(automaticDiscount);
        root.addResultAttributes(node);
        query.addResultAttributes(root);

        try {
            GraphqlResponse response = graphqlClient.doQuery(query);
            Map result = response.getData();

            AutomaticDiscount discount = AutomaticDiscount.apply(result);
            return Right.apply(discount);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Left.apply(AutomaticDiscount.Errors.NOT_FOUND);
    }

}
