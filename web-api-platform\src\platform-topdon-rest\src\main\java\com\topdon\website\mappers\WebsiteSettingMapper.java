package com.topdon.website.mappers;

import com.topdon.website.model.WebsiteSetting;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class WebsiteSettingMapper {

    public static final RowMapper<WebsiteSetting> DETAIL = (rs, index) -> {
        WebsiteSetting detail = new WebsiteSetting();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("key"));
        detail.setValue(rs.getString("value"));
        detail.setRemarks(rs.getString("remarks"));
        return detail;
    };

}
