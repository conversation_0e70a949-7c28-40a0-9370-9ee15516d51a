package com.topdon.website.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.entity.Product;
import com.topdon.website.mapper.ProductMapper;
import com.topdon.website.service.ProductService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public List<Product> getCompareProductList(String classificationCode, boolean draft) {
        return baseMapper.getCompareProductList(classificationCode, draft);
    }

    @Override
    public AbstractEither<ErrorMessage, Product> getProductInfoByName(String name) {
        Product product = this.lambdaQuery()
                .eq(Product::getName, name)
                .last("limit 1")
                .one();
        return Right.apply(product);
    }
}
