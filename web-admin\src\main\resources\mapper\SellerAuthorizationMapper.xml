<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.SellerAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.SellerAuthorization">
    <!--@mbg.generated-->
    <!--@Table seller_authorization-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="auth_cert_no" jdbcType="VARCHAR" property="authCertNo" />
    <result column="authorized_entity" jdbcType="VARCHAR" property="authorizedEntity" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="auth_type" jdbcType="INTEGER" property="authType" />
    <result column="auth_start_date" jdbcType="DATE" property="authStartDate" />
    <result column="auth_end_date" jdbcType="DATE" property="authEndDate" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="contact_address" jdbcType="VARCHAR" property="contactAddress" />
    <result column="sales_rep" jdbcType="VARCHAR" property="salesRep" />
    <result column="dealer_level" jdbcType="INTEGER" property="dealerLevel" />
    <result column="dealer_code" jdbcType="VARCHAR" property="dealerCode" />
    <result column="dealer_name" jdbcType="VARCHAR" property="dealerName" />
    <result column="parent_dealer" jdbcType="VARCHAR" property="parentDealer" />
    <result column="other_msg" jdbcType="VARCHAR" property="otherMsg" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, auth_cert_no, authorized_entity, `status`, auth_type, auth_start_date, auth_end_date, 
    contact_phone, contact_email, contact_address, sales_rep, dealer_level, dealer_code, 
    dealer_name, parent_dealer, other_msg, create_by, create_date, update_by, update_date
  </sql>

  <select id="getPage" resultType="com.topdon.admin.vo.SellerAuthorizationVo">
    <include refid="selectListSql">
    </include>
  </select>

  <sql id="selectListSql">
    select
    distinct sa.id did
    ,sa.*
    from seller_authorization sa
    <if test="param.orderProp == 'r.name'">
        left join seller_auth_region_mapping sarm on sa.id = sarm.seller_auth_id
        left join region r on r.code = sarm.region_code
    </if>
    <if test="param.orderProp == 'pl.name'">
        left join seller_auth_product_lines_mapping saplm on sa.id = saplm.seller_auth_id
        left join product_lines pl on saplm.product_lines_id = pl.id
    </if>
    <if test="param.orderProp == 'ap.auth_platform'">
        left join authorized_platforms ap on ap.seller_auth_id = sa.id
    </if>
    <where>
      <if test="param.authCertNo != null and param.authCertNo != ''">
        sa.auth_cert_no = #{param.authCertNo,jdbcType=VARCHAR}
      </if>
      <if test="param.authorizedEntity != null and param.authorizedEntity != ''">
        and sa.authorized_entity like concat('%',#{param.authorizedEntity,jdbcType=VARCHAR},'%')
      </if>
      <if test="param.dealerLevel !=null and param.dealerLevel.size > 0">
        and sa.dealer_level in
        <foreach collection="param.dealerLevel" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="param.status !=null and param.status.size > 0">
        and sa.status in
        <foreach collection="param.status" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="param.authType !=null and param.authType.size > 0">
        and sa.auth_type in
        <foreach collection="param.authType" separator="," open="(" close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="param.authStartDate !=null">
        and sa.auth_start_date &gt;= #{param.authStartDate,jdbcType=TIMESTAMP}
      </if>
      <if test="param.authEndDate !=null">
        and sa.auth_end_date &lt;= #{param.authEndDate,jdbcType=TIMESTAMP}
      </if>
      <if test="param.authPlatform !=null and param.authPlatform != ''">
        and sa.id in (select seller_auth_id from authorized_platforms a where auth_platform like concat('%',#{param.authPlatform,jdbcType=VARCHAR},'%'))
      </if>
      <if test="param.regionList !=null and param.regionList.size > 0">
        and sa.id in (select seller_auth_id from seller_auth_region_mapping a where region_code in <foreach collection="param.regionList" item="item" close=")" open="(" separator=",">#{item}</foreach>)
      </if>
      <if test="param.productLinesList !=null and param.productLinesList.size > 0">
        and sa.id in (select seller_auth_id from seller_auth_product_lines_mapping a where product_lines_id in <foreach collection="param.productLinesList" item="item" close=")" open="(" separator=",">#{item}</foreach>)
      </if>
    </where>
    <if test="param.order != null and param.order != ''">
      order by ${param.orderProp} ${param.order}
    </if>
  </sql>

  <select id="getList" resultType="com.topdon.admin.vo.SellerAuthorizationVo">
    <include refid="selectListSql">
    </include>
  </select>
</mapper>