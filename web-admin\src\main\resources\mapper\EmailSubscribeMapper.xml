<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.EmailSubscribeMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.EmailSubscribe">
    <!--@mbg.generated-->
    <!--@Table email_subscribe-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="from" jdbcType="VARCHAR" property="from" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="copied_discount_code" jdbcType="INTEGER" property="copiedDiscountCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, email, `from`, create_at, site, copied_discount_code
  </sql>

  <sql id="getListSql">
    select *
    from email_subscribe a
    <where>
      <if test="param.email != null and param.email != ''">
        a.email like concat('%',#{param.email},'%')
      </if>
      <if test="param.from != null and param.from != ''">
        and a.from = #{param.from}
      </if>
      <if test="param.site != null and param.site != ''">
        and a.site = #{param.site}
      </if>
      <if test="param.startCreateAt != null and param.startCreateAt != ''">
        and a.create_at &gt;= #{param.startCreateAt}
      </if>
      <if test="param.endCreateAt != null and param.endCreateAt != ''">
        and a.create_at &lt;= #{param.endCreateAt}
      </if>
      <if test="param.copiedDiscountCode != null">
        and a.copied_discount_code = #{param.copiedDiscountCode}
      </if>
    </where>
  </sql>

  <select id="getPage" resultMap="BaseResultMap">
    <include refid="getListSql">
    </include>
    <if test="param.orderBy != null and param.orderBy != ''">
      order by ${param.orderBy}
    </if>
  </select>

  <select id="getExcelList" resultType="com.topdon.admin.vo.EmailSubscribeExcelVo">
    <include refid="getListSql">
    </include>
  </select>

  <select id="selectBySiteAndEmail" resultMap="BaseResultMap">
    select * from email_subscribe
    where site = #{site} and email = #{email}
    limit 1
  </select>
</mapper>