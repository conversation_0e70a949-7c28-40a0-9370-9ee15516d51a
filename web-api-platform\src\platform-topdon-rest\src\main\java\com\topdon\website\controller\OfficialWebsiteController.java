package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.service.OfficialWebsiteService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/official/website")
public class OfficialWebsiteController {

    @Resource
    private OfficialWebsiteService officialWebsiteService;

    @GetMapping("/list")
    public AbstractRestResponse getList() {
        return AbstractRestResponse.apply(officialWebsiteService.getList());
    }
}
