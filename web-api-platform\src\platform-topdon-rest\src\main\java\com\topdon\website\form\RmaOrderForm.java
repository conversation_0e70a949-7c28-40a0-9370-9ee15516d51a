package com.topdon.website.form;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3 17:35
 */
@Data
public class RmaOrderForm {

    private String title;
    private String sn;
    private Integer productId;
    private String issueType;
    private String ClassificationId;
    private String ClassificationName;
    private String productName;
    private String description;
    private String orderNo;
    private String country;
    private String stateRegion;
    private String city;
    private String address1;
    private String postalCode;
    private String sellerName;
    private String platform;
    private List<String> uploads;
    private String channel;
    private String email;
    private String jingxiaoshang;
    private String phone;
    private String chexing;
    private Date createTime;
}
