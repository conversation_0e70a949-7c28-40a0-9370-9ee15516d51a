spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

server.id=PM
server.domain=https://www.topdon.com
#server.domain=http://**************:8082/api

mail.host=smtp.gmail.com
mail.port=587
mail.user_name=<EMAIL>
mail.password=ahpznayulikdyhhx

shopify.api.graphql.version=2022-10
shopify.graphql.url=https://topdon-it.myshopify.com/admin/api/2022-10/graphql.json
shopify.admin.graphql.token=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
shopify.storefront.graphql.url=https://topdon-it.myshopify.com/api/2022-10/graphql.json
shopify.storefront.graphql.token=5d6dd7a3b4d08828b52242bfacd0ca6f

support.receive.email=<EMAIL>

file.root.path=/home/<USER>/webapps/filetmp/

file.zip.root.path=/opt/data
file.client.aliyun.endpoint=oss-cn-hongkong.aliyuncs.com
file.client.aliyun.accessKeyId=LTAI5tFbPQurigpDWGq7F51i
file.client.aliyun.accessKeySecret=******************************
file.client.aliyun.bucketName=lenkor-plat
file.client.aliyun.root=topdon-web/
file.client.aliyun.proxyEndpoint=web-file.topdon.com

topdon.url=https://api.topdon.com
desk.sdk.config.fileName=zohoSdkConfig
zoho.config.enabled=true
zoho.config.clientId=1000.******************************
zoho.config.clientSecret=4f673246bc3217be015821aeeb2f17f6d652b1171b
zoho.config.redirectUri=https://official-web-api.topdon.com/api/zoho/auth/callBackCode
zoho.config.timeOut=900
zoho.config.scope=Desk.tickets.ALL,Desk.tasks.ALL,Desk.settings.ALL,Desk.events.ALL,Desk.basic.ALL,Desk.contacts.ALL
zoho.config.minLogLevel=INFO
zoho.config.dc=com
zoho.config.userIdentifier=<EMAIL>
zoho.config.oauthTokensFilePath=/var/zohoOauth