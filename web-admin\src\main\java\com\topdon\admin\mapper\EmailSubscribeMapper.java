package com.topdon.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.topdon.admin.dto.EmailSubscribeListDto;
import com.topdon.admin.dto.EmailSubscribePageDto;
import com.topdon.admin.entity.EmailSubscribe;
import com.topdon.admin.vo.EmailSubscribeExcelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EmailSubscribeMapper extends BaseMapper<EmailSubscribe> {
    PageDTO<EmailSubscribe> getPage(PageDTO<Object> objectPageDTO, @Param("param") EmailSubscribePageDto emailSubscribe);

    List<EmailSubscribeExcelVo> getExcelList(@Param("param") EmailSubscribeListDto emailSubscribe);

    EmailSubscribe selectBySiteAndEmail(@Param("site") String site, @Param("email") String email);
}