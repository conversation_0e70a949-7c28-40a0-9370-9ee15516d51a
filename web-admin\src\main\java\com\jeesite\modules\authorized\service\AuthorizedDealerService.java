package com.jeesite.modules.authorized.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.authorized.entity.AuthorizedDealer;
import com.jeesite.modules.authorized.dao.AuthorizedDealerDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 授权经销商管理Service
 * <AUTHOR>
 * @version 2022-03-07
 */
@Service
@Transactional(readOnly=true)
public class AuthorizedDealerService extends CrudService<AuthorizedDealerDao, AuthorizedDealer> {
	
	/**
	 * 获取单条数据
	 * @param authorizedDealer
	 * @return
	 */
	@Override
	public AuthorizedDealer get(AuthorizedDealer authorizedDealer) {
		return super.get(authorizedDealer);
	}
	
	/**
	 * 查询分页数据
	 * @param authorizedDealer 查询条件
	 * @param authorizedDealer.page 分页对象
	 * @return
	 */
	@Override
	public Page<AuthorizedDealer> findPage(AuthorizedDealer authorizedDealer) {
		return super.findPage(authorizedDealer);
	}
	
	/**
	 * 查询列表数据
	 * @param authorizedDealer
	 * @return
	 */
	@Override
	public List<AuthorizedDealer> findList(AuthorizedDealer authorizedDealer) {
		return super.findList(authorizedDealer);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param authorizedDealer
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(AuthorizedDealer authorizedDealer) {
		super.save(authorizedDealer);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(authorizedDealer, authorizedDealer.getId(), "authorizedDealer_cover_image");
	}
	
	/**
	 * 更新状态
	 * @param authorizedDealer
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(AuthorizedDealer authorizedDealer) {
		super.updateStatus(authorizedDealer);
	}
	
	/**
	 * 删除数据
	 * @param authorizedDealer
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(AuthorizedDealer authorizedDealer) {
		super.delete(authorizedDealer);
	}
	
}