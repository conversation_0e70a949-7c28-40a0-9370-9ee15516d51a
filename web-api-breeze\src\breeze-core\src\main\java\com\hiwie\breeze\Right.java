package com.hiwie.breeze;

import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 */
public final class Right<A, B> extends AbstractEither<A, B> {

    private final B value;

    private Right(B value) {
        this.value = value;
    }

    public static <A, B> Right<A, B> apply(final B b) {
        return new Right<>(b);
    }

    @Override
    protected A getLeft() {
        throw new NoSuchElementException("getLeft() on Right()");
    }

    @Override
    protected B getRight() {
        return value;
    }

    @Override
    public Boolean isLeft() {
        return false;
    }

    @Override
    public Boolean isRight() {
        return true;
    }

    @Override
    public String toString() {
        return "Right(" + value + ")";
    }

}
