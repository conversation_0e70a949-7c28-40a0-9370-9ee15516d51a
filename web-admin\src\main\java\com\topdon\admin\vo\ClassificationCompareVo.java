package com.topdon.admin.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jeesite.modules.classification.entity.Classification;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClassificationCompareVo extends Classification {

    private Integer classificationCompareId;

    private Boolean classificationCompareDraft;
    private Date classificationCompareReleaseDate;

    private String classificationCompareCreateBy;
    private Date classificationCompareCreateDate;
    private String classificationCompareUpdateBy;
    private Date classificationCompareUpdateDate;
}
