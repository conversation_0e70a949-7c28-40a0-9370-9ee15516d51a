package com.hiwie.breeze.jdbc;


import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.None;
import com.hiwie.breeze.Some;
import com.hiwie.breeze.util.ObjectUtil;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 */
public abstract class AbstractMapperSupport {

    protected static <T> AbstractOption<T> nullable(String columnName, RowMapper<T> mapper, ResultSet rs, int rowNum) throws SQLException {
        if (ObjectUtil.isNull(rs.getString(columnName))) {
            return None.apply();
        } else {
            return Some.apply(mapper.mapRow(rs, rowNum));
        }
    }

    protected static <T> AbstractOption<T> ifExist(String columnName, RowMapper<T> mapper, ResultSet rs, int rowNum) throws SQLException {
        ResultSetMetaData meta = rs.getMetaData();
        int numCol = meta.getColumnCount();
        for (int i = 1; i <= numCol; i++) {
            if (meta.getColumnName(i).equalsIgnoreCase(columnName)) {
                return nullable(columnName, mapper, rs, rowNum);
            }
        }
        return None.apply();
    }

}
