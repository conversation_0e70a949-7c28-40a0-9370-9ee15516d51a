package com.hiwie.breeze.util;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateUtil {

    private static final Pattern PERIOD_PATTERN = Pattern.compile("P([0-9]+Y)?([0-9]+M)?([0-9]+W)?([0-9]+D)?");

    private static final Pattern DURATION_PATTERN = Pattern.compile("P.*(T([0-9]+H)?([0-9]+M)?([0-9]+S)?)");

    private static final String PERIOD_EMPTY = "P";

    private static final String DURATION_EMPTY = "T";

    public static Duration getDuration(String s) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime after = now;
        Matcher periodMatcher = PERIOD_PATTERN.matcher(s);
        if (periodMatcher.find() && !PERIOD_EMPTY.equals(periodMatcher.group())) {
            after = now.plus(Period.parse(periodMatcher.group()));
        }
        Matcher durationMatcher = DURATION_PATTERN.matcher(s);
        if (durationMatcher.find() && !DURATION_EMPTY.equals(durationMatcher.group(1))) {
            after = after.plus(Duration.parse(PERIOD_EMPTY + durationMatcher.group(1)));
        }

        if (Duration.between(now, after).getSeconds() == 0) {
            throw new IllegalArgumentException("Invalid duration format: " + s);
        }

        return Duration.between(now, after);
    }

    public static int getSeason(LocalDateTime localDateTime) {
        int month = localDateTime.getMonthValue();
        if (month != 1 && month != 2 && month != 3) {
            if (month != 4 && month != 5 && month != 6) {
                return month != 7 && month != 8 && month != 9 ? 4 : 3;
            } else {
                return 2;
            }
        } else {
            return 1;
        }

    }

}
