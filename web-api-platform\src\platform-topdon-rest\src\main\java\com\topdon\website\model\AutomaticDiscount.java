package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.SCConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AutomaticDiscount {
    public static final String ENTITY_NAME = "AUTOMATIC_DISCOUNT";
    private String title;
    private String summary;
    private DiscountForType discountForType;
    private MinimumRequirementType minimumRequirementType;
    private String minimumRequirement;
    private DiscountType discountType;
    private String discountValue;

    private boolean appliesOnEachItem;

    private List<String> variants;

    private List<String> products;

    private boolean canDiscount;

    public enum DiscountForType {
        PRODUCT, ORDER
    }

    public enum MinimumRequirementType {
        QUANTITY, SUB_TOTAL
    }

    public enum DiscountType {
        FIX_AMOUNT, PERCENTAGE
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public DiscountForType getDiscountForType() {
        return discountForType;
    }

    public void setDiscountForType(DiscountForType discountForType) {
        this.discountForType = discountForType;
    }

    public MinimumRequirementType getMinimumRequirementType() {
        return minimumRequirementType;
    }

    public void setMinimumRequirementType(MinimumRequirementType minimumRequirementType) {
        this.minimumRequirementType = minimumRequirementType;
    }

    public String getMinimumRequirement() {
        return minimumRequirement;
    }

    public void setMinimumRequirement(String minimumRequirement) {
        this.minimumRequirement = minimumRequirement;
    }

    public DiscountType getDiscountType() {
        return discountType;
    }

    public void setDiscountType(DiscountType discountType) {
        this.discountType = discountType;
    }

    public String getDiscountValue() {
        return discountValue;
    }

    public void setDiscountValue(String discountValue) {
        this.discountValue = discountValue;
    }

    public boolean isAppliesOnEachItem() {
        return appliesOnEachItem;
    }

    public void setAppliesOnEachItem(boolean appliesOnEachItem) {
        this.appliesOnEachItem = appliesOnEachItem;
    }

    public List<String> getVariants() {
        return variants;
    }

    public void setVariants(List<String> variants) {
        this.variants = variants;
    }

    public List<String> getProducts() {
        return products;
    }

    public void setProducts(List<String> products) {
        this.products = products;
    }

    public boolean isCanDiscount() {
        return canDiscount;
    }

    public void setCanDiscount(boolean canDiscount) {
        this.canDiscount = canDiscount;
    }

    public static AutomaticDiscount apply(Map result) {
        List<Map<String, Map<String, Map>>> edges = (ArrayList) (((Map<String, Map>) result.get("data")).get("automaticDiscountNodes")).get("edges");
        if (edges.size() == 0) {
            return new AutomaticDiscount();
        }
        Map<String, Map<String, Map>> resultNode = edges.get(0);
        Map<String, Object> discountMap = resultNode.get("node").get("automaticDiscount");
        AutomaticDiscount discount = new AutomaticDiscount();
        discount.setTitle((String) discountMap.get("title"));
        discount.setSummary((String) discountMap.get("summary"));
        discount.setDiscountForType(((String) discountMap.get("discountClass")).equalsIgnoreCase("PRODUCT") ? AutomaticDiscount.DiscountForType.PRODUCT : AutomaticDiscount.DiscountForType.ORDER);

        Map<String, Object> minimumRequirementMap = (Map<String, Object>) discountMap.get("minimumRequirement");
        String greaterThanOrEqualToQuantity = (String) minimumRequirementMap.get("greaterThanOrEqualToQuantity");
        if (StringUtil.isNotEmpty(greaterThanOrEqualToQuantity)) {
            discount.setMinimumRequirementType(MinimumRequirementType.QUANTITY);
            discount.setDiscountValue(greaterThanOrEqualToQuantity);
        } else {
            discount.setMinimumRequirementType(MinimumRequirementType.SUB_TOTAL);
            discount.setMinimumRequirement(((Map<String, String>) minimumRequirementMap.get("greaterThanOrEqualToSubtotal")).get("amount"));
        }
        Map<String, Map<String, Object>> customerGets = (Map<String, Map<String, Object>>) discountMap.get("customerGets");

        Map<String, Object> value = customerGets.get("value");
        if (value.get("appliesOnEachItem") != null) {
            discount.setAppliesOnEachItem((boolean) value.get("appliesOnEachItem"));
        } else {
            discount.setAppliesOnEachItem(true);
        }


        Object percentage = value.get("percentage");
        if (percentage != null) {
            discount.setDiscountType(DiscountType.PERCENTAGE);
            discount.setDiscountValue(String.valueOf(percentage));
        } else {
            discount.setDiscountType(DiscountType.FIX_AMOUNT);
            discount.setDiscountValue(((Map<String, String>) value.get("amount")).get("amount"));
        }
        Map<String, Object> items = customerGets.get("items");
        if (items != null) {
            Map<String, List<Map<String, String>>> variantsContainer = (Map<String, List<Map<String, String>>>) items.get("productVariants");
            Map<String, List<Map<String, String>>> productsContainer = (Map<String, List<Map<String, String>>>) items.get("products");
            Map<String, List<Map<String, Boolean>>> collectionsContainer = (Map<String, List<Map<String, Boolean>>>) items.get("collections");
            if (variantsContainer != null) {
                List<Map<String, String>> variants = variantsContainer.get("nodes");
                discount.setVariants(variants.stream().map(var -> var.get("id")).collect(Collectors.toList()));
            }
            if (productsContainer != null) {
                List<Map<String, String>> products = productsContainer.get("nodes");
                discount.setProducts(products.stream().map(var -> var.get("id")).collect(Collectors.toList()));
            }
            if (collectionsContainer != null) {
                List<Map<String, Boolean>> collections = collectionsContainer.get("nodes");
                discount.setCanDiscount(collections.stream().anyMatch(col -> col.get("hasProduct")));
            }
        }
        return discount;
    }


    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUND");
    }
}
