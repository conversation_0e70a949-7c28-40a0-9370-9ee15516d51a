package com.topdon.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.topdon.admin.dto.MenuClickLogListDto;
import com.topdon.admin.dto.MenuClickLogPageDto;
import com.topdon.admin.entity.MenuClickLog;
import com.topdon.admin.vo.MenuClickLogExcelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MenuClickLogMapper extends BaseMapper<MenuClickLog> {
    PageDTO<MenuClickLog> getPage(PageDTO<Object> objectPageDTO, @Param("param") MenuClickLogPageDto menuClickLogPageDto);

    List<MenuClickLogExcelVo> getExcelList(@Param("param") MenuClickLogListDto menuClickLogListDto);
}