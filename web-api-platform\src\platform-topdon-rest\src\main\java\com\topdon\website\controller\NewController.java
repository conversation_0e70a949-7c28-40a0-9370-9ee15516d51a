package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/news")
public class NewController extends ControllerSupport {

    private final NewsService newsService;

    @Autowired
    public NewController(NewsService newsService) {
        this.newsService = newsService;
    }

    @GetMapping
    public AbstractRestResponse get(String id) {
        return AbstractRestResponse.apply(newsService.get(id));
    }
}
