<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.PropertyCategoryMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.PropertyCategory">
    <!--@mbg.generated-->
    <!--@Table property_category-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="classification_compare_id" jdbcType="INTEGER" property="classificationCompareId" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="draft" jdbcType="BOOLEAN" property="draft" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, classification_compare_id, parent_id, category, sort, draft, create_by, create_date, 
    update_by, update_date
  </sql>

  <select id="getList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from property_category
    where classification_compare_id = (select id from classification_compare where classification_code = #{classificationCode,jdbcType=VARCHAR} limit 1) and draft = #{draft}
    order by sort
    </select>
</mapper>