package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.form.CartForm;
import com.topdon.website.services.CartShopifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("checkout")
public class CheckoutController extends ControllerSupport {

    private final CartShopifyService cartShopifyService;

    @Autowired
    public CheckoutController(CartShopifyService cartShopifyService) {
        this.cartShopifyService = cartShopifyService;
    }

    @PostMapping
    public AbstractRestResponse checkout(@HWSession Session session, @RequestBody List<Long> cartIds) {
        return AbstractRestResponse.apply(cartShopifyService.checkout(session, cartIds));
    }

    @PostMapping("/single")
    public AbstractRestResponse checkoutSingle(@HWSession Session session, CartForm cartForm) {
        return AbstractRestResponse.apply(cartShopifyService.checkoutSingle(session, cartForm));
    }

    @PostMapping("/apply_discount")
    public AbstractRestResponse checkout(@HWSession Session session, String id, String discountCode) {
        return AbstractRestResponse.apply(cartShopifyService.applyDiscountCode(id, discountCode));
    }


}
