package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.form.ForgetPasswordForm;
import com.topdon.website.form.RegisterForm;
import com.topdon.website.services.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/customer")
public class CustomerController extends ControllerSupport {

    private final CustomerService customerService;

    @Autowired
    public CustomerController(CustomerService customerService) {
        this.customerService = customerService;
    }

    @PostMapping("/register")
    public AbstractRestResponse register(RegisterForm form) {
        return AbstractRestResponse.apply(customerService.register(form));
    }

    @PostMapping("/reset_password")
    public AbstractRestResponse forgetPassword(ForgetPasswordForm form) {
        return AbstractRestResponse.apply(customerService.forgetPassword(form));
    }
}
