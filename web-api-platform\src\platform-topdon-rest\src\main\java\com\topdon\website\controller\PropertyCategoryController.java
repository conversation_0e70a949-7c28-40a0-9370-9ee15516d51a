package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.service.PropertyCategoryService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/property/category")
public class PropertyCategoryController {

    @Resource
    private PropertyCategoryService propertyCategoryService;

    @GetMapping("/getList")
    public AbstractRestResponse getList(String classificationCode, @RequestParam(required = false) boolean draft) {
        return AbstractRestResponse.apply(propertyCategoryService.getList(classificationCode, draft));
    }

}