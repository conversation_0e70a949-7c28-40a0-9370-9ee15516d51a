package com.topdon.website.helper;

import org.mountcloud.graphql.request.GraphqlRequest;
import org.mountcloud.graphql.request.query.GraphqlQuery;

public class ShopifyGraphqlQuery extends GraphqlQuery {
    /**
     * 不可见的构造
     *
     * @param requestName query的名字
     */
    public ShopifyGraphqlQuery(String requestName) {
        super(requestName);
    }

    @Override
    public String toString() {
        String superStr = super.toString().replace("\"query\":","");
        superStr = superStr.replaceFirst("\\{","");
        superStr = superStr.substring(0,superStr.length()-1);
        superStr = superStr.substring(0,superStr.length()-1);
        superStr = superStr.replaceFirst("\"","");
        superStr = superStr.replaceAll("\\\\","");
        return superStr;
    }
}
