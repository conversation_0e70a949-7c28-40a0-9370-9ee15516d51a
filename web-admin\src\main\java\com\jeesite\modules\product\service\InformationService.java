package com.jeesite.modules.product.service;

import com.beust.jcommander.internal.Maps;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mapper.JsonMapper;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.product.dao.InformationDao;
import com.jeesite.modules.product.entity.Information;
import com.jeesite.modules.product.entity.InformationApp;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 产品资料管理Service
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Service
@Transactional(readOnly = true)
public class InformationService extends CrudService<InformationDao, Information> {

    @Resource
    private FileUploadService fileUploadService;

    /**
     * 获取单条数据
     *
     * @param information
     * @return
     */
    @Override
    public Information get(Information information) {
        Information entity = super.get(information);
        if (entity != null && entity.getContent() != null) {
            List<Map<String, InformationApp>> list = JsonMapper.fromJson(entity.getOtherParam(), List.class);
            if (list != null && list.size() != 0) {
                List<InformationApp> apps = Lists.newArrayList();
                for (Map<String, InformationApp> map : list) {
                    InformationApp app = JsonMapper.fromJson(JsonMapper.toJson(map.get("obj")), InformationApp.class);
                    apps.add(app);
                }
                entity.setApps(apps);
            }

        }
        return entity;
    }

    /**
     * 查询分页数据
     *
     * @param information 查询条件
     * @return
     */
    @Override
    public Page<Information> findPage(Information information) {
        return super.findPage(information);
    }

    /**
     * 查询列表数据
     *
     * @param information
     * @return
     */
    @Override
    public List<Information> findList(Information information) {
        return super.findList(information);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param information
     */
    @Override
    @Transactional(readOnly = false)
    public void save(Information information) {
        List<Map<String, InformationApp>> apps = Lists.newArrayList();
        for (InformationApp app : information.getApps()) {
            if (DataEntity.STATUS_DELETE.equals(app.getStatus())) {
                continue;
            }
            Map<String, InformationApp> map = Maps.newHashMap();
            if (StringUtils.isEmpty(app.getId())) {
                app.setId(IdGen.nextId());
            }
            // 保存上传图片
            FileUploadUtils.saveFileUpload(app.getId(), "information_app_file", app.getUploadId(), app.getInformation_app_file__del());
            FileUploadUtils.saveFileUpload(app.getId(), "information_app_icon", app.getUploadId(), app.getInformation_app_icon__del());
            map.put("obj", app);
            apps.add(map);

            removeRepeatFile(app.getId(),"information_app_icon");
        }

        String otherParam = JsonMapper.toJson(apps);
        information.setOtherParam(otherParam);
        super.save(information);
        FileUploadUtils.saveFileUpload(information.getId(), "information_cover");
        FileUploadUtils.saveFileUpload(information.getId(), "information_download");
    }

    private void removeRepeatFile(String id, String informationAppIcon) {
        FileUpload t = new FileUpload();
        t.setBizKey(id);
        t.setBizType(informationAppIcon);
        List<FileUpload> list = fileUploadService.findList(t);
        if(list.size()>1){
            list.remove(0);
            for (FileUpload fileUpload : list) {
                fileUploadService.delete(fileUpload);
            }
        }
    }

    /**
     * 更新状态
     *
     * @param information
     */
    @Override
    @Transactional(readOnly = false)
    public void updateStatus(Information information) {
        super.updateStatus(information);
    }

    /**
     * 删除数据
     *
     * @param information
     */
    @Override
    @Transactional(readOnly = false)
    public void delete(Information information) {
        super.delete(information);
    }
}