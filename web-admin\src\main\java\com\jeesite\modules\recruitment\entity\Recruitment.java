package com.jeesite.modules.recruitment.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 招聘管理Entity
 * <AUTHOR>
 * @version 2022-04-20
 */
@Table(name="recruitment", alias="a", label="招聘信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="name", attrName="name", label="岗位名称", queryType=QueryType.LIKE),
		@Column(name="people", attrName="people", label="招聘人数", isQuery=false, isUpdateForce=true),
		@Column(name="position", attrName="position", label="工作地点"),
		@Column(name="create_at", attrName="createDate", label="创建时间",  isUpdate=false, isQuery=false),
		@Column(name="content", attrName="content", label="岗位要求", isQuery=false),
		@Column(name="category", attrName="category", label="岗位类型"),
		@Column(name="type", attrName="type", label="招聘类型"),
	}, orderBy="a.id DESC"
)
public class Recruitment extends DataEntity<Recruitment> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 岗位名称
	private Integer people;		// 招聘人数
	private String position;		// 工作地点
	private String content;		// 岗位要求
	private String category;		// 岗位类型
	private String type;		// 招聘类型
	
	public Recruitment() {
		this(null);
	}

	public Recruitment(String id){
		super(id);
	}
	
	@Size(min=0, max=50, message="岗位名称长度不能超过 50 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public Integer getPeople() {
		return people;
	}

	public void setPeople(Integer people) {
		this.people = people;
	}
	
	@Size(min=0, max=20, message="工作地点长度不能超过 20 个字符")
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	@Size(min=0, max=2048, message="岗位要求长度不能超过 2048 个字符")
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	@Size(min=0, max=30, message="岗位类型长度不能超过 30 个字符")
	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
	
	@Size(min=0, max=20, message="招聘类型长度不能超过 20 个字符")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
}