package com.topdon.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.dto.SellerAuthorizationDTO;
import com.topdon.admin.dto.SellerAuthorizationDetailDTO;
import com.topdon.admin.dto.SellerAuthorizationStatusChangeDTO;
import com.topdon.admin.entity.SellerAuthorization;
import com.topdon.admin.vo.SellerAuthorizationExportVo;
import com.topdon.admin.vo.SellerAuthorizationVo;

import java.util.List;

public interface SellerAuthorizationService extends IService<SellerAuthorization>{


    IPage<SellerAuthorizationVo> getPage(SellerAuthorizationDTO sellerAuthorizationDTO);

    SellerAuthorizationDetailDTO detail(Integer id);

    List<SellerAuthorizationExportVo> exportData(SellerAuthorizationDTO sellerAuthorizationDTO);

    void changeStatus(SellerAuthorizationStatusChangeDTO sellerAuthorizationStatusChangeDTO);

}
