package com.topdon.admin.controller;

import com.topdon.admin.service.PropertyCategoryService;
import com.topdon.admin.vo.PropertyCategoryListVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${adminPath}/property/category")
@Slf4j
public class PropertyCategoryController {

    @Resource
    private PropertyCategoryService propertyCategoryService;

    @GetMapping("/list/{classificationCompareId}")
    public Map<Integer,List<PropertyCategoryListVo>> list(@PathVariable Integer classificationCompareId,
                                                          @RequestParam(required = false, defaultValue = "false") boolean draft) {
        return propertyCategoryService.getList(classificationCompareId, draft);
    }

}
