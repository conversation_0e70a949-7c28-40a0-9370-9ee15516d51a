package com.topdon.website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topdon.website.entity.Information;
import com.topdon.website.form.InformationQueryForm;
import com.topdon.website.vo.InformationGroupVo;
import com.topdon.website.vo.InformationVo;

import java.util.List;

public interface InformationMapper extends BaseMapper<Information> {
    List<InformationGroupVo> getList(InformationQueryForm form);

    List<InformationVo> search(InformationQueryForm form);
}