package com.topdon.admin.convert;

import com.topdon.admin.entity.PropertyCategory;
import com.topdon.admin.vo.PropertyCategoryListVo;
import org.mapstruct.Context;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class PropertyCategoryConvert {

    public abstract PropertyCategoryListVo toPropertyCategoryListVo(PropertyCategory propertyCategory, String parentCategory);

}
