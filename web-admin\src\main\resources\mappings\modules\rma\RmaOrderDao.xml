<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.rma.dao.RmaOrderDao">
	
	<!-- 查询数据
	<select id="findList" resultType="RmaOrder">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="getByRmaOrderId" resultType="com.jeesite.modules.rma.entity.RmaOrderStatusLog">
		select
		status
		,status_modified_time as statusModifiedTime
		from rma_order_status_log
		where rma_order_id= #{rmaOrderId}
		order by status_modified_time desc
	</select>
	
</mapper>