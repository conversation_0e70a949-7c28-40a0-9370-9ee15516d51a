mutation checkoutDiscountCodeApplyV2($checkoutId: ID!, $discountCode: String!) {
    checkoutDiscountCodeApplyV2(checkoutId: $checkoutId, discountCode: $discountCode) {
        checkout {
            id
            paymentDue{
                amount
            }
            requiresShipping
            shippingDiscountAllocations{
                allocatedAmount {
                    amount
                }
            }
            lineItemsSubtotalPrice{
                amount
            }
            subtotalPrice{
                amount
            }
            totalDuties{
                amount
            }
            totalPrice{
                amount
            }
            totalTax{
                amount
            }
            webUrl
        }
        checkoutUserErrors{
            field
            message
        }
    }
}
