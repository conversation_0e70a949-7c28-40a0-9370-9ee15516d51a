package com.hiwie.breeze.cache;

import com.hiwie.breeze.*;
import com.hiwie.breeze.json.Json;
import com.hiwie.breeze.util.*;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;

import java.time.Duration;
import java.util.function.Function;

/**
 * Redeemer
 *
 * <AUTHOR>
 */
public class Redeemer<T> extends AbstractCache {

    private static final ErrorMessage TRIES_LIMIT_REACHED = new ErrorMessage("cache", "redeemer", "tries_limit_reached");
    private static final ErrorMessage NOT_FOUND = new ErrorMessage("cache", "redeemer", "not_found");
    private final int tries;
    private final Class<T> clazz;
    private final String SECRET_KEY = "s";
    private final String ITEM_KEY = "i";
    private final String TRIES = "t";
    private boolean consumable;

    public Redeemer(String prefix, int db, Duration expireDuration, int tries, boolean consumable, Class<T> clazz) {
        super(prefix, db, Some.apply(expireDuration));
        this.tries = tries;
        this.clazz = clazz;
        this.consumable = consumable;
    }

    public Function<Jedis, AbstractEither<ErrorMessage, T>> redeem(String token) {
        return (Jedis jedis) -> CryptographyUtil.getMessageWithoutCheck(token).flatMap(id -> {
            String theKey = generateKey(id);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            Response<String> itemResponse = pipeline.hget(theKey, ITEM_KEY);
            Response<String> secretResponse = pipeline.hget(theKey, SECRET_KEY);
            Response<String> triesResponse = pipeline.hget(theKey, TRIES);
            pipeline.sync();
            if (ObjectUtil.isNull(itemResponse.get()) || ObjectUtil.isNull(secretResponse.get()) || ObjectUtil.isNull(triesResponse.get())) {
                return Left.apply(NOT_FOUND);
            } else {
                if (this.tries > 0) {
                    int dbTries = Integer.parseInt(triesResponse.get());
                    if (tries <= dbTries) {
                        return Left.apply(TRIES_LIMIT_REACHED);
                    }
                }
                return CryptographyUtil.getMessage(token, secretResponse.get()).fold(
                        error -> {
                            Pipeline triesPipeline = jedis.pipelined();
                            triesPipeline.select(db);
                            triesPipeline.hincrBy(theKey, TRIES, 1L);
                            triesPipeline.sync();
                            return Left.apply(error);
                        },
                        message -> {
                            if(this.consumable){
                                jedis.del(theKey);
                            }
                            return Right.apply(Json.readValue(itemResponse.get(), clazz));
                        }
                );
            }
        });
    }

    public Function<Jedis, AbstractEither<ErrorMessage, String>> post(String id, T item) {
        return (Jedis jedis) -> {
            String secret = StringUtil.secureRandom(64, StringUtil.Encoder.HEX);
            String token = CryptographyUtil.signAsToken(id, secret);
            String theKey = generateKey(id);
            Pipeline pipeline = jedis.pipelined();
            pipeline.select(db);
            pipeline.hset(theKey, ITEM_KEY, Json.writeValueAsString(item));
            pipeline.hset(theKey, SECRET_KEY, secret);
            pipeline.hset(theKey, TRIES, "0");
            pipeline.expire(theKey, expireSeconds);
            pipeline.sync();
            return Right.apply(token);
        };
    }

}
