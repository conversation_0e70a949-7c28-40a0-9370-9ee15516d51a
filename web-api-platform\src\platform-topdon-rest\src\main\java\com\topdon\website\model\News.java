package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;

public class News {
    public static final String ENTITY = "NEWS";
    private String id;
    private String name;
    private String content;
    private String cover;
    private String introduction;
    private LocalDateTime createAt;
    private Category category;
    private boolean isMain;
    private LocalDateTime publishAt;

    public enum Category {
        PRODUCT,
        BRAND,
        COMPANY,
        LASTEST
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public boolean isMain() {
        return isMain;
    }

    public void setMain(boolean main) {
        isMain = main;
    }

    public LocalDateTime getPublishAt() {
        return publishAt;
    }

    public void setPublishAt(LocalDateTime publishAt) {
        this.publishAt = publishAt;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY, "NOT_FOUND");
    }
}
