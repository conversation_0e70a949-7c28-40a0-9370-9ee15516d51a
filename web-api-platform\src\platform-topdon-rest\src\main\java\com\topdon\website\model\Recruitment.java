package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;

public class Recruitment {
    public static final String ENTITY = "RECRUITMENT";
    private String id;
    private String name;
    private int people;
    private String position;
    private LocalDateTime createAt;
    private String content;
    private Category category;
    private Type type;

    public enum Type {
        CAMPUS,
        SOCIAL
    }

    public enum Category {
        DESIGNER,
        IT,
        SERVICE,
        SUPPORTING_FUNCTION,
        SALE_MARKETING
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPeople() {
        return people;
    }

    public void setPeople(int people) {
        this.people = people;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY, "NOT_FOUND");
    }
}
