package com.jeesite.modules.news.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.news.entity.News;
import com.jeesite.modules.news.dao.NewsDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 新闻管理Service
 * <AUTHOR>
 * @version 2022-03-03
 */
@Service
@Transactional(readOnly=true)
public class NewsService extends CrudService<NewsDao, News> {
	
	/**
	 * 获取单条数据
	 * @param news
	 * @return
	 */
	@Override
	public News get(News news) {
		return super.get(news);
	}
	
	/**
	 * 查询分页数据
	 * @param news 查询条件
	 * @param news.page 分页对象
	 * @return
	 */
	@Override
	public Page<News> findPage(News news) {
		return super.findPage(news);
	}
	
	/**
	 * 查询列表数据
	 * @param news
	 * @return
	 */
	@Override
	public List<News> findList(News news) {
		return super.findList(news);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param news
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(News news) {
		super.save(news);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(news, news.getId(), "news_cover_image");
	}
	
	/**
	 * 更新状态
	 * @param news
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(News news) {
		super.updateStatus(news);
	}
	
	/**
	 * 删除数据
	 * @param news
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(News news) {
		super.delete(news);
	}
	
}