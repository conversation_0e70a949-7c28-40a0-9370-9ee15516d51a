<% layout('/layouts/default.html', {title: '申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('cooperation:cooperationApply:edit')){ %>
					<a href="${ctx}/cooperation/cooperationApply/form" class="btn btn-default btnTool" title="${text('新增申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i>
					${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${cooperationApply}" action="${ctx}/cooperation/cooperationApply/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('申请类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="type" dictType="apply_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('公司名称')}：</label>
					<div class="control-inline">
						<#form:input path="companyName" maxlength="40" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	$('#btnExport').click(function () {
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/cooperation/cooperationApply/exportData',
			downloadFile: true
		});
	});
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("公司名称")}', name:'companyName', index:'a.company_name', width:150, align:"left",frozen:true,formatter: function(val, obj, row, act){
				return '<a href="${ctx}/cooperation/cooperationApply/form?id='+row.id+'" class="btnList" data-title="${text("编辑申请")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("申请类型")}', name:'type', index:'a.type', width:250, align:"center",formatter: function(val, obj, row, act){
                return js.getDictLabel(${@DictUtils.getDictListJson('apply_type')}, val, '${text("未知")}', true);
            }},
		{header:'${text("电子邮箱")}', name:'email', index:'a.email', width:300, align:"left"},
		{header:'${text("手机号码")}', name:'phoneNumber', index:'a.phone_number', width:300, align:"left"},
		{header:'${text("名")}', name:'firstName', index:'a.first_name', width:100, align:"left"},
		{header:'${text("姓")}', name:'lastName', index:'a.last_name', width:100, align:"left"},
		{header:'${text("公司大小")}', name:'companySize', index:'a.company_size', width:150, align:"center"},
		{header:'${text("官网链接")}', name:'websiteUrl', index:'a.website_url', width:350, align:"left"},
		{header:'${text("区域")}', name:'region.name', index:'a.country', width:150, align:"left"},
		{header:'${text("公司地址")}', name:'address', index:'a.address', width:450, align:"left"},
		{header:'${text("品牌")}', name:'brand', index:'a.brand', width:150, align:"left"},
		{header:'${text("备注说明")}', name:'message', index:'a.message', width:150, align:"left"},
		{header:'${text("申请时间")}', name:'createDate', index:'a.create_at', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('cooperation:cooperationApply:edit')){ %>
				actions.push('<a href="${ctx}/cooperation/cooperationApply/form?id='+row.id+'" class="btnList" title="${text("编辑申请")}"><i class="fa fa-pencil"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	// 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
</script>