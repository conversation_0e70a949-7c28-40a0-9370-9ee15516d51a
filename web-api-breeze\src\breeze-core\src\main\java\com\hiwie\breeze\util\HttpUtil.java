package com.hiwie.breeze.util;

import com.google.common.net.HttpHeaders;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.BreezeConstants;
import com.hiwie.breeze.None;
import com.hiwie.breeze.Some;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    private static final String UNKNOWN = "unknown";

    private static final String UA_NATIVE = "native";

    private static final String UA_UNIX = "x11";

    private static final String UA_IE = "msie";

    private static final Pattern OS_WINDOWS_VERSION_PATTERN = Pattern.compile("\\(windows nt ([\\d.]+)[);]");

    private static final Pattern OS_MAC_VERSION_PATTERN = Pattern.compile("\\bmac os x ([^;)]+)[;)]");

    private static final Pattern OS_IPHONE_VERSION_PATTERN = Pattern.compile("\\biphone os ([\\d_]+)");

    private static final Pattern OS_ANDROID_VERSION_PATTERN = Pattern.compile("\\bandroid ([\\d.]+)");

    private static final Pattern OS_UNIX_VERSION_PATTERN = Pattern.compile("\\(x11; ([^)]+)\\)");

    private static final Pattern CLIENT_EDGE_VERSION_PATTERN = Pattern.compile("\\bedge/([\\d.]+)");

    private static final Pattern CLIENT_IE_VERSION_PATTERN = Pattern.compile("\\bmsie ([\\d.]+)");

    private static final Pattern CLIENT_SAFARI_VERSION_PATTERN = Pattern.compile("\\bsafari/([\\d.]+)");

    private static final Pattern CLIENT_OPERA_VERSION_PATTERN = Pattern.compile("\\b(version/|opera )([\\d.]+)");

    private static final Pattern CLIENT_CHROME_VERSION_PATTERN = Pattern.compile("\\bchrome/([\\d.]+)");

    private static final Pattern CLIENT_FIREFOX_VERSION_PATTERN = Pattern.compile("\\bfirefox/([\\d.]+)");

    private static final Pattern CLIENT_NETSCAPE_VERSION_PATTERN = Pattern.compile("\\b(netscape|navigator)/([\\d.]+)");

    private static final String[] IP_HEADER_CANDIDATES = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR"
    };

    private HttpUtil() {
    }

    public static AbstractOption<String> getCookie(HttpServletRequest request, String name) {
        if (Objects.isNull(request.getCookies())) {
            return None.apply();
        } else {
            for (Cookie cookie : request.getCookies()) {
                if (name.equals(cookie.getName())) {
                    return Some.apply(cookie.getValue());
                }
            }
            return None.apply();
        }
    }

    private static boolean isValidClientIP(String ip) {
        return !"127.0.0.1".equalsIgnoreCase(ip) && "unknown".equalsIgnoreCase(ip) && ip.matches("^[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}$");
    }


    public static String getClientIP(HttpServletRequest request) {
        String ip;
        for (String header : IP_HEADER_CANDIDATES) {
            ip = request.getHeader(header);
            if (StringUtil.isNotEmpty(ip)) {
                if (ip.contains(",")) {
                    String[] proxyIPs = ip.split(",");
                    for (String proxyIP : proxyIPs) {
                        ip = proxyIP.trim();
                        if (isValidClientIP(ip)) {
                            return ip;
                        }
                    }
                } else {
                    ip = ip.trim();
                    if (isValidClientIP(ip)) {
                        return ip;
                    }
                }
            }
        }
        return request.getRemoteAddr();
    }

    /**
     * 获取HTTP信息
     * <p>
     * Firefox 60+ : Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0
     * Wechat : Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E302 MicroMessenger/6.6.7 NetType/WIFI Language/en
     */
    public static UAInfo getInfo(HttpServletRequest request) {
        String ua = request.getHeader(HttpHeaders.USER_AGENT);
        if (Objects.isNull(ua)) {
            return null;
        } else {
            UAInfo uaInfo = new UAInfo();
            ua = ua.toLowerCase();
            if (ua.startsWith(UA_NATIVE)) {
                String[] nativeInfo = ua.trim().split("-");
                uaInfo.setSystem(nativeInfo.length > 1 ? nativeInfo[1] : UNKNOWN);
                uaInfo.setClientVersion(nativeInfo.length > 2 ? nativeInfo[2] : UNKNOWN);
                uaInfo.setClient(nativeInfo.length > 3 ? nativeInfo[2] : UNKNOWN);
                uaInfo.setClientVersion(nativeInfo.length > 4 ? nativeInfo[2] : UNKNOWN);
                return uaInfo;
            } else {
                Matcher matcher;
                if (ua.contains(BreezeConstants.OS_WINDOWS)) {
                    uaInfo.setSystem(BreezeConstants.OS_WINDOWS);
                    matcher = OS_WINDOWS_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.OS_IPHONE)) {
                    uaInfo.setSystem(BreezeConstants.OS_IPHONE);
                    matcher = OS_IPHONE_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.OS_MAC)) {
                    uaInfo.setSystem(BreezeConstants.OS_MAC);
                    matcher = OS_MAC_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(UA_UNIX)) {
                    uaInfo.setSystem(BreezeConstants.OS_UNIX);
                    matcher = OS_UNIX_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.OS_ANDROID)) {
                    uaInfo.setSystem(BreezeConstants.OS_ANDROID);
                    matcher = OS_ANDROID_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else {
                    LOGGER.warn("unknown http info detected UA = {0}", ua);
                    uaInfo.setSystem(UNKNOWN);
                    uaInfo.setSystemVersion(UNKNOWN);
                }
                if (Objects.isNull(uaInfo.systemVersion)) {
                    LOGGER.warn("unknown os version UA = {0}", ua);
                    uaInfo.setSystemVersion(UNKNOWN);
                }

                if (ua.contains(BreezeConstants.CLIENT_EDGE)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_EDGE);
                    matcher = CLIENT_EDGE_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(UA_IE)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_IE);
                    matcher = CLIENT_IE_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.CLIENT_SAFARI)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_SAFARI);
                    matcher = CLIENT_SAFARI_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.CLIENT_OPERA)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_OPERA);
                    matcher = CLIENT_OPERA_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(2));
                    }
                } else if (ua.contains(BreezeConstants.CLIENT_CHROME)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_CHROME);
                    matcher = CLIENT_CHROME_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else if (ua.contains(BreezeConstants.CLIENT_NETSCAPE)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_NETSCAPE);
                    matcher = CLIENT_NETSCAPE_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(2));
                    }
                } else if (ua.contains(BreezeConstants.CLIENT_FIREFOX)) {
                    uaInfo.setClient(BreezeConstants.CLIENT_FIREFOX);
                    matcher = CLIENT_FIREFOX_VERSION_PATTERN.matcher(ua);
                    if (matcher.find()) {
                        uaInfo.setSystemVersion(matcher.group(1));
                    }
                } else {
                    LOGGER.warn(ua);
                    uaInfo.setClient(UNKNOWN);
                    uaInfo.setClientVersion(UNKNOWN);
                }
                if (Objects.isNull(uaInfo.clientVersion)) {
                    LOGGER.warn("unknown client version UA = {0}", ua);
                    uaInfo.setClientVersion(UNKNOWN);
                }
                return uaInfo;
            }
        }
    }

    public static class UAInfo {

        private String system;

        private String systemVersion;

        private String client;

        private String clientVersion;

        public String getSystem() {
            return system;
        }

        public void setSystem(String system) {
            this.system = system;
        }

        public String getSystemVersion() {
            return systemVersion;
        }

        public void setSystemVersion(String systemVersion) {
            this.systemVersion = systemVersion;
        }

        public String getClient() {
            return client;
        }

        public void setClient(String client) {
            this.client = client;
        }

        public String getClientVersion() {
            return clientVersion;
        }

        public void setClientVersion(String clientVersion) {
            this.clientVersion = clientVersion;
        }

        @Override
        public String toString() {
            return StringUtils.join(new String[]{system, systemVersion, client, clientVersion}, "-");
        }

    }

}