package com.topdon.admin.vo;

import com.topdon.admin.entity.ProductProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class ProductPropertyVo {
    private String productName;
    private String productId;

    private List<ProductPropertyVoInner> productProperties;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ProductPropertyVoInner extends ProductProperty{
        private String category;
    }
}
