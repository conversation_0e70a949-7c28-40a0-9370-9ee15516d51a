package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类比较
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "classification_compare")
public class ClassificationCompare {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分类编码
     */
    @TableField(value = "classification_code")
    private String classificationCode;

    /**
     * 是否草稿
     */
    @TableField(value = "draft")
    private Boolean draft;

    /**
     * 发布时间
     */
    @TableField(value = "release_date")
    private Date releaseDate;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "update_date")
    private Date updateDate;
}