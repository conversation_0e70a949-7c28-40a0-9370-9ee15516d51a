package com.hiwie.breeze;


/**
 * <AUTHOR>
 */
public class None<T> extends AbstractOption<T> {

    private static None<?> INSTANCE = new None<>();

    private None(){
    }

    public static <T> None<T> apply(){
        @SuppressWarnings("unchecked")
        None<T> t = (None<T>) INSTANCE;
        return t;
    }

    @Override
    public T get() {
        return null;
    }

    @Override
    public boolean isEmpty() {
        return true;
    }

}
