package com.topdon.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.topdon.admin.entity.SellerAuthProductLinesMapping;
import com.topdon.admin.entity.SellerAuthorization;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SellerAuthorizationVo extends SellerAuthorization {

    private String authPlatform;
    private String storeName;
    private String storeLink;

    private String productLineName;
    private String region;

    private List<SellerAuthProductLinesMapping> sellerAuthProductLinesMappings;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Override
    public Date getAuthStartDate() {
        return super.getAuthStartDate();
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Override
    public Date getAuthEndDate() {
        return super.getAuthEndDate();
    }
}
