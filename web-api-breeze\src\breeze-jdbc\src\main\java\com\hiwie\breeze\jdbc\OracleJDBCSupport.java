package com.hiwie.breeze.jdbc;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.None;
import com.hiwie.breeze.Some;
import com.hiwie.breeze.repository.PaginationForm;
import com.hiwie.breeze.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OracleJDBCSupport extends AbstractJDBCSupport {

    protected OracleJDBCSupport(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    private String paginate(String sql) {
        @Language("SQL") String wrapped = "SELECT * FROM (SELECT ROWNUM AS RN_, R_.* FROM(" + sql + ") R_ WHERE ROWNUM <= :DROP_ + :TAKE_ ) ALIAS WHERE ALIAS.RN_ > :DROP_";
        return wrapped;
    }

    protected <T> List<T> paged(@Language("SQL") String sql, RowMapper<T> mapper, MapSqlParameterSource params, PaginationForm form) {
        params.addValue("DROP_", form.getDrop());
        params.addValue("TAKE_", form.getTake());
        return list(paginate(sql), mapper, params);
    }

    protected <T> List<T> paged(@Language("SQL") String sql, RowMapper<T> mapper, PaginationForm form) {
        return list(paginate(sql), mapper, new MapSqlParameterSource());
    }

    protected <T> List<T> paged(@Language("SQL") String sql, ResultSetExtractor<List<T>> rse, MapSqlParameterSource params, PaginationForm form) {
        params.addValue("DROP_", form.getDrop());
        params.addValue("TAKE_", form.getTake());
        return list(paginate(sql), rse, params);
    }

    protected <T> List<T> paged(@Language("SQL") String sql, ResultSetExtractor<List<T>> rse, PaginationForm form) {
        return paged(sql, rse, new MapSqlParameterSource(), form);
    }

    @Override
    protected Number autoIncreaseInsert(@Language("SQL") String sql, MapSqlParameterSource params) {
        return autoIncreaseInsert(sql, params, "ID");
    }

    private <T> String nullable(AbstractOption<String> aliasOption, @Language("SQL") String column, AbstractOption<T> valueOption, MapSqlParameterSource params) {
        String aliasedColumn = aliasOption.map(alias -> StringUtil.join(alias, ".", column)).getOrElse(column);
        return valueOption.map(value -> {
            params.addValue(column, value);
            return StringUtils.join(" AND ", aliasedColumn, " = :", column);
        }).getOrElse(() ->
                StringUtils.join(" AND ", aliasedColumn, " IS NULL ")
        );
    }

    protected <T> String nullable(@Language("SQL") String alias, @Language("SQL") String column, AbstractOption<T> valueOption, MapSqlParameterSource params) {
        return nullable(Some.apply(alias), column, valueOption, params);
    }

    protected <T> String nullable(@Language("SQL") String column, AbstractOption<T> valueOption, MapSqlParameterSource params) {
        return nullable(None.apply(), column, valueOption, params);
    }

}
