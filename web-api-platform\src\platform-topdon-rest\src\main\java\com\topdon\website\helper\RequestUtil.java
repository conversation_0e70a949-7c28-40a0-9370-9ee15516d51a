package com.topdon.website.helper;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.hiwie.breeze.*;
import com.hiwie.breeze.json.Json;
import com.hiwie.breeze.util.StringUtil;
import com.hiwie.security.models.User;
import com.topdon.website.SCConstants;
import com.topdon.website.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.topdon.website.SCConstants.CONTENT_TYPE_TEXT_JSON;

@Component
@Slf4j
public class RequestUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestUtil.class);


    private static String topdonUrl;
    private static String lenkorInnerApiUrl;
    private static String lenkorInnerApiClientId;
    private static String lenkorInnerApiClientSecret;

    @Value("${topdon.url}")
    public void setTopdonUrl(final String topdonUrl) {
        RequestUtil.topdonUrl = topdonUrl;
    }

    @Value("${lenkor.innerApi.url}")
    public void setLenkorInnerApiUrl(String lenkorInnerApiUrl) {
        RequestUtil.lenkorInnerApiUrl = lenkorInnerApiUrl;
    }

    @Value("${lenkor.innerApi.clientId}")
    public void setLenkorInnerApiClientId(String lenkorInnerApiClientId) {
        RequestUtil.lenkorInnerApiClientId = lenkorInnerApiClientId;
    }

    @Value("${lenkor.innerApi.clientSecret}")
    public void setLenkorInnerApiClientSecret(String lenkorInnerApiClientSecret) {
        RequestUtil.lenkorInnerApiClientSecret = lenkorInnerApiClientSecret;
    }

    public static AbstractEither<ErrorMessage, TopdonSecret> signin(String username, String password, String app) {
        String appKey = "6DCC55A836A246BEBAD583EBBA995ED1";
        String appSecret = "DAC450E89BEB43B5B02CD6BE82B8C76E";
        if (!"global".equalsIgnoreCase(app)) {
            if ("EVC".equalsIgnoreCase(app)) {
                appKey = "2CAFB79AD7534A188600006FC231B41F";
                appSecret = "736532369D6B4CC1A807F55E813040DE";
            } else if ("AU".equalsIgnoreCase(app)) {
                appKey = "6E9A31EE4E0E4717B00EA1B3243B1A0C";
                appSecret = "994EC72F491845A490CA84EE93A58D51";
            } else {
                appKey = "8C1F200ADC6D4A2E9EA0B8E13582CAB8";
                appSecret = "EA18BDA4EDED423D8B66C648D735B136";
            }
        }

        String nonceStr = SignUtil.randomString(10);
        long timestamp = new Date().getTime();
        String signTemp = null;
        try {
            signTemp = "appkey=" + appKey + "&grant_type=password&nonceStr=" + nonceStr + "&password=" + URLEncoder.encode(password, "UTF-8") + "&scope=server&timestamp=" + timestamp + "&username=" + username;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        String hash = DigestUtils.md5Hex(signTemp + "&appSecret=" + appSecret).toUpperCase();
        String uri = "/api/v1/auth/oauth/token?" + signTemp + "&sign=" + hash;

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Basic bGVua29yOmxlbmtvcg==");
        headers.put("languageId", "7");
        return post(uri, None.apply(), Some.apply(headers)).fold(
                Left::apply,
                dataString -> {
                    try {
                        LOGGER.info("signin api call back:" + dataString);
                        ObjectMapper jsonMapper = Json.MAPPER;
                        jsonMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
                        Map<String, Object> topdonResponse = jsonMapper.readValue(dataString, Map.class);
                        String code = (String) topdonResponse.get("code");
                        if (StringUtil.isNotEmpty(code)) {
                            if ("60002".equals(code)) {
                                return Left.apply(User.Errors.INCORRECT_ACCOUNT_OR_PASSWORD);
                            } else if ("60001".equals(code)) {
                                return Left.apply(User.Errors.NOT_FOUND);
                            } else {
                                return Left.apply(SCConstants.API_ERROR);
                            }
                        }
                        return Right.apply(Json.MAPPER.readValue(dataString, TopdonSecret.class));
                    } catch (IOException e) {
                        e.printStackTrace();
                        return Left.apply(SCConstants.API_ERROR);
                    }
                }
        );

    }

    public static AbstractEither<ErrorMessage, TopdonQuickLogin> quickLogin(String code, String email, String app) {
        String appKey = "6DCC55A836A246BEBAD583EBBA995ED1";
        String appSecret = "DAC450E89BEB43B5B02CD6BE82B8C76E";
        if (!"global".equalsIgnoreCase(app)) {
            if ("EVC".equalsIgnoreCase(app)) {
                appKey = "2CAFB79AD7534A188600006FC231B41F";
                appSecret = "736532369D6B4CC1A807F55E813040DE";
            } else if ("AU".equalsIgnoreCase(app)) {
                appKey = "6E9A31EE4E0E4717B00EA1B3243B1A0C";
                appSecret = "994EC72F491845A490CA84EE93A58D51";
            } else {
                appKey = "8C1F200ADC6D4A2E9EA0B8E13582CAB8";
                appSecret = "EA18BDA4EDED423D8B66C648D735B136";
            }
        }

        String nonceStr = SignUtil.randomString(10);
        long timestamp = new Date().getTime();
        String signTemp = "appkey=" + appKey + "&grant_type=password&nonceStr=" + nonceStr + "&scope=server&timestamp=" + timestamp;

        String hash = DigestUtils.md5Hex(signTemp + "&appSecret=" + appSecret).toUpperCase();
        String uri = "/api/v1/user/platUserPro/quickLogin?" + signTemp + "&sign=" + hash;

        Map<String, Object> params = Maps.newHashMap();
        params.put("code", code);
        params.put("type", 4);
        params.put("email", email);

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Basic bGVua29yOmxlbmtvcg==");
        headers.put("languageId", "7");
        return post(uri, Some.apply(params), Some.apply(headers)).fold(
                Left::apply,
                dataString -> {
                    try {
                        LOGGER.info("quickLogin api call back:" + dataString);
                        ObjectMapper jsonMapper = Json.MAPPER;
                        jsonMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
                        Map<String, Object> topdonResponse = jsonMapper.readValue(dataString, Map.class);
                        boolean success = (boolean) topdonResponse.get("success");
                        if (!success) {
                            return Left.apply(Optional.ofNullable(TopdonQuickLogin.Errors.MESSAGE_MAP.get((Integer) topdonResponse.get("code"))).orElse(SystemError.UNKNOWN_EXCEPTION));
                        }

                        return Right.apply(Json.MAPPER.readValue(Json.writeValueAsString(topdonResponse.get("data")), TopdonQuickLogin.class));
                    } catch (IOException e) {
                        e.printStackTrace();
                        return Left.apply(SCConstants.API_ERROR);
                    }
                }
        );

    }

    public static void main(String[] args) {
        AbstractEither<ErrorMessage, TopdonQuickLogin> response = quickLogin("<EMAIL>", "123123", "global");
        System.out.println(response);
    }

    private static AbstractEither<ErrorMessage, TopdonResponse> get(AbstractOption<String> domain, String uri, AbstractOption<Map<String, Object>> param, AbstractOption<Map<String, String>> headers) {
        String finalUri = uri;
        uri = domain.map(domain1 -> domain1 + finalUri).getOrElse("https://api.topdon.com" + uri);
        if (param.isDefined()) {
            uri = new BuildReqUrl(uri).set(param.get()).toString();
        }
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet get = new HttpGet(uri);
        get.addHeader("clientType", "3");
        get.addHeader("Content-Type", "application/json;charset=UTF-8");
        headers.map(header -> {
            for (String key : header.keySet()) {
                get.addHeader(key, header.get(key));
            }
            return header;
        });

        CloseableHttpResponse response = null;
        try {

            response = httpClient.execute(get);
            HttpEntity entity = response.getEntity();
            TopdonResponse topdonResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            return Right.apply(topdonResponse);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Left.apply(SCConstants.API_ERROR);
    }

    public static AbstractEither<ErrorMessage, TopdonResponse> getWithSign(String uri, Map<String, Object> param) {
        return get(Some.apply(RequestUtil.lenkorInnerApiUrl), uri, Some.apply(param), Some.apply(getAuthHeaders()));
    }

    public static AbstractEither<ErrorMessage, String> post(String uri, AbstractOption<Map<String, Object>> param, AbstractOption<Map<String, String>> headers) {
        return post(None.apply(), uri, param, headers, true);
    }

    private static AbstractEither<ErrorMessage, String> post(AbstractOption<String> domain, String uri, AbstractOption<Map<String, Object>> param, AbstractOption<Map<String, String>> headers, boolean isSign) {
        String finalUri = uri;
        uri = domain.map(domain1 -> domain1 + finalUri).getOrElse(topdonUrl + uri);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        if (!isSign) {
            uri = signParam(uri, Maps.newHashMap());
        }


        HttpPost post = new HttpPost(uri);
        post.addHeader("clientType", "3");
        post.addHeader("Content-Type", "application/json;charset=UTF-8");
        headers.map(header -> {
            for (String key : header.keySet()) {
                post.addHeader(key, header.get(key));
            }
            return header;
        });

        CloseableHttpResponse response = null;
        try {
            param.map(map -> {
                try {
                    StringEntity se = new StringEntity(Json.writeValueAsString(map));
                    se.setContentType(CONTENT_TYPE_TEXT_JSON);
                    post.setEntity(se);
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                return map;
            });

            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            InputStream stream = entity.getContent();
            String dataString = new BufferedReader(new InputStreamReader(stream)).lines().collect(Collectors.joining(System.lineSeparator()));
            log.info("Request URL:{} ,request:{} ,response:{}",finalUri,param,dataString);
//            TopdonResponse topdonResponse = Json.MAPPER.readValue(entity.getContent(), TopdonResponse.class);
            return Right.apply(dataString);
        } catch (IOException e) {
            LOGGER.error("request error", e);
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Left.apply(SCConstants.API_ERROR);
    }

    public static AbstractEither<ErrorMessage, TopdonResponse> postWithSign(String uri, Map<String, Object> param) {
        return post(Some.apply(RequestUtil.lenkorInnerApiUrl), uri, Some.apply(param), Some.apply(getAuthHeaders()), false).fold(
                Left::apply,
                dataString -> {
                    try {
                        TopdonResponse topdonResponse = Json.MAPPER.readValue(dataString, TopdonResponse.class);
                        return Right.apply(topdonResponse);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return Left.apply(SCConstants.API_ERROR);
                }
        );
    }

    public static AbstractEither<ErrorMessage, LogisticsInfoApiVo> postWithLogisticsInfo(String uri, Map<String, Object> param) {
        return post(Some.apply(RequestUtil.lenkorInnerApiUrl), uri, Some.apply(param), Some.apply(getAuthHeaders()), false).fold(
                Left::apply,
                dataString -> {
                    try {
                        LogisticsInfoApiVo logisticsInfoApiVo = Json.MAPPER.readValue(dataString, LogisticsInfoApiVo.class);
                        return Right.apply(logisticsInfoApiVo);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return Left.apply(SCConstants.API_ERROR);
                }
        );
    }

    public static AbstractEither<ErrorMessage, TopdonResponse> postTopdonWithSign(String uri, Map<String, Object> param) {
        return post(Some.apply("http://api.topdon.top:8022"), uri, Some.apply(param), None.apply(), false).fold(
                Left::apply,
                dataString -> {
                    try {
                        TopdonResponse topdonResponse = Json.MAPPER.readValue(dataString, TopdonResponse.class);
                        return Right.apply(topdonResponse);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return Left.apply(SCConstants.API_ERROR);
                }
        );
    }


    public static Map<String, String> getAuthHeaders() {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("In-client-id", RequestUtil.lenkorInnerApiClientId);

        long timestamp = System.currentTimeMillis() / 1000;
        String stamp = timestamp + "-" + SignUtil.randomString(5);
        headers.put("In-stamp", stamp);
        headers.put("In-sign", DigestUtils.md5Hex(RequestUtil.lenkorInnerApiClientSecret + stamp));
        return headers;
    }

    public static String signParam(String uri, Map<String, Object> param) {
        long timestamp = new Date().getTime();
        String nonceStr = SignUtil.randomString(10);
        param.put("appkey", "6DCC55A836A246BEBAD583EBBA995ED1");
        param.put("grant_type", "web-password");
        param.put("scope", "server");
        param.put("timestamp", timestamp);
        param.put("nonceStr", nonceStr);
        String signTemp = SignUtil.sortAscii(param);
        String hash = DigestUtils.md5Hex(signTemp + "&appSecret=DAC450E89BEB43B5B02CD6BE82B8C76E").toUpperCase();
        return uri + "?" + signTemp + "&sign=" + hash;
    }

    public static AbstractEither<ErrorMessage, String> postBy(String url, Optional<Map<String, Object>> param, Optional<Map<String, String>> headers) {
        String finalUri = url;
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        HttpPost post = new HttpPost(url);
        headers.ifPresent(h -> h.entrySet().forEach(e -> {
            post.addHeader(e.getKey(), e.getValue());
        }));

        CloseableHttpResponse response = null;
        try {
            param.ifPresent(map -> {
                try {
                    StringEntity se = new StringEntity(Json.writeValueAsString(map));
                    se.setContentType(CONTENT_TYPE_TEXT_JSON);
                    post.setEntity(se);
                } catch (UnsupportedEncodingException e) {
                    log.error("post " + url + " error!", e);
                }
            });

            response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();
            InputStream stream = entity.getContent();
            String dataString = new BufferedReader(new InputStreamReader(stream)).lines().collect(Collectors.joining(System.lineSeparator()));
            return Right.apply(dataString);
        } catch (IOException e) {
            LOGGER.error("request error", e);
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                log.error("post " + url + " close connect error!", e);
            }
        }
        return Left.apply(SCConstants.API_ERROR);
    }
}
