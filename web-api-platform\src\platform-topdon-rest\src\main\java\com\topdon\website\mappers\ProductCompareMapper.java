package com.topdon.website.mappers;

import com.google.common.collect.Maps;
import com.hiwie.breeze.json.Json;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.model.Product;
import org.springframework.jdbc.core.RowMapper;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ProductCompareMapper {

    public static final RowMapper<Map<String, Object>> DETAIL = (rs, index) -> {
        Map<String, Object> detail = Maps.newHashMap();
        detail.put("id", rs.getString("id"));
        detail.put("product", Product.apply(rs.getString("product_id"), rs.getString("product_name")));
        detail.put("type", rs.getString("type"));
        detail.put("name", rs.getString("name"));
        detail.put("image", rs.getString("image"));
        String param = rs.getString("param");
        Map<String, String> params = Json.readValue(param, Map.class);
        detail.put("param", params);
        return detail;
    };

}
