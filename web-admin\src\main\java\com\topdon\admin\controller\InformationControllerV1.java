package com.topdon.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.jeesite.common.entity.Page;
import com.topdon.admin.service.InformationServiceV1;
import com.topdon.admin.vo.InformationVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "${adminPath}/v1/product/information")
public class InformationControllerV1 {

    @Resource
    private InformationServiceV1 informationServiceV1;

    /**
     * 查询列表数据
     */
    @RequiresPermissions("product:information:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<InformationVo> listData(InformationVo information, HttpServletRequest request, HttpServletResponse response) {
        PageDTO<InformationVo> page = informationServiceV1.getPage(information, new Page<>(request, response));
        return new Page<>((int) page.getCurrent(), (int) page.getSize(), page.getTotal(), page.getRecords());
    }
}
