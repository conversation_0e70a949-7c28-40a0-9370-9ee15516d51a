package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;
import java.util.Map;

public class Cart {

    private static final String ENTITY_NAME = "CART";
    private long id;
    private String userId;
    private String sn;

    private String snName;
    private long productId;
    private String shopifyProductId;
    private long skuId;
    private String shopifyVariantId;
    private int count;
    private double price;

    private double msrpPrice;
    private LocalDateTime createAt;
    private Object detail;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public long getProductId() {
        return productId;
    }

    public void setProductId(long productId) {
        this.productId = productId;
    }

    public String getShopifyProductId() {
        return shopifyProductId;
    }

    public String getShopifyHQLProductId() {
        return "gid://shopify/Product/" + shopifyProductId;
    }

    public void setShopifyProductId(String shopifyProductId) {
        this.shopifyProductId = shopifyProductId;
    }

    public long getSkuId() {
        return skuId;
    }

    public void setSkuId(long skuId) {
        this.skuId = skuId;
    }

    public String getShopifyVariantId() {
        return shopifyVariantId;
    }

    public String getShopifyHQLVariantId() {
        return "gid://shopify/ProductVariant/" + shopifyVariantId;
    }

    public void setShopifyVariantId(String shopifyVariantId) {
        this.shopifyVariantId = shopifyVariantId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public Object getDetail() {
        return detail;
    }

    public void setDetail(Object detail) {
        this.detail = detail;
    }

    public String getSnName() {
        return snName;
    }

    public void setSnName(String snName) {
        this.snName = snName;
    }

    public double getMsrpPrice() {
        return msrpPrice;
    }

    public void setMsrpPrice(double msrpPrice) {
        this.msrpPrice = msrpPrice;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "NOT_FOUND");
        public static final ErrorMessage EXISTS = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "EXISTS");
        public static final ErrorMessage SHOPIFY_ERROR = new ErrorMessage(SCConstants.MODULE, ENTITY_NAME, "SHOPIFY_ERROR");
    }
}
