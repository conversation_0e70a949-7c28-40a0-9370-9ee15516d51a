package com.topdon.website.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.model.Classification;
import com.topdon.website.repositories.ClassificationRepository;
import com.topdon.website.vo.PropertyCategoryTreeVo;
import com.topdon.website.vo.PropertyCategoryVo;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.website.entity.PropertyCategory;
import com.topdon.website.mapper.PropertyCategoryMapper;
import com.topdon.website.service.PropertyCategoryService;

import javax.annotation.Resource;

@Service
public class PropertyCategoryServiceImpl extends ServiceImpl<PropertyCategoryMapper, PropertyCategory> implements PropertyCategoryService {

    @Resource
    private ClassificationRepository classificationRepository;

    @Override
    public AbstractEither<ErrorMessage, PropertyCategoryTreeVo> getList(String classificationCode, boolean draft) {
        List<PropertyCategory> list = baseMapper.getList(classificationCode, draft);

        AbstractOption<Classification> classificationAbstractOption = classificationRepository.get(classificationCode);
        Classification classification = classificationAbstractOption.get();

        List<TreeNode<Integer>> treeNodeList = list.stream()
                .map(item -> new TreeNode<>(item.getId(), item.getParentId(), item.getCategory(), item.getSort()))
                .collect(Collectors.toList());

        return Right.apply(new PropertyCategoryTreeVo(TreeUtil.build(treeNodeList, 0),
                Optional.ofNullable(classification).map(Classification::getName).orElse("")));
    }
}
