<% layout('/layouts/default.html', {title: '产品资料管理', libs: ['validate','dataGrid','fileupload','ueditor']}){ %>

<link rel="stylesheet" href="/admin/static/element-ui/index.css">
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/element-ui/index.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<script src="/admin/static/common/vue.js"></script>

<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(information.isNewRecord ? '新增产品资料' : '编辑产品资料')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${information}" action="${ctx}/product/information/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden path="oldId"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('产品')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="product" title="产品选择" path="product.id" labelPath="product.name"
								url="${ctx}/product/product/select" allowClear="false"
								checkbox="false" class="required" itemCode="id" itemName="name"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								${text('回答内容')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:ueditor path="content" maxlength="10000" height="200" simpleToolbars="false" readonly="false" class="" outline="false"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('显示封面')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadMedia" bizKey="${information.id}" bizType="information_cover"
								uploadType="image"  class="" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="mediaUrl"/>
								<#form:hidden path="mediaUrl"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('下载地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadDownload" bizKey="${information.id}" bizType="information_download" uploadType="all"
								class="" maxUploadNum="1" preview="true" returnPath="true" filePathInputId="downloadUrl"/>
								<#form:input path="downloadUrl" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
                    <div id="informationFormMainContent" class="hidden">
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="control-label col-sm-4" title="">
                                    <span class="required">*</span> ${text('资料类型')}：<i
                                        class="fa icon-question hide"></i></label>
                                <div class="col-sm-8">
                                    <#form:select path="type" dictType="infomation_type" blankOption="true"
                                    class="form-control required" />
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="control-label col-sm-4" title="">
                                    <span class="required">*</span> ${text('分组标题')}：<i
                                        class="fa icon-question hide"></i></label>
                                <div class="col-sm-8">
                                    <el-cascader
                                            v-model="informationGroupId"
                                            style="width: 100%"
                                            :options="informationGroupTreeDataFilterProductName"
                                            :props="{ checkStrictly: true ,value:'id',label:'name',emitPath:false}"
                                            clearable>
                                    </el-cascader>
                                    <#form:input path="informationGroupId" maxlength="40" class="form-control required hide"/>
                                    <#form:input path="groupBy" maxlength="40" class="form-control required hide"/>
                                </div>

                            </div>
						</div>
					</div>

					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('排序')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sort" maxlength="20" class="form-control digits required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('文件类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="fileType" dictType="file_type" blankOption="true" class="form-control" />
							</div>
						</div>
					</div>
				</div>

				<h4 class="form-unit">${text('软件下载参数')}</h4>
				<div class="ml10 mr10 table-form">
					<table id="informationDataGrid"></table>
					<% if (hasPermi('product:information:edit')){ %>
					<a href="#" id="informationDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
					<% } %>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('product:information:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<script>
    var ctx = '${ctx}'
</script>
<% } %>
<script>


	//初始化测试数据子表DataGrid对象
	$("#informationDataGrid").dataGrid({
		data: ${toJson(information.apps)},
		datatype: "local", // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度
		// 设置数据表格列
		columnModel: [
			{header:'${text("操作")}', name:'actions', width:40, align:"center", formatter: function(val, obj, row, act){
					var actions = [];
					if (val == 'new'){
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#informationDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
					}else{
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#informationDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}},
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'文件地址', name:'download', editable:true, hidden:false},
			{header:'图标地址', name:'icon', editable:true, hidden:true},
			{header:'文件id', name:'uploadId', editable:true, hidden:true},
			{header:'${text("父表主键")}', name:'information.id', editable:true, hidden:true},
			{header:'${text("下载文件")}', name:'id', width:200,
				editable: true, edittype: "custom", editoptions: {
					custom_element: function(val, editOptions) {
						return js.template('fileuploadTpl', {
							id: 'fileupload_'+editOptions.id, title: '下载文件',
							bizKey: val, bizType: 'information_app_file', cssClass: '', readonly: false,
							inputId: editOptions.rowId + '_download'
						});
					},
					css:""
				}
			},
			{header:'${text("显示图标")}', name:'id', width:200,
				editable: true, edittype: "custom", editoptions: {
					custom_element: function(val, editOptions) {
						return js.template('imageuploadTpl', {
							id: 'imageupload_'+editOptions.id, title: '显示图标',
							bizKey: val, bizType: 'information_app_icon', cssClass: '', readonly: false,
							inputId: editOptions.rowId + '_icon'
						});
					},
					css:""
				}
			},
			{header:'${text("软件版本")}', name:'version', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'200', 'class':'form-control'}},
			{header:'${text("需求平台")}', name:'platform', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'200', 'class':'form-control'}}
		],
		shrinkToFit: false,	// 是否按百分比自动调整列宽

		// 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#informationDataGridAddRowBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上 v4.1.7
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		// 编辑表格的提交数据参数
		editGridInputFormListName: 'apps', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,information.id,download,icon,uploadId,version,platform,information_app_icon__del,information_app_file__del', // 提交数据列表的属性字段

		// 加载成功后执行事件
		ajaxSuccess: function(data){
		}
	});
</script>
<script id="fileuploadTpl" type="text/template">//<!--<div>
<#form:fileupload id="{{d.id}}" bizKey="{{d.bizKey}}" bizType="{{d.bizType}}" uploadType="all" callbackFuncName= "fileuploadCallback"
	class="{{d.cssClass}}" maxUploadNum="1" isMini="true" preview="false" returnPath="true" filePathInputId="{{d.inputId}}" readonly="{{d.readonly}}"/>
</div>//--></script>
<script id="imageuploadTpl" type="text/template">//<!--<div>
	<#form:fileupload id="{{d.id}}" bizKey="{{d.bizKey}}" bizType="{{d.bizType}}"  callbackFuncName= "fileuploadCallback"
	uploadType="image" class="" maxUploadNum="1" readonly="{{d.readonly}}" preview="false" returnPath="true" filePathInputId="{{d.inputId}}"/>
</div>//-->
</script>
<script>

	function fileuploadCallback(id, act, $this, fileUploadId, fileUrl, fileName, fileUpload){
		let rowId = id.split("_")[1];
		$("#" + rowId + "_uploadId").val(fileUploadId);
	}

	$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>

<script>
    js.loading()

    new Vue({
        el: '#informationFormMainContent',
        data: {
            informationGroupTreeData: [],
            informationGroupId: null,
            type: [{
                name: '下载',
                value: 'DOWNLOAD',
            }, {
                name: '配件',
                value: 'PARTS',
            }, {
                name: '视频',
                value: 'VIDEO',
            }, {
                name: '问答',
                value: 'FAQ',
            }],
            selectType: '',
            productName: '',
        },
        computed: {
            informationGroupTreeDataFilterProductName() {
                if (this.productName) {
                    let r = this.informationGroupTreeData.filter(item => item.productName === this.productName)
                    if (r && r.length !== 0) {
                        return r;
                    } else {
                        return this.informationGroupTreeData
                    }
                } else {
                    return this.informationGroupTreeData
                }
            }
        },
        mounted() {
            js.closeLoading()
            $('#informationFormMainContent').removeClass('hidden')

            this.initInformationData()

            let that = this
			that.productName = $('#productName').val()
            setInterval(() => {
                that.selectType = $('#select2-type-container').text()
                that.productName = $('#productName').val()
            }, 200)

            let informationGroupId = $('#informationGroupId').val()
            this.informationGroupId = Number(informationGroupId)
        },
        methods: {
            async initInformationData() {
				let type1 = this.type.findLast(item => item.name === this.selectType)?.value ?? '';
				if(!type1 || type === ''){
					this.informationGroupTreeData = []
					return
				}
				let response = await axios.post(ctx + '/product/informationGroup/tree', {
					type: type1,
					productName:this.productName
				})
                this.informationGroupTreeData = response.data
                if (this.informationGroupId) {
                    $('#groupBy').val(this.getName(this.informationGroupId, this.informationGroupTreeData))
                } else {
                    $('#groupBy').val('')
                }
            },
            getName(id, arr) {
                for (let i = 0; i < arr.length; i++) {
                    let item = arr[i]
                    if (item.id === id) {
                        return item.name
                    }
                    if (item.children) {
                        let name = this.getName(id, item.children)
                        if (name) {
                            return name
                        }
                    }
                }
                return null
            }
        },
        watch: {
			productName(){
				this.initInformationData()
			},
            informationGroupId(n, o) {
                $('#informationGroupId').val(n)
                if (n) {
					let name = this.getName(n, this.informationGroupTreeData);
					$('#groupBy').val(name)
                }
            },
            selectType(n, o) {
                if (o) {
                    this.informationGroupId = null
                }
                this.initInformationData()
            }
        }
    })
</script>