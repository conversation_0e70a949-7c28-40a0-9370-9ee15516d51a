package com.jeesite.modules.product.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.product.entity.Product;
import com.jeesite.modules.product.service.ProductService;

/**
 * 产品管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-12
 */
@Controller
@RequestMapping(value = "${adminPath}/product/product")
public class ProductController extends BaseController {

    @Autowired
    private ProductService productService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public Product get(String id, boolean isNewRecord) {
        return productService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("product:product:view")
    @RequestMapping(value = {"list", ""})
    public String list(Product product, Model model) {
        model.addAttribute("product", product);
        return "modules/product/productList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("product:product:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<Product> listData(Product product, HttpServletRequest request, HttpServletResponse response) {
        product.setPage(new Page<>(request, response));
        Page<Product> page = productService.findPage(product);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("product:product:view")
    @RequestMapping(value = "form")
    public String form(Product product, Model model) {
        if (product.getDiscontinued()==null) {
            product.setDiscontinued(0);
        }
        model.addAttribute("product", product);
        return "modules/product/productForm";
    }


    /**
     * 查看编辑表单
     */
    @RequiresPermissions("product:product:view")
    @RequestMapping(value = "select")
    public String select(Product product, String selectData, Model model) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }

        model.addAttribute("product", product);
        return "modules/product/productSelect";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("product:product:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated Product product, HttpServletRequest request) {
        productService.save(product);
        return renderResult(Global.TRUE, text("保存产品成功！"));
    }

    /**
     * 停用数据
     */
    @RequiresPermissions("product:product:edit")
    @RequestMapping(value = "disable")
    @ResponseBody
    public String disable(Product product) {
        product.setStatus(Product.STATUS_DISABLE);
        productService.updateStatus(product);
        return renderResult(Global.TRUE, text("停用产品成功"));
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("product:product:edit")
    @RequestMapping(value = "enable")
    @ResponseBody
    public String enable(Product product) {
        product.setStatus(Product.STATUS_NORMAL);
        productService.updateStatus(product);
        return renderResult(Global.TRUE, text("启用产品成功"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("product:product:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(Product product) {
        productService.delete(product);
        return renderResult(Global.TRUE, text("删除产品成功！"));
    }

}