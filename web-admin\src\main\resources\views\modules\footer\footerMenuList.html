<% layout('/layouts/default.html', {title: '菜单管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('菜单管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('footer:footerMenu:edit')){ %>
					<a href="${ctx}/footer/footerMenu/form" class="btn btn-default btnTool" title="${text('新增菜单')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${footerMenu}" action="${ctx}/footer/footerMenu/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('链接')}：</label>
					<div class="control-inline">
						<#form:input path="link" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="1024" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm">${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm">${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
// 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("名称")}', name:'name', index:'a.name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '( '+row.code+' ) '+'<a href="${ctx}/footer/footerMenu/form?code='+row.code+'" class="btnList" data-title="${text("编辑菜单")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("链接")}', name:'link', index:'a.link', width:150, align:"left"},
		{header:'${text("当前组排序")}', name:'treeSort', index:'a.tree_sort', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			<% if(hasPermi('footer:footerMenu:edit')){ %>
				actions.push('<a href="${ctx}/footer/footerMenu/form?code='+row.code+'" class="btnList" title="${text("编辑菜单")}"><i class="fa fa-pencil"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/footer/footerMenu/delete?code='+row.code+'" class="btnList" title="${text("删除菜单")}" data-confirm="${text("确认要删除该菜单及所有子菜单吗？")}" data-deltreenode="'+row.id+'"><i class="fa fa-trash-o"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/footer/footerMenu/form?parentCode='+row.id+'" class="btnList" title="${text("新增下级菜单")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
			<% } %>
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'name,link,remarks,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>