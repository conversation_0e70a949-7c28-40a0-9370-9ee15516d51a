package com.jeesite.modules.product.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.product.entity.ProductClassification;
import com.jeesite.modules.product.dao.ProductClassificationDao;

/**
 * 产品分类信息Service
 * <AUTHOR>
 * @version 2024-05-13
 */
@Service
@Transactional(readOnly=true)
public class ProductClassificationService extends CrudService<ProductClassificationDao, ProductClassification> {
	
	/**
	 * 获取单条数据
	 * @param productClassification
	 * @return
	 */
	@Override
	public ProductClassification get(ProductClassification productClassification) {
		return super.get(productClassification);
	}
	
	/**
	 * 查询分页数据
	 * @param productClassification 查询条件
	 * @param productClassification.page 分页对象
	 * @return
	 */
	@Override
	public Page<ProductClassification> findPage(ProductClassification productClassification) {
		return super.findPage(productClassification);
	}
	
	/**
	 * 查询列表数据
	 * @param productClassification
	 * @return
	 */
	@Override
	public List<ProductClassification> findList(ProductClassification productClassification) {
		return super.findList(productClassification);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param productClassification
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(ProductClassification productClassification) {
		super.save(productClassification);
	}
	
	/**
	 * 更新状态
	 * @param productClassification
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(ProductClassification productClassification) {
		super.updateStatus(productClassification);
	}
	
	/**
	 * 删除数据
	 * @param productClassification
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(ProductClassification productClassification) {
		super.delete(productClassification);
	}
	
}