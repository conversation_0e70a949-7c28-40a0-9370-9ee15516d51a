package com.hiwie.breeze.json.serializers;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.hiwie.breeze.AbstractOption;

import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class AbstractOptionSerializer extends StdSerializer<AbstractOption> {

    public static final AbstractOptionSerializer INSTANCE = new AbstractOptionSerializer();


    private static final long serialVersionUID = 1L;

    private AbstractOptionSerializer() {
        super(AbstractOption.class);
    }

    @Override
    public void serialize(AbstractOption value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeObjectField("option", Optional.ofNullable(value.orNull()));
        gen.writeEndObject();
    }

}
