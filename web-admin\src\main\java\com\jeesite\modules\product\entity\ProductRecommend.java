package com.jeesite.modules.product.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 产品推荐管理Entity
 *
 * <AUTHOR>
 * @version 2022-07-13
 */
@Table(name = "product_recommend", alias = "a", label = "推荐产品信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "product_id", attrName = "product.id", label = "推荐产品", isQuery = false),
        @Column(name = "sort", attrName = "sort", label = "排序", isQuery = false, isUpdateForce = true),
        @Column(name = "create_date", attrName = "createDate", label = "create_date", isUpdate = false, isQuery = false, isUpdateForce = true),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(name = "name", label = "产品名称", queryType = QueryType.LIKE),})
}, orderBy = "a.id DESC"
)
public class ProductRecommend extends DataEntity<ProductRecommend> {

    private static final long serialVersionUID = 1L;
    private Product product;        // 推荐产品
    private Long sort;        // 排序

    public ProductRecommend() {
        this(null);
    }

    public ProductRecommend(String id) {
        super(id);
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

}