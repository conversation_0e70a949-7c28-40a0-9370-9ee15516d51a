package com.topdon.website.model;

import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.SCConstants;

import java.time.LocalDateTime;
import java.util.List;

public class Classification {
    public static final String ENTITY = "CLASSIFICATION";
    private String id;
    private String name;  // 名称
    private LocalDateTime createAt;
    private Classification parent; // 父级分类
    private String idPath; // id路径
    private String namePath; //名称路径
    private long productCount; //下属产品数量
    private boolean hasSub; //是否有下级
    private String menuMedia; // 导航栏图片
    private String bannerMedia; // banner显示图片
    private String proMedia; // 导航内页图片
    private String navDesc; // 导航内页描述
    private String description; // 描述
    private List<Classification> children; //下级列表
    private List<Product> products; // 产品列表
    private List<ClassificationExtensionLink> extensionLinks; // 拓展链接列表
    private String productId;
    private Integer newProduct;
    private boolean compare;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public Classification getParent() {
        return parent;
    }

    public void setParent(Classification parent) {
        this.parent = parent;
    }

    public String getIdPath() {
        return idPath;
    }

    public void setIdPath(String idPath) {
        this.idPath = idPath;
    }

    public String getNamePath() {
        return namePath;
    }

    public void setNamePath(String namePath) {
        this.namePath = namePath;
    }

    public long getProductCount() {
        return productCount;
    }

    public void setProductCount(long productCount) {
        this.productCount = productCount;
    }

    public boolean isHasSub() {
        return hasSub;
    }

    public void setHasSub(boolean hasSub) {
        this.hasSub = hasSub;
    }

    public String getMenuMedia() {
        return menuMedia;
    }

    public void setMenuMedia(String menuMedia) {
        this.menuMedia = menuMedia;
    }

    public String getBannerMedia() {
        return bannerMedia;
    }

    public void setBannerMedia(String bannerMedia) {
        this.bannerMedia = bannerMedia;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Product> getProducts() {
        return products;
    }

    public void setProducts(List<Product> products) {
        this.products = products;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Integer getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(Integer newProduct) {
        this.newProduct = newProduct;
    }

    public List<Classification> getChildren() {
        return children;
    }

    public String getProMedia() {
        return proMedia;
    }

    public void setProMedia(String proMedia) {
        this.proMedia = proMedia;
    }

    public String getNavDesc() {
        return navDesc;
    }

    public void setNavDesc(String navDesc) {
        this.navDesc = navDesc;
    }

    public void setChildren(List<Classification> children) {
        this.children = children;
    }

    public List<ClassificationExtensionLink> getExtensionLinks() {
        return extensionLinks;
    }

    public void setExtensionLinks(List<ClassificationExtensionLink> extensionLinks) {
        this.extensionLinks = extensionLinks;
    }

    public boolean getCompare() {
        return compare;
    }

    public void setCompare(boolean compare) {
        this.compare = compare;
    }

    public static Classification defaultPk(String id, String name) {
        Classification classification = new Classification();
        classification.setId(id);
        classification.setName(name);
        return classification;
    }


    public static Classification defaultPk(String id, String name, String productId) {
        Classification classification = new Classification();
        classification.setId(id);
        classification.setName(name);
        classification.setProductId(productId);
        return classification;
    }

    public static class Errors {
        public static final ErrorMessage NOT_FOUND = new ErrorMessage(SCConstants.MODULE, ENTITY, "NOT_FOUND");
    }
}
