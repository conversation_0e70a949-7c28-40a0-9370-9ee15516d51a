package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.BannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/banners")
public class BannersController extends ControllerSupport {

    private final BannerService bannerService;

    @Autowired
    public BannersController(BannerService bannerService) {
        this.bannerService = bannerService;
    }

    @GetMapping
    public AbstractRestResponse list() {
        return AbstractRestResponse.apply(bannerService.list());
    }
}
