package com.topdon.admin.vo;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.topdon.admin.entity.MenuClickLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MenuClickLogExcelVo extends MenuClickLog {

    @ExcelFields({
            @ExcelField(title = "点击时间", attrName = "clickTime", align = ExcelField.Align.CENTER, sort = 10, dataFormat = "yyyy-MM-dd HH:mm:ss"),
            @ExcelField(title = "产品名称", attrName = "productName", align = ExcelField.Align.CENTER, sort = 20),
            @ExcelField(title = "菜单名称", attrName = "menuName", align = ExcelField.Align.CENTER, sort = 30),
            @ExcelField(title = "IP归属地", attrName = "ipLocation", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "IP地址", attrName = "ipAddress", align = ExcelField.Align.CENTER, sort = 50),
    })
    public MenuClickLogExcelVo() {
    }
}
