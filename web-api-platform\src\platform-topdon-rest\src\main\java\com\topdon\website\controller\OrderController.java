package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.services.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("order")
public class OrderController extends ControllerSupport {

    private final OrderService orderService;

    @Autowired
    public OrderController(OrderService orderService) {
        this.orderService = orderService;
    }

    @GetMapping
    public AbstractRestResponse get(@HWSession Session session, String orderNo,@RequestParam(defaultValue = "0") Integer type) {
        return AbstractRestResponse.apply(orderService.get(session, orderNo,type));
    }
}
