package com.hiwie.breeze.rest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hiwie.breeze.ErrorMessage;

/**
 * <AUTHOR>
 */
public class ErrorRestResponse extends AbstractRestResponse {

    @JsonProperty
    private final ErrorMessage error;

    public ErrorRestResponse(ErrorMessage error) {
        this.error = error;
    }

    public static ErrorRestResponse apply(ErrorMessage error) {
        return new ErrorRestResponse(error);
    }

    public ErrorMessage getError() {
        return error;
    }

}