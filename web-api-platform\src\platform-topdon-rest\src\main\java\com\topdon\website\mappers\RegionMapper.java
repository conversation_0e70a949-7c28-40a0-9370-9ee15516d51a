package com.topdon.website.mappers;

import com.topdon.website.model.Region;
import org.springframework.jdbc.core.RowMapper;

/**
 * <AUTHOR>
 */
public class RegionMapper {

    public static final RowMapper<Region> DETAIL = (rs, index) -> {
        Region detail = new Region();
        detail.setId(rs.getString("code"));
        detail.setName(rs.getString("name"));
        detail.setParent(Region.defaultFk(rs.getString("parent_code"),rs.getString("parent_name"),rs.getString("parent_flag")));
        detail.setIdPath(rs.getString("parent_codes"));
        detail.setFlag(rs.getString("flag"));
        detail.setNamePath(rs.getString("tree_names"));
        detail.setCreateAt(rs.getTimestamp("create_date").toLocalDateTime());
        return detail;
    };

}
