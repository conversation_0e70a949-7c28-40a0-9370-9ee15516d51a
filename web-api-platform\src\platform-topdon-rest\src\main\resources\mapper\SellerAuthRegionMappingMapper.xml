<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.SellerAuthRegionMappingMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.SellerAuthRegionMapping">
    <!--@mbg.generated-->
    <!--@Table seller_auth_region_mapping-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_auth_id" jdbcType="INTEGER" property="sellerAuthId" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, seller_auth_id, region_code
  </sql>
</mapper>