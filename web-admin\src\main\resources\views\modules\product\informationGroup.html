<% layout('/layouts/default.html', {title: '产品资料分组配置', libs: ['dataGrid']}){ %>

<link rel="stylesheet" href="/admin/static/element-ui/index.css">
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/element-ui/index.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<script src="/admin/static/common/vue.js"></script>

<div class="col-sm-8 hidden">
    <#form:listselect id="productSelect" title="用户选择" path="product.id" labelPath="product.name"
    url="${ctx}/product/product/select" allowClear="false"
    checkbox="false" itemCode="id" itemName="name" class="required"/>
</div>

<div class="main-content hidden" id="informationGroupMainContent">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('产品资料分组配置')}
            </div>
            <div class="box-tools pull-right">
                <el-button @click="hideQuery = !hideQuery">
                    <i class="fa fa-filter"></i>&nbsp;{{ hideQuery ? '查询' : '隐藏' }}
                </el-button>

                <% if(hasPermi('product:informationGroup:edit')){ %>
                <el-button @click="showEdit = true;editData = { parentId:0,productName: null };">
                    <i class="fa fa-plus"></i>&nbsp;新增
                </el-button>
                <% } %>

            </div>
        </div>
        <div class="box-body">
            <div class="form-inline" v-if="!hideQuery">
                <div class="form-group">
                    <label class="control-label">产品：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.productName" class="width-120"></el-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">资料类型：</label>
                    <div class="control-inline">
                        <el-select v-model="queryData.type">
                            <el-option v-for="item in type"
                                       :key="item.value"
                                       :label="item.name"
                                       :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">分组标题：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.name" class="width-120"></el-input>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-sm" @click="initInformationData">${text('查询')}
                    </button>
                    <button type="reset" class="btn btn-default btn-sm" @click="queryData ={};initInformationData()">
                        ${text('重置')}
                    </button>
                </div>
            </div>

            <el-dialog title="产品资料分组配置" :visible.sync="showEdit">
                <el-form :model="editData" ref="ruleForm" label-width="130px">
                    <el-form-item label="产品"
                                  prop="productName">
                        <div>
                            <div class="width-260" style="float: left">
                                <el-input class="" v-model="editData.productName" disabled=""></el-input>
                            </div>
                            <div class="btn btn-default btn-mini ml3" @click="()=>{$('#productSelectName').click()}"
                                 v-if="!editData.parentId">
                                <i class="fa fa-plus"></i>选择产品
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item v-if="editData.parentId && editData.parentId !== 0"
                                  label="上级分组层级"
                                  prop="parentId"
                                  :rules="[{ required: true, message: '上级分组层级必填', trigger: 'blur' }]">
                        <el-input v-model="editData.parentName" disabled="" class="width-260"></el-input>
                    </el-form-item>
                    <el-form-item label="资料类型"
                                  prop="type"
                                  :rules="[{ required: true, message: '请选择资料类型', trigger: 'blur' }]">
                        <el-select v-model="editData.type" placeholder="请选择资料类型" class="width-260"
                                   :disabled="editData.id || editData.parentId">
                            <el-option v-for="item in type" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="分组标题"
                                  prop="name"
                                  :rules="[{ required: true, message: '请填写分组标题', trigger: 'blur' },
                                  { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }]">
                        <el-input v-model="editData.name" class="width-260"></el-input>
                    </el-form-item>
                    </el-form-item>
                    <el-form-item label="分组排序"
                                  prop="sort"
                                  :rules="[{ required: true, message: '请填写分组排序', trigger: 'blur' }]">
                        <el-input v-model="editData.sort" type="number" class="width-260"></el-input>
                    </el-form-item>
                    <el-form-item label="分组层级"
                                  prop="level">
                        <el-input v-model="editData.parentId === 0 ?'一级':'二级'" class="width-260" disabled=""></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
                        <el-button @click="showEdit = false;">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <el-table
                    :data="tableData"
                    style="width: 100%;margin-bottom: 20px;"
                    row-key="id"
                    border
                    :tree-props="{children: 'children'}">
                <el-table-column
                        prop="type"
                        label="资料类型">
                    <template slot-scope="scope">
                        {{ type.findLast(item => item.value === scope.row.type).name }}
                    </template>
                </el-table-column>
                <el-table-column
                        prop="productName"
                        label="产品名称">
                </el-table-column>
                <el-table-column
                        prop="name"
                        label="分组标题">
                </el-table-column>
                <el-table-column
                        label="分组层级">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.parentId?'':'success'">
                            {{ scope.row.parentId === 0 ? '一级' : '二级' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="sort"
                        label="排序">
                </el-table-column>

                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-link icon="fa fa-pencil" type="success" title="编辑"
                                 @click="handleEdit(scope.row)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-trash-o" type="danger" title="删除"
                                 @click="handleDelete(scope.row.id)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-plus-square" type="primary" title="添加下级分组层级"
                                 v-if="scope.row.type === 'PARTS' && scope.row.parentId === 0"
                                 @click="handleAddChild(scope.row)"></el-link>&nbsp;&nbsp;
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</div>
<script>
    var ctx = '${ctx}'
</script>
<% } %>
<script>
    js.loading()

    new Vue({
        el: '#informationGroupMainContent',
        data: {
            editData: {},
            showEdit: false,
            tableData: [],
            queryData: {},
            hideQuery: true,
            type: [{
                name: '下载',
                value: 'DOWNLOAD',
            }, {
                name: '配件',
                value: 'PARTS',
            }, {
                name: '视频',
                value: 'VIDEO',
            }, {
                name: '问答',
                value: 'FAQ',
            }]
        },
        mounted() {
            js.closeLoading()
            $('#informationGroupMainContent').removeClass('hidden')

            this.initInformationData()
            let that = this
            $('#productSelectCode').on('change', function () {
                var id = $(this).val();

                requestAnimationFrame(function () {
                    var name = $('#productSelectName').val();

                    if (name) {
                        Vue.set(that.editData, 'productName', name);
                        Vue.set(that.editData, 'productId', id);
                    } else {
                        Vue.set(that.editData, 'productName', null);
                        Vue.set(that.editData, 'productId', null);
                    }
                });
            });
        },
        methods: {
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        axios.post(ctx + '/product/informationGroup/saveOrUpdate', this.editData).then(res => {
                            if(res.data){
                                js.showErrorMessage(res.data)
                                return
                            }
                            this.initInformationData()
                            this.$message({
                                type: 'success',
                                message: '保存成功!'
                            });
                            this.showEdit = false
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            async initInformationData() {
                let response = await axios.post(ctx + '/product/informationGroup/tree', this.queryData)
                this.tableData = response.data
            },
            async handleDelete(id) {
                try {
                    await this.$confirm('此操作将永久删除该分组, 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })

                    let res = await axios.post(ctx + '/product/informationGroup/delete/' + id)
                    if(res.data){
                        js.showErrorMessage(res.data)
                        return
                    }
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    await this.initInformationData()
                } catch (e) {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                }
            },
            handleEdit(obj) {
                let findLast = this.tableData.findLast(item => item.id === obj.parentId);
                this.editData = {
                    ...obj,
                    sort: obj.sort,
                    parentName: findLast?.name,
                    productName: findLast?.productName
                }
                this.showEdit = true
            },
            handleAddChild(obj) {
                this.editData = {
                    type: obj.type,
                    parentId: obj.id,
                    parentName: obj?.name,
                    productName: obj?.productName,
                    productId: obj?.productId
                }
                this.showEdit = true
            }
        }
    })
</script>
<style>

</style>