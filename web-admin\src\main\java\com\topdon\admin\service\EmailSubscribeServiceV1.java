package com.topdon.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.dto.EmailSubscribeDeleteDto;
import com.topdon.admin.dto.EmailSubscribeListDto;
import com.topdon.admin.dto.EmailSubscribePageDto;
import com.topdon.admin.entity.EmailSubscribe;
import com.topdon.admin.vo.EmailSubscribeExcelVo;

import java.util.List;

public interface EmailSubscribeServiceV1 extends IService<EmailSubscribe> {
    List<EmailSubscribeExcelVo> getExcelList(EmailSubscribeListDto emailSubscribe);

    PageDTO<EmailSubscribe> getPage(EmailSubscribePageDto emailSubscribe);

    String batchDelete(EmailSubscribeDeleteDto deleteDto);

    /**
     * 保存订阅，检查同一站点+同一邮箱不能重复
     */
    String saveWithDuplicateCheck(EmailSubscribe emailSubscribe);
}
