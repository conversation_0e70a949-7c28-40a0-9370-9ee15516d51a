<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.AuthorizedPlatformsMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.AuthorizedPlatforms">
    <!--@mbg.generated-->
    <!--@Table authorized_platforms-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_auth_id" jdbcType="INTEGER" property="sellerAuthId" />
    <result column="auth_platform" jdbcType="VARCHAR" property="authPlatform" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_link" jdbcType="VARCHAR" property="storeLink" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, seller_auth_id, auth_platform, store_name, store_link
  </sql>
</mapper>