/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * AdminWebApplication
 * <AUTHOR>
 * @version 2018-10-13
 */
@MapperScan(basePackages = "com.topdon.admin.mapper",sqlSessionFactoryRef = "mybatisPlusSqlSessionFactory")
@SpringBootApplication(scanBasePackages= {"com.jeesite","com.topdon.admin"})
public class AdminWebApplication extends SpringBootServletInitializer {
	
	public static void main(String[] args) {
        try {
            SpringApplication.run(AdminWebApplication.class, args);
        } catch (Exception e) {
			e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		this.setRegisterErrorPageFilter(false); // 错误页面有容器来处理，而不是SpringBoot
		return builder.sources(AdminWebApplication.class);
	}
	
}