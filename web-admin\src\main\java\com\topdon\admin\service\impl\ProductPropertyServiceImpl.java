package com.topdon.admin.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.modules.product.entity.Product;
import com.jeesite.modules.product.service.ProductService;
import com.topdon.admin.dto.ProductPropertyListCategoryIdDTO;
import com.topdon.admin.dto.ProductPropertySaveDTO;
import com.topdon.admin.entity.ProductProperty;
import com.topdon.admin.entity.PropertyCategory;
import com.topdon.admin.mapper.ProductPropertyMapper;
import com.topdon.admin.service.ClassificationCompareService;
import com.topdon.admin.service.ProductPropertyService;
import com.topdon.admin.service.PropertyCategoryService;
import com.topdon.admin.vo.ProductPropertyVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductPropertyServiceImpl extends ServiceImpl<ProductPropertyMapper, ProductProperty> implements ProductPropertyService {

    @Resource
    private PropertyCategoryService propertyCategoryService;
    @Resource
    private ProductService productService;
    @Resource
    private ClassificationCompareService classificationCompareService;

    @Override
    public void saveProductProperty(ProductPropertySaveDTO productPropertySaveDTO) {
        // 保存或更新属性类别，并获取一个映射，键为类别名，值为PropertyCategory对象
        Map<String, PropertyCategory> propertyCategoryMap = propertyCategoryService.saveOrUpdateList(productPropertySaveDTO.isDraft(),
                productPropertySaveDTO.getCompareId(),
                productPropertySaveDTO.getPropertyCategories());

        // 获取产品属性列表
        List<ProductPropertySaveDTO.ProductPropertySaveDTOInner> productProperties = productPropertySaveDTO.getProductProperties();

        // 删除旧的产品属性
        lambdaUpdate()
                .eq(ProductProperty::getDraft, productPropertySaveDTO.isDraft())
                .eq(ProductProperty::getClassificationCompareId, productPropertySaveDTO.getCompareId())
                .remove();

        // 遍历并保存新的产品属性
        for (ProductPropertySaveDTO.ProductPropertySaveDTOInner productProperty : productProperties) {
            // 根据类别名获取对应的PropertyCategory对象
            PropertyCategory propertyCategory = propertyCategoryMap.get(productProperty.getCategory());
            // 设置draft状态
            productProperty.setDraft(productPropertySaveDTO.isDraft());
            // 设置categoryId
            productProperty.setCategoryId(propertyCategory.getId());
            productProperty.setClassificationCompareId(productPropertySaveDTO.getCompareId());
            // 保存产品属性
            save(productProperty);
        }

        // 更新这个分类状态
        classificationCompareService.updateStatus(productPropertySaveDTO.getCompareId(),productPropertySaveDTO.isDraft());
    }

    @Override
    public List<ProductPropertyVo> listByCategoryId(ProductPropertyListCategoryIdDTO productPropertyListCategoryIdDTO) {
        // 查询产品属性列表
        // 条件：按sort升序排序，categoryId在指定列表中，匹配draft状态
        List<ProductProperty> list = this.lambdaQuery()
                .orderByAsc(ProductProperty::getSort)
                .eq(ProductProperty::getClassificationCompareId, productPropertyListCategoryIdDTO.getCompareId())
                .in(ProductProperty::getCategoryId, productPropertyListCategoryIdDTO.getCategoryId())
                .eq(ProductProperty::getDraft, productPropertyListCategoryIdDTO.isDraft())
                .list();

        ArrayList<ProductPropertyVo> productPropertyVos = new ArrayList<>();

        // 按productId分组处理产品属性
        list.stream().collect(Collectors.groupingBy(ProductProperty::getProductId))
                .forEach((productId, item) -> {
                    // 创建ProductPropertyVo对象
                    ProductPropertyVo productPropertyVo = new ProductPropertyVo();
                    // 设置产品名称
                    productPropertyVo.setProductName(productService.get(productId).getName());
                    // 设置产品ID
                    productPropertyVo.setProductId(productId);
                    // 转换并设置产品属性列表
                    productPropertyVo.setProductProperties(item.stream().map(item1 -> {
                        ProductPropertyVo.ProductPropertyVoInner productPropertyVoInner = new ProductPropertyVo.ProductPropertyVoInner();
                        // 复制属性
                        BeanUtils.copyProperties(item1, productPropertyVoInner);
                        // 设置类别名称
                        productPropertyVoInner.setCategory(propertyCategoryService.getById(item1.getCategoryId()).getCategory());
                        return productPropertyVoInner;
                    }).collect(Collectors.toList()));

                    // 添加到结果列表
                    productPropertyVos.add(productPropertyVo);
                });

        // 根据第一个产品属性的sort字段对结果列表进行排序
        productPropertyVos.sort(Comparator.comparingInt(o -> o.getProductProperties().get(0).getSort()));

        return productPropertyVos;
    }

    @Override
    public String importExcel(MultipartFile file, Integer compareId) {
        StringBuilder sb = new StringBuilder();
        List<String> headerList = new ArrayList<>();
        List<List<String>> data = new ArrayList<>();

        try (ExcelImport excelImport = new ExcelImport(file, 1, 0);) {
            // 取出第一列头部数据
            int lastCellNum = excelImport.getLastCellNum();
            for (int i = 0; i < excelImport.getDataRowNum(); i++) {
                Row row = excelImport.getRow(i);
                for (int i1 = 0; i1 < lastCellNum; i1++) {
                    Cell cell = row.getCell(i1);
                    String stringCellValue = cell.getStringCellValue();
                    if (StrUtil.isNotBlank(stringCellValue)) {
                        headerList.add(stringCellValue);
                    } else {
                        break;
                    }
                }
            }

            int headerSize = headerList.size();
            for (int i = 1; i < excelImport.getLastDataRowNum(); i++) {
                Row row = excelImport.getRow(i);
                if (row == null) {
                    break;
                }
                ArrayList<String> list = new ArrayList<>();
                data.add(list);
                for (int i1 = 0; i1 < headerSize; i1++) {
                    Cell cell = row.getCell(i1);
                    switch (cell.getCellType()) {
                        case BLANK:
                        case FORMULA:
                        case ERROR:
                            list.add("");
                            break;
                        case BOOLEAN:
                            list.add(String.valueOf(cell.getBooleanCellValue()));
                            break;
                        case STRING:
                            list.add(cell.getStringCellValue());
                            break;
                        case NUMERIC:
                            try {
                                list.add(BigDecimal.valueOf(cell.getNumericCellValue()).stripTrailingZeros().toPlainString());
                            } catch (Exception e) {
                                list.add(String.valueOf(cell.getNumericCellValue()));
                            }
                            break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("导入产品对比Excel错误", e);
            return e.getMessage();
        }

        if (headerList.isEmpty() || data.isEmpty()) {
            return "";
        }

        ArrayList<ProductPropertySaveDTO.PropertyCategorySaveDTO> propertyCategories = new ArrayList<>();
        ArrayList<ProductPropertySaveDTO.ProductPropertySaveDTOInner> productProperties = new ArrayList<>();
        List<String> mainCategoryList = data.stream().map(item -> item.get(0)).collect(Collectors.toList());
        List<String> mainCategorySet = new ArrayList<>(new LinkedHashSet<>(mainCategoryList));
        List<String> subcategoryList = data.stream().map(item -> item.get(1)).collect(Collectors.toList());

        // 主分类
        for (int i = 0; i < mainCategorySet.size(); i++) {
            String category = mainCategorySet.get(i);
            ProductPropertySaveDTO.PropertyCategorySaveDTO mainPropertyCategorySaveDTO = new ProductPropertySaveDTO.PropertyCategorySaveDTO();
            mainPropertyCategorySaveDTO.setClassificationCompareId(compareId);
            mainPropertyCategorySaveDTO.setParentId(0);
            mainPropertyCategorySaveDTO.setCategory(category);
            mainPropertyCategorySaveDTO.setSort(i);
            propertyCategories.add(mainPropertyCategorySaveDTO);
        }

        // 子分类
        for (int i = 0; i < subcategoryList.size(); i++) {
            String mainCategory = mainCategoryList.get(i);
            String category = subcategoryList.get(i);

            ProductPropertySaveDTO.PropertyCategorySaveDTO propertyCategorySaveDTO = new ProductPropertySaveDTO.PropertyCategorySaveDTO();
            propertyCategorySaveDTO.setParentCategory(mainCategory);
            propertyCategorySaveDTO.setClassificationCompareId(compareId);
            propertyCategorySaveDTO.setCategory(category);
            propertyCategorySaveDTO.setSort(i);
            propertyCategories.add(propertyCategorySaveDTO);
        }

        if (headerList.size() > 2) {
            for (int i = 2; i < headerList.size(); i++) {
                String productName = headerList.get(i);
                Product product = productService.getByEqName(productName);
                if (product == null) {
                    sb.append(productName).append("不存在 ,");
                    log.info("product == null ,productName:{}", productName);
                    continue;
                }

                for (int i1 = 0; i1 < data.size(); i1++) {
                    List<String> row = data.get(i1);
                    String val = row.get(i);
                    String category = subcategoryList.get(i1);

                    ProductPropertySaveDTO.ProductPropertySaveDTOInner productPropertySaveDTOInner = new ProductPropertySaveDTO.ProductPropertySaveDTOInner();
                    productPropertySaveDTOInner.setCategory(category);
                    productPropertySaveDTOInner.setProductId(product.getId());
                    productPropertySaveDTOInner.setVal(val);
                    productPropertySaveDTOInner.setSort(i);
                    productProperties.add(productPropertySaveDTOInner);
                }
            }
        }

        ProductPropertySaveDTO productPropertySaveDTO = new ProductPropertySaveDTO();
        productPropertySaveDTO.setDraft(true);
        productPropertySaveDTO.setCompareId(compareId);
        productPropertySaveDTO.setPropertyCategories(propertyCategories);
        productPropertySaveDTO.setProductProperties(productProperties);

        saveProductProperty(productPropertySaveDTO);

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}
