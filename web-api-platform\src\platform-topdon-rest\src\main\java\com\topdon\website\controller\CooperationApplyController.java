package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.breeze.rest.ErrorRestResponse;
import com.topdon.website.form.CooperationApplyCreateForm;
import com.topdon.website.services.CooperationApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("cooperation_apply")
public class CooperationApplyController extends ControllerSupport {
    private final CooperationApplyService cooperationApplyService;

    @Autowired
    public CooperationApplyController(CooperationApplyService cooperationApplyService) {
        this.cooperationApplyService = cooperationApplyService;
    }

    @PostMapping
    public AbstractRestResponse create(@Valid CooperationApplyCreateForm createForm, BindingResult result) {
        if (result.hasErrors()) {
            return ErrorRestResponse.apply(BINDING_ERROR.apply(result));
        }
        return AbstractRestResponse.apply(cooperationApplyService.create(createForm));
    }
}
