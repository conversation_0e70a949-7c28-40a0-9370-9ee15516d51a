package com.topdon.website.form;


import com.topdon.website.model.Subscribe;

public class SubscribeForm {
    private String email;
    private Subscribe.SubscribeSiteFrom siteFrom;
    private Subscribe.SubscribeFrom from;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Subscribe.SubscribeSiteFrom getSiteFrom() {
        return siteFrom;
    }

    public void setSiteFrom(Subscribe.SubscribeSiteFrom siteFrom) {
        this.siteFrom = siteFrom;
    }

    public Subscribe.SubscribeFrom getFrom() {
        return from;
    }

    public void setFrom(Subscribe.SubscribeFrom from) {
        this.from = from;
    }

    public static SubscribeForm apply(String email, Subscribe.SubscribeSiteFrom siteFrom, Subscribe.SubscribeFrom from) {
        SubscribeForm form = new SubscribeForm();
        form.setEmail(email);
        form.setSiteFrom(siteFrom);
        form.setFrom(from);
        return form;
    }
}
