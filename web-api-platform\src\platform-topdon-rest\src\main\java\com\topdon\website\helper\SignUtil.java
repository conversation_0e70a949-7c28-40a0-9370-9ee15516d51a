package com.topdon.website.helper;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.crypto.symmetric.AES;
import com.hiwie.breeze.json.Json;
import com.topdon.website.model.ShopifyCustomer;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class SignUtil {

    public static String randomString(int strLength) {

        Random rnd = ThreadLocalRandom.current();
        StringBuilder ret = new StringBuilder();
        for (int i = 0; i < strLength; i++) {
            boolean isChar = (rnd.nextInt(2) % 2 == 0);// 输出字母还是数字
            if (isChar) { // 字符串
                int choice = rnd.nextInt(2) % 2 == 0 ? 65 : 97; // 取得大写字母还是小写字母
                ret.append((char) (choice + rnd.nextInt(26)));
            } else { // 数字
                ret.append(rnd.nextInt(10));
            }
        }
        return ret.toString();
    }

    public static String multipassEncode(ShopifyCustomer customerData, String multipassEncode) {
        try {
            byte[] signAndEnc = DigestUtils.sha256(multipassEncode);
            byte[] enc = new byte[16];
            byte[] sign = new byte[16];
            System.arraycopy(signAndEnc, 0, enc, 0, 16);
            System.arraycopy(signAndEnc, 16, sign, 0, 16);

            byte[] dataBytes = Json.MAPPER.writeValueAsString(customerData).getBytes();
            IvParameterSpec ivspec = new IvParameterSpec(randomString(16).getBytes());
            SecretKeySpec keyspec = new SecretKeySpec(enc, "AES");

            AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, keyspec, ivspec);
            byte[] encrypted = aes.encrypt(dataBytes);
            byte[] ciphertext = ArrayUtil.addAll(ivspec.getIV(), encrypted);
            byte[] signed = DigestUtil.hmac(HmacAlgorithm.HmacSHA256, sign).digest(ciphertext);
            String token = Base64.encode(ArrayUtil.addAll(ciphertext, signed));
            return token.replace("+", "-").replace("/", "_");

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String sortAscii(Map<String, Object> param) {
        List<Map.Entry<String, Object>> infoIds = new ArrayList<>(param.entrySet());
        infoIds.sort(Map.Entry.comparingByKey());
        StringBuilder ret = new StringBuilder();
        for (Map.Entry<String, Object> entry : infoIds) {
            ret.append(entry.getKey());
            ret.append("=");
            ret.append(entry.getValue());
            ret.append("&");
        }
        ret = new StringBuilder(ret.substring(0, ret.length() - 1));
        return ret.toString();
    }
}
