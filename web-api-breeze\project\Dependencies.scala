import sbt._

object Dependencies {

  lazy val spring_web = spring("spring-web")
  lazy val spring_webflux = spring("spring-webflux")
  lazy val spring_aspects = spring("spring-aspects")
  lazy val spring_orm = spring("spring-orm")
  lazy val spring_webmvc = spring("spring-webmvc")
  lazy val spring_core = spring("spring-core")
  lazy val spring_jdbc = spring("spring-jdbc")
  lazy val spring_beans = spring("spring-beans")
  lazy val spring_context = spring("spring-context")
  lazy val spring_expression = spring("spring-expression")
  lazy val spring_context_support = spring("spring-context-support")
  lazy val spring_test = spring("spring-test")
  lazy val netty_http = "io.projectreactor.netty" % "reactor-netty-http" % "1.0.24"
  lazy val javax_servlet = "javax.servlet" % "javax.servlet-api" % "4.0.0" % Provided
  lazy val javax_validation = "javax.validation" % "validation-api" % "2.0.1.Final"
  lazy val javax_inject = "javax.inject" % "javax.inject" % "1"
  lazy val javax_el = "javax.el" % "javax.el-api" % "3.0.0"
  lazy val javax_el_impl = "org.glassfish" % "javax.el" % "3.0.0"
  lazy val poi = "org.apache.poi" % "poi" % poi_version
  lazy val poi_ooxml = "org.apache.poi" % "poi-ooxml" % poi_version
  lazy val poi_ooxml_schemas = "org.apache.poi" % "poi-ooxml-schemas" % poi_version
  lazy val jackson_databind = "com.fasterxml.jackson.core" % "jackson-databind" % jackson_version
  lazy val jackson_datatype_jdk8 = "com.fasterxml.jackson.datatype" % "jackson-datatype-jdk8" % jackson_version
  lazy val jackson_datatype_jsr310 = "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310" % jackson_version
  lazy val jackson_core = "com.fasterxml.jackson.core" % "jackson-core" % jackson_version
  lazy val guava = "com.google.guava" % "guava" % "24.1-jre"
  lazy val hibernate_validator = "org.hibernate" % "hibernate-validator" % "6.0.9.Final"
  lazy val commons_lang3 = "org.apache.commons" % "commons-lang3" % "3.7"
  lazy val commons_codec = "commons-codec" % "commons-codec" % "1.11"
  lazy val commons_text = "org.apache.commons" % "commons-text" % "1.3"
  lazy val commons_beanutils = "commons-beanutils" % "commons-beanutils" % "1.9.3"
  lazy val logback = "ch.qos.logback" % "logback-classic" % "1.2.3"
  lazy val lomlok = "org.projectlombok" % "lombok" % "1.18.22"
  lazy val jedis = "redis.clients" % "jedis" % "2.9.0"
  lazy val intellij_annotation = "com.intellij" % "annotations" % "12.0"
  lazy val junit_jupiter = "org.junit.jupiter" % "junit-jupiter-api" % junit_version
  lazy val junit_platform = "org.junit.platform" % "junit-platform-runner" % "1.1.1"
  lazy val junit_vintage = "org.junit.vintage" % "junit-vintage-engine" % junit_version
  lazy val jsonpath = "com.jayway.jsonpath" % "json-path" % "2.4.0"
  lazy val quartz = "org.quartz-scheduler" % "quartz" % "2.3.0"
  lazy val activiti_engine="org.activiti" % "activiti-engine" % activiti_verison
  lazy val activiti_modoler = "org.activiti" % "activiti-modeler" % activiti_verison
  lazy val activiti_spring = "org.activiti" % "activiti-spring" % activiti_verison
  lazy val aspectj_rt = "org.aspectj" % "aspectjrt" % aspectj_version
  lazy val aspectj_tools = "org.aspectj" % "aspectjtools" % aspectj_version
  lazy val aspectj_weaver = "org.aspectj" % "aspectjweaver" % aspectj_version

  private val jackson_version = "2.9.5"
  private val junit_version = "5.1.1"
  private val activiti_verison = "5.21.0"
  private val poi_version = "3.9"
  private val aspectj_version = "1.8.13"

  private def spring(artifact: String) = "org.springframework" % artifact % "5.3.22"

}