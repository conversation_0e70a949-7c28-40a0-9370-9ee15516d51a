package com.topdon.website.repositories;

import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.topdon.website.form.CooperationApplyCreateForm;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;

@Named
public class CooperationApplyRepository extends MysqlJDBCSupport {

    @Inject
    protected CooperationApplyRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public long create(CooperationApplyCreateForm createForm) {
        @Language("SQL") String sql = "insert into cooperation_apply(type, email,phone_number, first_name, last_name, company_name, company_size, website_url, country, address, brand, message, create_at) VALUES (:type,:email,:phoneNumber,:firstName,:lastName,:companyName,:companySize,:websiteUrl,:country,:address,:brand,:message,now())";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("type", createForm.getType());
        params.addValue("email", createForm.getEmail());
        params.addValue("phoneNumber", createForm.getPhoneNumber());
        params.addValue("firstName", createForm.getFirstName());
        params.addValue("lastName", createForm.getLastName());
        params.addValue("companyName", createForm.getCompanyName());
        params.addValue("companySize", createForm.getCompanySize());
        params.addValue("websiteUrl", createForm.getWebsiteUrl());
        params.addValue("country", createForm.getCountryId());
        params.addValue("address", createForm.getAddress());
        params.addValue("brand", createForm.getBrand());
        params.addValue("message", createForm.getMessage());
        return autoIncreaseInsert(sql, params).longValue();
    }
}
