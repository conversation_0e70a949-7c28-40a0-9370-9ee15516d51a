package com.topdon.admin.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.dto.ProductLinesDTO;
import com.topdon.admin.entity.ProductLines;
import com.topdon.admin.vo.ProductLinesExportVo;

import java.util.List;

public interface ProductLinesService extends IService<ProductLines> {


    List<Tree<Integer>> getTree(ProductLinesDTO productLinesDTO);

    String delete(Integer id);

    List<ProductLinesExportVo> exportData(ProductLinesDTO productLinesDTO);
}
