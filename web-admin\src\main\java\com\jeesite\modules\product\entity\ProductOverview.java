package com.jeesite.modules.product.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 产品详情管理Entity
 *
 * <AUTHOR>
 * @version 2022-04-21
 */
@Table(name = "product_overview", alias = "a", label = "产品详情信息", columns = {
        @Column(name = "id", attrName = "id", label = "id", isPK = true),
        @Column(name = "product_id", attrName = "product.id", label = "产品"),
        @Column(name = "type", attrName = "type", label = "显示类型"),
        @Column(name = "desc", attrName = "desc", label = "描述", isQuery = false),
        @Column(name = "image", attrName = "image", label = "图片", isQuery = false),
        @Column(name = "title", attrName = "title", label = "标题", queryType = QueryType.LIKE),
        @Column(name = "background", attrName = "background", label = "背景色", isQuery = false),
        @Column(name = "title_color", attrName = "titleColor", label = "标题色", isQuery = false),
        @Column(name = "desc_color", attrName = "descColor", label = "描述颜色", isQuery = false),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Product.class, alias = "p",
                on = "p.id = a.product_id", attrName = "product",
                columns = {@Column(name = "name", label = "产品名称", queryType = QueryType.LIKE),})
}, orderBy = "a.id DESC"
)
public class ProductOverview extends DataEntity<ProductOverview> {

    private static final long serialVersionUID = 1L;
    private Product product;        // 产品
    private String type;        // 显示类型
    private String desc;        // 描述
    private String image;        // 图片
    private String title;        // 标题
    private String background;        // 背景色
    private String titleColor;        // 标题色
    private String descColor;        // 描述颜色

    public ProductOverview() {
        this(null);
    }

    public ProductOverview(String id) {
        super(id);
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    @Size(min = 0, max = 20, message = "显示类型长度不能超过 20 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Size(min = 0, max = 256, message = "描述长度不能超过 256 个字符")
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Size(min = 0, max = 1024, message = "图片长度不能超过 1024 个字符")
    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Size(min = 0, max = 56, message = "标题长度不能超过 56 个字符")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Size(min = 0, max = 50, message = "背景色长度不能超过 50 个字符")
    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    @Size(min = 0, max = 25, message = "标题色长度不能超过 25 个字符")
    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    @Size(min = 0, max = 25, message = "描述颜色长度不能超过 25 个字符")
    public String getDescColor() {
        return descColor;
    }

    public void setDescColor(String descColor) {
        this.descColor = descColor;
    }

}