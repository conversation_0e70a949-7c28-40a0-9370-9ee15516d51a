package com.topdon.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.topdon.admin.dto.MenuClickLogListDto;
import com.topdon.admin.dto.MenuClickLogPageDto;
import com.topdon.admin.entity.MenuClickLog;
import com.topdon.admin.service.MenuClickLogService;
import com.topdon.admin.vo.MenuClickLogExcelVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Controller
@RequestMapping("${adminPath}/menu/click/log")
public class MenuClickLogController {

    @Resource
    private MenuClickLogService menuClickLogService;

    @RequiresPermissions("menu:click:log:view")
    @GetMapping
    public String view(Model model) throws JsonProcessingException {
        // 获取当前时间
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusMonths(1);

        // 转成时间戳（毫秒值）
        long startMillis = startDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long endMillis = endDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        model.addAttribute("startDate", startMillis);
        model.addAttribute("endDate", endMillis);

        return "modules/menu/meneClickLogList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("menu:click:log:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<MenuClickLog> listData(MenuClickLogPageDto menuClickLogPageDto) {
        if (menuClickLogPageDto.getPageNo() == null) {
            menuClickLogPageDto.setPageNo(1);
        }
        if (menuClickLogPageDto.getPageSize() == null) {
            menuClickLogPageDto.setPageSize(20);
        }
        if (StrUtil.isBlank(menuClickLogPageDto.getOrderBy())) {
            menuClickLogPageDto.setOrderBy("a.click_time desc");
        }
        PageDTO<MenuClickLog> page = menuClickLogService.getPage(menuClickLogPageDto);
        return new Page<>((int) page.getCurrent(), (int) page.getSize(), page.getTotal(), page.getRecords());
    }

    /**
     * 导出列表数据
     */
    @RequiresPermissions("menu:click:log:view")
    @RequestMapping(value = "exportData")
    @ResponseBody
    public void exportData(MenuClickLogListDto menuClickLogListDto, HttpServletResponse response) {
        List<MenuClickLogExcelVo> list = menuClickLogService.getExcelList(menuClickLogListDto);
        String fileName = "菜单点击日志数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try (ExcelExport ee = new ExcelExport(null, MenuClickLogExcelVo.class)) {
            ee.setDataList(list).write(response, fileName);
        }
    }
}
