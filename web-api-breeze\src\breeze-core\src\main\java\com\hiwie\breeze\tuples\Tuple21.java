package com.hiwie.breeze.tuples;

/**
 * <AUTHOR>
 */
public class Tuple21<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18, T19, T20, T21> implements Tuple {

    private final T1 _1;
    private final T2 _2;
    private final T3 _3;
    private final T4 _4;
    private final T5 _5;
    private final T6 _6;
    private final T7 _7;
    private final T8 _8;
    private final T9 _9;
    private final T10 _10;
    private final T11 _11;
    private final T12 _12;
    private final T13 _13;
    private final T14 _14;
    private final T15 _15;
    private final T16 _16;
    private final T17 _17;
    private final T18 _18;
    private final T19 _19;
    private final T20 _20;
    private final T21 _21;


    Tuple21(T1 t1, T2 t2, T3 t3, T4 t4, T5 t5, T6 t6, T7 t7, T8 t8, T9 t9, T10 t10, T11 t11, T12 t12, T13 t13, T14 t14, T15 t15, T16 t16, T17 t17, T18 t18, T19 t19, T20 t20, T21 t21) {
        this._1 = t1;
        this._2 = t2;
        this._3 = t3;
        this._4 = t4;
        this._5 = t5;
        this._6 = t6;
        this._7 = t7;
        this._8 = t8;
        this._9 = t9;
        this._10 = t10;
        this._11 = t11;
        this._12 = t12;
        this._13 = t13;
        this._14 = t14;
        this._15 = t15;
        this._16 = t16;
        this._17 = t17;
        this._18 = t18;
        this._19 = t19;
        this._20 = t20;
        this._21 = t21;
    }

    public T1 _1() {
        return _1;
    }

    public T2 _2() {
        return _2;
    }

    public T3 _3() {
        return _3;
    }

    public T4 _4() {
        return _4;
    }

    public T5 _5() {
        return _5;
    }

    public T6 _6() {
        return _6;
    }

    public T7 _7() {
        return _7;
    }

    public T8 _8() {
        return _8;
    }

    public T9 _9() {
        return _9;
    }

    public T10 _10() {
        return _10;
    }

    public T11 _11() {
        return _11;
    }

    public T12 _12() {
        return _12;
    }

    public T13 _13() {
        return _13;
    }

    public T14 _14() {
        return _14;
    }

    public T15 _15() {
        return _15;
    }

    public T16 _16() {
        return _16;
    }

    public T17 _17() {
        return _17;
    }

    public T18 _18() {
        return _18;
    }

    public T19 _19() {
        return _19;
    }

    public T20 _20() {
        return _20;
    }

    public T21 _21() {
        return _21;
    }


    @Override
    public String toString() {
        return "(" + _1 + "," + _2 + "," + _3 + "," + _4 + "," + _5 + "," + _6 + "," + _7 + "," + _8 + "," + _9 + "," + _10 +
                "," + _11 + "," + _12 + "," + _13 + "," + _14 + "," + _15 + "," + _16 + "," + _17 + "," + _18 + "," + _19 + "," + _20 + "," + _21 + ")";
    }


}
