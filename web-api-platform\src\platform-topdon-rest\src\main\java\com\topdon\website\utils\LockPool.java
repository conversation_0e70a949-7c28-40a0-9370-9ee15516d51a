package com.topdon.website.utils;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class LockPool {
    private static final Map<Object, LockObject> LOCK_OBJECT_MAP = new ConcurrentHashMap<>();

    static {
        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(1000 * 60);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                List<Object> keys = new ArrayList<>();
                for (Map.Entry<Object, LockObject> entry : LOCK_OBJECT_MAP.entrySet()) {
                    if (entry.getValue().isExpire()) {
                        keys.add(entry.getKey());
                    }
                }
                keys.forEach(LOCK_OBJECT_MAP::remove);
            }
        }).start();
    }

    public static synchronized LockObject getObject(Object key, long timeout, TimeUnit unit) {
        LockObject lockObject = LOCK_OBJECT_MAP.get(key);
        if (lockObject == null) {
            lockObject = new LockObject();
        }
        lockObject.setExpireAt(System.currentTimeMillis() + unit.toMillis(timeout));
        LOCK_OBJECT_MAP.put(key, lockObject);
        return lockObject;
    }

    @Data
    public static class LockObject {
        private Long expireAt;

        protected boolean isExpire() {
            return System.currentTimeMillis() > expireAt;
        }
    }
}
