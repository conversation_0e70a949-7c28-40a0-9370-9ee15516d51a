<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.website.mapper.SellerAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.topdon.website.entity.SellerAuthorization">
    <!--@mbg.generated-->
    <!--@Table seller_authorization-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="auth_cert_no" jdbcType="VARCHAR" property="authCertNo" />
    <result column="authorized_entity" jdbcType="VARCHAR" property="authorizedEntity" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="auth_type" jdbcType="INTEGER" property="authType" />
    <result column="auth_start_date" jdbcType="DATE" property="authStartDate" />
    <result column="auth_end_date" jdbcType="DATE" property="authEndDate" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="contact_address" jdbcType="VARCHAR" property="contactAddress" />
    <result column="sales_rep" jdbcType="VARCHAR" property="salesRep" />
    <result column="dealer_level" jdbcType="INTEGER" property="dealerLevel" />
    <result column="dealer_code" jdbcType="VARCHAR" property="dealerCode" />
    <result column="dealer_name" jdbcType="VARCHAR" property="dealerName" />
    <result column="parent_dealer" jdbcType="VARCHAR" property="parentDealer" />
    <result column="other_msg" jdbcType="VARCHAR" property="otherMsg" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, auth_cert_no, authorized_entity, `status`, auth_type, auth_start_date, auth_end_date, 
    contact_phone, contact_email, contact_address, sales_rep, dealer_level, dealer_code, 
    dealer_name, parent_dealer, other_msg, create_by, create_date, update_by, update_date
  </sql>
</mapper>