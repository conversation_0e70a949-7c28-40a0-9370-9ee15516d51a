package com.topdon.website.form;


import com.topdon.website.model.Subscribe;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class CooperationApplyCreateForm {
    @NotEmpty
    private String type;
    @NotEmpty
    private String email;
    @NotEmpty
    private String firstName;
    @NotEmpty
    private String lastName;
    @NotEmpty
    private String companyName;
    @NotEmpty
    private String companySize;
    @NotEmpty
    private String websiteUrl;
    @NotEmpty
    private String countryId;
    @NotEmpty
    private String address;
    @NotEmpty
    private String phoneNumber;
    private String brand;
    private String message;

    private Subscribe.SubscribeSiteFrom siteFrom;
    private boolean isSubscribe;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanySize() {
        return companySize;
    }

    public void setCompanySize(String companySize) {
        this.companySize = companySize;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getCountryId() {
        return countryId;
    }

    public void setCountryId(String countryId) {
        this.countryId = countryId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Subscribe.SubscribeSiteFrom getSiteFrom() {
        return siteFrom;
    }

    public void setSiteFrom(Subscribe.SubscribeSiteFrom siteFrom) {
        this.siteFrom = siteFrom;
    }

    public boolean isSubscribe() {
        return isSubscribe;
    }

    public void setSubscribe(boolean subscribe) {
        isSubscribe = subscribe;
    }
}
