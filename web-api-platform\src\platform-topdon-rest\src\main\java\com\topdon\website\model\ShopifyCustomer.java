package com.topdon.website.model;

import com.hiwie.breeze.util.StringUtil;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class ShopifyCustomer {
    private String email;
    private String created_at;
    private String identifier;
    private String return_to;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getReturn_to() {
        return return_to;
    }

    public void setReturn_to(String return_to) {
        this.return_to = return_to;
    }

    public static ShopifyCustomer apply(String email, long identifier) {
        return ShopifyCustomer.apply(email, identifier, "https://www.topdon.com/pages/my-profile");
    }

    public static ShopifyCustomer apply(String email, long identifier, String returnTo) {
        ShopifyCustomer customer = new ShopifyCustomer();
        customer.setEmail(email);
        customer.setIdentifier(String.valueOf(identifier));
        if (StringUtil.isNotEmpty(returnTo)) {
            customer.setReturn_to(returnTo);
        } else {
            customer.setReturn_to("https://www.topdon.com/pages/my-profile");
        }
        customer.setCreated_at(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(ZonedDateTime.now()));
        return customer;
    }
}
