import sbt.Keys._
import sbt._

object BuildSettings {

  def JavaProject(projectName: String): Project = Project(projectName, file("src/" + projectName)).settings(
    name := projectName,
    javacOptions ++= Seq("-source", "1.8", "-target", "1.8", "-encoding", "utf8", "-g", "-Xlint"),
    javacOptions in doc := Seq("-source", "1.8", "-encoding", "utf8"),
    autoScalaLibrary := false,
    crossPaths := false
  )

  def SbtPluginProject(projectName: String): Project = Project(projectName, file("src/" + projectName)).settings(
    name := projectName,
    sbtPlugin := true
  )

}