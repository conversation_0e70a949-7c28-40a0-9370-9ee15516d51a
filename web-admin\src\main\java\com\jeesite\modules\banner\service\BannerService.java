package com.jeesite.modules.banner.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.banner.entity.Banner;
import com.jeesite.modules.banner.dao.BannerDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * banner管理Service
 * <AUTHOR>
 * @version 2022-04-23
 */
@Service
@Transactional(readOnly=true)
public class BannerService extends CrudService<BannerDao, Banner> {
	
	/**
	 * 获取单条数据
	 * @param banner
	 * @return
	 */
	@Override
	public Banner get(Banner banner) {
		return super.get(banner);
	}
	
	/**
	 * 查询分页数据
	 * @param banner 查询条件
	 * @param banner.page 分页对象
	 * @return
	 */
	@Override
	public Page<Banner> findPage(Banner banner) {
		return super.findPage(banner);
	}
	
	/**
	 * 查询列表数据
	 * @param banner
	 * @return
	 */
	@Override
	public List<Banner> findList(Banner banner) {
		return super.findList(banner);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param banner
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(Banner banner) {
		super.save(banner);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(banner, banner.getId(), "banner_image");
		FileUploadUtils.saveFileUpload(banner, banner.getId(), "banner_mobile_image");
	}
	
	/**
	 * 更新状态
	 * @param banner
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(Banner banner) {
		super.updateStatus(banner);
	}
	
	/**
	 * 删除数据
	 * @param banner
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(Banner banner) {
		super.delete(banner);
	}
	
}