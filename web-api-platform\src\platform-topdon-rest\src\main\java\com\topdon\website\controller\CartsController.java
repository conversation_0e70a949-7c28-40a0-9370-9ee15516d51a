package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.security.HWSession;
import com.hiwie.security.models.Session;
import com.topdon.website.services.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/carts")
public class CartsController extends ControllerSupport {

    private final CartService cartService;

    @Autowired
    public CartsController(CartService cartService) {
        this.cartService = cartService;
    }


    @GetMapping("")
    public AbstractRestResponse list(@HWSession Session session, @RequestParam("topdonToken") String topdonToken) {
        return AbstractRestResponse.apply(cartService.list(session.getUserId(), topdonToken));
    }

    @GetMapping("/count")
    public AbstractRestResponse count(@HWSession Session session) {
        return AbstractRestResponse.apply(cartService.count(session.getUserId()));
    }


}
