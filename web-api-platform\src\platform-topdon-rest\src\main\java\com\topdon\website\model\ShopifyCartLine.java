package com.topdon.website.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

@Data
public class ShopifyCartLine {
    private Map<String, String> customAttributes;
    private String variantId;
    private int quantity;

    public static ShopifyCartLine apply(String sn, String merchandiseId, int quantity) {
        ShopifyCartLine cartLine = new ShopifyCartLine();
        Map<String, String> attr = Maps.newHashMapWithExpectedSize(1);
        attr.put("key", "sn");
        attr.put("value", sn);
        cartLine.setCustomAttributes(attr);
        cartLine.setVariantId(merchandiseId);
        cartLine.setQuantity(quantity);
        return cartLine;
    }
}
