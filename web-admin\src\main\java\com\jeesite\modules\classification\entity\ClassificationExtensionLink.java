package com.jeesite.modules.classification.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 分类拓展链接Entity
 * <AUTHOR>
 * @version 2025-07-31
 */
@Table(name="classification_extension_link", alias="a", label="分类拓展链接", columns={
		@Column(name="id", attrName="id", label="ID", isPK=true),
		@Column(name="classification_code", attrName="classificationCode", label="分类编码"),
		@Column(name="icon_default", attrName="iconDefault", label="图标(默认)", isQuery=false),
		@Column(name="icon_hover", attrName="iconHover", label="图标(鼠标移入)", isQuery=false),
		@Column(name="nav_text", attrName="navText", label="导航文案", isQuery=false),
		@Column(name="category_text", attrName="categoryText", label="二级品类文案", isQuery=false),
		@Column(name="sort_order", attrName="sortOrder", label="排序"),
		@Column(name="is_display", attrName="isDisplay", label="是否展示"),
		@Column(name="jump_link", attrName="jumpLink", label="跳转链接", isQuery=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="remarks", attrName="remarks", label="备注", isQuery=false),
	}, orderBy="a.classification_code, a.sort_order"
)
public class ClassificationExtensionLink extends DataEntity<ClassificationExtensionLink> {
	
	private static final long serialVersionUID = 1L;
	private String classificationCode;		// 分类编码
	private String iconDefault;				// 图标(默认)
	private String iconHover;				// 图标(鼠标移入)
	private String navText;					// 导航文案
	private String categoryText;			// 二级品类文案
	private Integer sortOrder;				// 排序
	private String isDisplay;				// 是否展示
	private String jumpLink;				// 跳转链接
	
	public ClassificationExtensionLink() {
		this(null);
	}

	public ClassificationExtensionLink(String id){
		super(id);
	}
	
	@NotBlank(message="分类编码不能为空")
	@Size(min=0, max=64, message="分类编码长度不能超过 64 个字符")
	public String getClassificationCode() {
		return classificationCode;
	}

	public void setClassificationCode(String classificationCode) {
		this.classificationCode = classificationCode;
	}
	
	@Size(min=0, max=1024, message="图标(默认)长度不能超过 1024 个字符")
	public String getIconDefault() {
		return iconDefault;
	}

	public void setIconDefault(String iconDefault) {
		this.iconDefault = iconDefault;
	}
	
	@Size(min=0, max=1024, message="图标(鼠标移入)长度不能超过 1024 个字符")
	public String getIconHover() {
		return iconHover;
	}

	public void setIconHover(String iconHover) {
		this.iconHover = iconHover;
	}
	
	@NotBlank(message="导航文案不能为空")
	@Size(min=0, max=50, message="导航文案长度不能超过 50 个字符")
	public String getNavText() {
		return navText;
	}

	public void setNavText(String navText) {
		this.navText = navText;
	}
	
	@NotBlank(message="二级品类文案不能为空")
	@Size(min=0, max=100, message="二级品类文案长度不能超过 100 个字符")
	public String getCategoryText() {
		return categoryText;
	}

	public void setCategoryText(String categoryText) {
		this.categoryText = categoryText;
	}
	
	@NotNull(message="排序不能为空")
	public Integer getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(Integer sortOrder) {
		this.sortOrder = sortOrder;
	}
	
	@NotBlank(message="是否展示不能为空")
	@Size(min=0, max=1, message="是否展示长度不能超过 1 个字符")
	public String getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(String isDisplay) {
		this.isDisplay = isDisplay;
	}
	
	@NotBlank(message="跳转链接不能为空")
	@Size(min=0, max=2000, message="跳转链接长度不能超过 2000 个字符")
	public String getJumpLink() {
		return jumpLink;
	}

	public void setJumpLink(String jumpLink) {
		this.jumpLink = jumpLink;
	}
	
}
