<% layout('/layouts/default.html', {title: '授权产品品线/型号管理', libs: ['dataGrid']}){ %>

<link rel="stylesheet" href="/admin/static/element-ui/index.css">
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/element-ui/index.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<script src="/admin/static/common/vue.js"></script>

<div class="main-content hidden" id="productLinesContent">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('授权产品品线/型号管理')}
            </div>
            <div class="box-tools pull-right">
                <el-button @click="hideQuery = !hideQuery">
                    <i class="fa fa-filter"></i>&nbsp;{{ hideQuery ? '查询' : '隐藏' }}
                </el-button>
                <el-button @click="expandTable(true);">
                    <i class="fa fa-angle-double-down"></i>&nbsp;展开
                </el-button>
                <el-button @click="expandTable(false)">
                    <i class="fa fa-angle-double-up"></i>&nbsp;折叠
                </el-button>
                <% if(hasPermi('product:informationGroup:edit')){ %>
                <el-button @click="showEdit = true;editData = { parentId:0,type: 0 };">
                    <i class="fa fa-plus"></i>&nbsp;新增
                </el-button>
                <% } %>
                <el-button @click="exportData">
                    <i class="fa fa-navicon"></i>&nbsp;导出
                </el-button>
                <el-popover
                        placement="bottom"
                        trigger="hover">
                    <div>
                        <el-checkbox :indeterminate="showColumn.isIndeterminate" v-model="showColumn.checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                        <div style="margin: 15px 0;"></div>
                        <el-checkbox-group v-model="showColumn.checkedColumn" @change="handleCheckedColumnChange" style="width: 130px">
                            <el-checkbox v-for="column in showColumn.allColumn" :label="column" :key="column">{{column}}</el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <el-button slot="reference">
                        <i class="fa fa-navicon"></i>
                    </el-button>
                </el-popover>
            </div>
        </div>
        <div class="box-body">
            <div class="form-inline" v-if="!hideQuery">
                <div class="form-group">
                    <label class="control-label">品线/系列/型号：</label>
                    <div class="control-inline">
                        <el-input v-model="queryData.name" class="width-120"></el-input>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-sm" @click="initProductLinesData">${text('查询')}
                    </button>
                    <button type="reset" class="btn btn-default btn-sm" @click="queryData ={};initProductLinesData()">
                        ${text('重置')}
                    </button>
                </div>
            </div>

            <el-dialog title="产品品线/型号" :visible.sync="showEdit">
                <el-form :model="editData" ref="ruleForm" label-width="130px">
                    <el-form-item v-if="editData?.type !== 0"
                                  :label="type[editData?.type - 1]?.label"
                                  prop="parentId"
                                  :rules="[{ required: true, message: type[editData?.type - 1]?.label, trigger: 'blur' }]">
                        <el-input v-model="editData.parentName" disabled="" class="width-260"></el-input>
                    </el-form-item>
                    <el-form-item label="数据类型"
                                  prop="type"
                                  :rules="[{ required: true, message: '请选择数据类型', trigger: 'blur' }]">
                        <el-select v-model="editData.type" placeholder="请选择数据类型" class="width-260"
                                   disabled="">
                            <el-option label="品线" :value="0"></el-option>
                            <el-option label="系列" :value="1"></el-option>
                            <el-option label="型号" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="名称"
                                  prop="name"
                                  :rules="[{ required: true, message: '请填写名称', trigger: 'blur' },
                                  { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }]">
                        <el-input v-model="editData.name" class="width-260"></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
                        <el-button @click="showEdit = false;">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <el-table
                    ref="table"
                    :data="tableData"
                    style="width: 100%;margin-bottom: 20px;"
                    row-key="id"
                    border
                    :tree-props="{children: 'children'}">
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('分组标题') >= 0"
                        prop="name"
                        label="品线/系列/型号 名称">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('数据类型') >= 0"
                        label="数据类型">
                    <template slot-scope="scope">
                        <el-tag :type="type[scope.row.type].type">
                            {{ type[scope.row.type].name }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('创建时间') >= 0"
                        prop="createDate"
                        label="创建时间">
                </el-table-column>
                <el-table-column
                        v-if="showColumn.checkedColumn.indexOf('更新时间') >= 0"
                        prop="updateDate"
                        label="更新时间">
                </el-table-column>

                <el-table-column
                        label="操作">
                    <template slot-scope="scope">
                        <el-link icon="fa fa-pencil" type="success" title="编辑"
                                 @click="handleEdit(scope.row)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-trash-o" type="danger" title="删除"
                                 @click="handleDelete(scope.row.id)"></el-link>&nbsp;&nbsp;
                        <el-link icon="fa fa-plus-square" type="primary" title="新增产品型号"
                                 v-if="scope.row.type < 2"
                                 @click="handleAddChild(scope.row)"></el-link>&nbsp;&nbsp;
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</div>
<script>
    var ctx = '${ctx}'
</script>
<% } %>
<script>
    js.loading()

    new Vue({
        el: '#productLinesContent',
        data: {
            showColumn:{
                checkAll:true,
                isIndeterminate: false,
                allColumn:['分组标题','数据类型','创建时间','更新时间',],
                checkedColumn:['分组标题','数据类型','创建时间','更新时间',],
            },
            editData: {},
            showEdit: false,
            tableData: [],
            queryData: {},
            hideQuery: true,
            type: {
                0: {
                    name:'品线',
                    label:'品线',
                    type:'primary'
                },
                1:{
                    name:'系列',
                    label:'产品系列',
                    type:'success'
                },
                2:{
                    name:'型号',
                    label:'产品型号',
                    type:'danger'
                }
            }
        },
        mounted() {
            js.closeLoading()
            $('#productLinesContent').removeClass('hidden')

            this.initProductLinesData()
        },
        methods: {
            handleCheckAllChange(val) {
                this.showColumn.checkedColumn = val ? this.showColumn.allColumn : [];
                this.showColumn.isIndeterminate = false;
            },
            handleCheckedColumnChange(value) {
                let checkedCount = value.length;
                this.showColumn.checkAll = checkedCount === this.showColumn.allColumn.length;
                this.showColumn.isIndeterminate = checkedCount > 0 && checkedCount < this.showColumn.allColumn.length;
            },
            expandTable(expand){
                this.$refs.table.toggleAllSelection()
                let expandTable = (arr) =>{
                    for (let arrElement of arr) {
                        this.$refs.table.toggleRowExpansion(arrElement,expand)
                        if(arrElement.children){
                            expandTable(arrElement.children)
                        }
                    }
                }
                expandTable(this.tableData)
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        axios.post(ctx + '/product/lines/saveOrUpdate', this.editData).then(res => {
                            if(res.data){
                                js.showErrorMessage(res.data)
                                return
                            }
                            this.initProductLinesData()
                            this.$message({
                                type: 'success',
                                message: '保存成功!'
                            });
                            this.showEdit = false
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            async initProductLinesData() {
                let response = await axios.post(ctx + '/product/lines/tree', this.queryData)
                this.tableData = response.data
            },
            async exportData(){
                try {
                    let response = await axios.post(
                        ctx + '/product/lines/exportData',
                        this.queryData,
                        {
                            responseType: 'arraybuffer',  // 设置响应类型为二进制数据
                        }
                    )

                    // 获取文件名，默认值为 'file.xlsx'
                    const contentDisposition = response.headers['content-disposition'];
                    let fileName = 'file.xlsx'; // 默认文件名

                    if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                        // 正则匹配 filename* 字段
                        const matches = /filename\*=([^;]*)/.exec(contentDisposition);
                        if (matches != null && matches[1]) {
                            // 提取并解码文件名
                            const encodedFileName = matches[1].split("'")[2]; // 去除编码部分 'zh_cn'
                            fileName = decodeURIComponent(encodedFileName); // 解码文件名
                        }
                    }

                    // 将返回的 arraybuffer 数据转化为 Blob
                    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

                    // 创建一个临时的 <a> 元素来触发下载
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob); // 创建 Blob 对象的 URL
                    link.download = fileName; // 指定下载文件的名称
                    link.click(); // 模拟点击以启动下载

                    // 清理 URL 对象
                    URL.revokeObjectURL(link.href);
                } catch (error) {
                    console.error('下载文件失败', error);
                }
            },
            async handleDelete(id) {
                try {
                    await this.$confirm('此操作将永久删除品线/型号, 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })

                    let res = await axios.post(ctx + '/product/lines/delete/' + id)
                    if(res.data){
                        js.showErrorMessage(res.data)
                        return
                    }
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    await this.initProductLinesData()
                } catch (e) {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                }
            },
            findByParentId(arr,id){
                for (let arrElement of arr) {
                    if (arrElement.id === id) {
                        return arrElement
                    }else if(arrElement.children){
                        let r = this.findByParentId(arrElement.children,id)
                        if(r !== null){
                            return r
                        }
                    }
                }
                return null
            },
            handleEdit(obj) {
                let findLast = this.findByParentId(this.tableData,obj.parentId);
                this.editData = {
                    ...obj,
                    parentName: findLast?.name,
                }
                this.showEdit = true
            },
            handleAddChild(obj) {
                this.editData = {
                    parentId: obj.id,
                    parentName: obj.name,
                    type: obj.type + 1
                }
                this.showEdit = true
            }
        }
    })
</script>
<style>

</style>