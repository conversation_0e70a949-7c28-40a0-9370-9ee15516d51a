package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.model.FooterMenu;
import com.topdon.website.repositories.FooterMenuRepository;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;

@Named
public class FooterMenuService {
    private final FooterMenuRepository footerMenuRepository;

    @Inject
    public FooterMenuService(FooterMenuRepository footerMenuRepository) {
        this.footerMenuRepository = footerMenuRepository;
    }

    public AbstractEither<ErrorMessage, List<FooterMenu>> list() {
        List<FooterMenu> footerMenus = footerMenuRepository.list("0");
        footerMenus.forEach(footerMenu -> {
            footerMenu.setChildren(footerMenuRepository.list(footerMenu.getCode()));
        });
        return Right.apply(footerMenus);
    }
}
