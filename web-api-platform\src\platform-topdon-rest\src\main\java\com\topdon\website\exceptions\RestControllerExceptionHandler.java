package com.topdon.website.exceptions;

import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.SCConstants;
import com.topdon.website.model.RmaOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class RestControllerExceptionHandler {

    /**
     * 系统异常拦截
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public AbstractRestResponse exceptionHandle(Exception e) {
        log.error("System Exception handle", e);
        return AbstractRestResponse.apply(Left.apply(SCConstants.SYSTEM_INNER_ERROR));
    }
}
