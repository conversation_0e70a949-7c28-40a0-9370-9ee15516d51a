package com.topdon.website.constant;

/**
 * <p>
 *  * 功能错误2开头
 *  * 第2-5位数 功能
 *  * 第 6-9位数 错误码
 *  * </p>
 *
 * <AUTHOR>
 * @since 2024/7/8 17:36
 */
public interface ErrorCode {

    int RMA_STATUS_NOT_NULL = 200010001; // RMA状态 不能为空
    int RMA_STATUS_TIME_NOT_NULL = 200010002; // 状态变更时间 不能为空
    int RMA_EMAIL_NOT_NULL = 200010003; // email不能为空
    int RMA_TICKET_NUMBER_NOT_NULL = 200010004; // email不能为空
}
