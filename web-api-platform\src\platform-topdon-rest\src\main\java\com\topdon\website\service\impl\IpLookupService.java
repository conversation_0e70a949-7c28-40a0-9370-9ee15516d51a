package com.topdon.website.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IpLookupService {

    public static void main(String[] args) {
        System.out.println(new IpLookupService().getLocation("*************"));
    }

    private static final String[] ipInfoUrls = new String[]{"https://ipwho.is/{}", "https://ipinfo.io/{}/json", "https://api.ip.sb/geoip/{}"};

    /**
     * 获取ip地址的位置信息
     * @param ip
     * @return
     */
    public String getLocation(String ip) {
        for (String ipInfoUrl : ipInfoUrls) {
            try (HttpResponse execute = HttpUtil.createGet(StrUtil.format(ipInfoUrl, ip))
                    .timeout(3000)
                    .setConnectionTimeout(3000)
                    .setReadTimeout(3000)
                    .execute()) {
                String body = execute.body();
                JSONObject entries = JSONUtil.parseObj(body);
                String country = entries.getStr("country");
                if (StrUtil.isNotBlank(country)) {
                    return country;
                }
            } catch (Exception e) {
                log.error("查询IP位置信息失败，IP：{}", ip, e);
            }
        }
        return "未知";
    }

}
