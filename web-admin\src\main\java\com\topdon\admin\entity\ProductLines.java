package com.topdon.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@TableName(value = "product_lines")
@NoArgsConstructor
public class ProductLines {



    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "parent_id")
    private Integer parentId;

    @TableField(value = "`type`")
    private Integer type;

    @TableField(value = "`name`")
    private String name;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "create_date")
    private Date createDate;

    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "update_date")
    private Date updateDate;
}