package com.topdon.admin.aop;

import com.jeesite.modules.file.entity.FileUploadParams;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class UploadMethodAspect {

    @Pointcut("execution(* com.jeesite.modules.file.web.FileUploadController.upload(com.jeesite.modules.file.entity.FileUploadParams))")
    public void uploadMethod() {}

    @Before("uploadMethod()")
    public void beforeUpload(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        FileUploadParams params = (FileUploadParams) args[0];
        params.setFileName(params.getFileName().replace(" ","_"));
    }

}
