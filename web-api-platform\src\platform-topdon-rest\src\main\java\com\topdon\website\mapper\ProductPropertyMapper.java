package com.topdon.website.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.ProductProperty;
import com.topdon.website.vo.ProductPropertyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductPropertyMapper extends BaseMapper<ProductProperty> {
    List<ProductPropertyVo> getList(@Param("classificationCode") String classificationCode,
                                                                  @Param("productId") String productId,
                                                                  @Param("draft") boolean draft);
}