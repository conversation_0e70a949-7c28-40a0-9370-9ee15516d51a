package com.topdon.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeesite.modules.sys.utils.UserUtils;
import com.topdon.admin.dto.ProductLinesDTO;
import com.topdon.admin.entity.ProductLines;
import com.topdon.admin.mapper.ProductLinesMapper;
import com.topdon.admin.service.ProductLinesService;
import com.topdon.admin.vo.ProductLinesExportVo;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ProductLinesServiceImpl extends ServiceImpl<ProductLinesMapper, ProductLines> implements ProductLinesService {

    private void recursionAppendParent(List<ProductLines> list) {
        Map<Integer, ProductLines> map = list.stream().collect(Collectors.toMap(ProductLines::getId, Function.identity(), (v1, v2) -> v1));

        List<ProductLines> productLinesList = list
                .stream()
                .filter(item -> item.getParentId() != 0 && !map.containsKey(item.getParentId()))
                .collect(Collectors.toList());
        productLinesList
                .forEach(item -> {
                    ProductLines productLines = getById(item.getParentId());
                    if (productLines != null) {
                        list.add(productLines);
                    }
                });
        if (!productLinesList.isEmpty()) {
            recursionAppendParent(list);
        }
    }

    @Override
    public List<Tree<Integer>> getTree(ProductLinesDTO productLinesDTO) {
        List<ProductLines> list = this.lambdaQuery()
                .like(StrUtil.isNotBlank(productLinesDTO.getName()), ProductLines::getName, productLinesDTO.getName())
                .list();
        recursionAppendParent(list);

        List<TreeNode<Integer>> treeNodeList = list
                .stream()
                .map(item ->
                        new TreeNode<>(item.getId(), item.getParentId(), item.getName(), item.getName())
                                .setExtra(
                                        Dict.create()
                                                .set("type", item.getType())
                                                .set("createDate", item.getCreateDate())
                                                .set("updateDate", item.getUpdateDate())
                                )
                )
                .collect(Collectors.toList());

        List<Tree<Integer>> build = TreeUtil.build(treeNodeList);
        if (build == null) {
            return new ArrayList<>();
        }
        return build;
    }

    @Override
    public String delete(Integer id) {
        this.lambdaUpdate()
                .eq(ProductLines::getParentId, id)
                .remove();

        this.removeById(id);
        return null;
    }

    @Override
    public List<ProductLinesExportVo> exportData(ProductLinesDTO productLinesDTO) {
        List<ProductLines> list = this.lambdaQuery()
                .like(StrUtil.isNotBlank(productLinesDTO.getName()), ProductLines::getName, productLinesDTO.getName())
                .list();
        recursionAppendParent(list);

        ArrayList<ProductLinesExportVo> productLinesExportVos = new ArrayList<>();

        Map<Integer, List<ProductLines>> groupByParentId = list.stream().collect(Collectors.groupingBy(ProductLines::getParentId));
        Map<Integer, ProductLines> groupById = list.stream().collect(Collectors.toMap(ProductLines::getId, Function.identity()));

        List<ProductLines> productLines = list.stream().filter(item -> item.getType() == 0).collect(Collectors.toList());
        recursionExportData(productLines, groupByParentId, groupById, productLinesExportVos);

        return productLinesExportVos;
    }

    private void recursionExportData(List<ProductLines> productLines,
                                     Map<Integer, List<ProductLines>> groupByParentId,
                                     Map<Integer, ProductLines> groupById,
                                     ArrayList<ProductLinesExportVo> productLinesExportVos) {
        for (ProductLines productLine : productLines) {
            List<ProductLines> childList = groupByParentId.get(productLine.getId());
            if (CollUtil.isNotEmpty(childList)) {
                recursionExportData(childList, groupByParentId, groupById, productLinesExportVos);
            } else {
                switch (productLine.getType()) {
                    case 2:
                        ProductLines productLines2 = groupById.get(productLine.getParentId());
                        if (productLines2 != null) {
                            ProductLines productLines3 = groupById.get(productLines2.getParentId());
                            if (productLines3 != null) {
                                productLinesExportVos.add(new ProductLinesExportVo(productLines3.getName(), productLines2.getName(), productLine.getName()));
                            }
                        }
                        break;
                    case 1:
                        ProductLines productLines1 = groupById.get(productLine.getParentId());
                        if (productLines1 != null) {
                            productLinesExportVos.add(new ProductLinesExportVo(productLines1.getName(), productLine.getName(), ""));
                        }
                        break;
                    case 0:
                        productLinesExportVos.add(new ProductLinesExportVo(productLine.getName(), "", ""));
                        break;
                }
            }
        }
    }

    @Override
    public boolean saveOrUpdate(ProductLines entity) {
        entity.setUpdateBy(UserUtils.getLoginInfo().getId());
        entity.setUpdateDate(new Date());
        if (entity.getId() == null) {
            entity.setCreateDate(new Date());
            entity.setCreateBy(UserUtils.getLoginInfo().getId());
        }
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean removeById(Serializable id) {
        this.lambdaUpdate()
                .eq(ProductLines::getParentId, id)
                .remove();
        return super.removeById(id);
    }

}
