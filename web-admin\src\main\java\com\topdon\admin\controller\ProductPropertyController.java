package com.topdon.admin.controller;

import com.topdon.admin.dto.ProductPropertyListCategoryIdDTO;
import com.topdon.admin.dto.ProductPropertySaveDTO;
import com.topdon.admin.service.ProductPropertyService;
import com.topdon.admin.vo.ProductPropertyVo;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("${adminPath}/product/property")
public class ProductPropertyController {

    @Resource
    private ProductPropertyService productPropertyService;

    @PostMapping("/save")
    public String save(@RequestBody ProductPropertySaveDTO productPropertySaveDTO) {
        productPropertyService.saveProductProperty(productPropertySaveDTO);
        return "";
    }

    @PostMapping("/importExcel")
    public String importExcel(@RequestParam("file") MultipartFile file,@RequestParam Integer compareId) {
        return productPropertyService.importExcel(file,compareId);
    }

    @PostMapping("/listByCategoryId")
    public List<ProductPropertyVo> listByCategoryId(@RequestBody ProductPropertyListCategoryIdDTO productPropertyListCategoryIdDTO) {
        return productPropertyService.listByCategoryId(productPropertyListCategoryIdDTO);
    }
}
