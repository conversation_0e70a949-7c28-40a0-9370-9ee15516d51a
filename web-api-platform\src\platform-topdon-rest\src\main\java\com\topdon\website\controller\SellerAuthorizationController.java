package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.topdon.website.service.SellerAuthorizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/seller/authorization")
public class SellerAuthorizationController {

    @Resource
    private SellerAuthorizationService sellerAuthorizationService;

    @RequestMapping("/{authCertNo}")
    public AbstractRestResponse query(@PathVariable String authCertNo){
        return AbstractRestResponse.apply(sellerAuthorizationService.getByAuthCertNo(authCertNo));
    }

}