package com.topdon.website.mappers;

import com.hiwie.breeze.AbstractOption;
import com.topdon.website.model.News;
import org.springframework.jdbc.core.RowMapper;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
public class NewsMapper {

    public static final RowMapper<News> LIST = (rs, index) -> {
        News detail = new News();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setCover(rs.getString("cover"));
        detail.setIntroduction(rs.getString("introduction"));
        detail.setCategory(News.Category.valueOf(rs.getString("category")));
        detail.setMain(rs.getBoolean("is_main"));
        detail.setPublishAt(AbstractOption.apply(rs.getTimestamp("publish_at")).map(Timestamp::toLocalDateTime).orNull());
        detail.setCreateAt(rs.getTimestamp("create_at").toLocalDateTime());
        return detail;
    };

    public static final RowMapper<News> DETAIL = (rs, index) -> {
        News detail = new News();
        detail.setId(rs.getString("id"));
        detail.setName(rs.getString("name"));
        detail.setCover(rs.getString("cover"));
        detail.setContent(rs.getString("content"));
        detail.setIntroduction(rs.getString("introduction"));
        detail.setCategory(News.Category.valueOf(rs.getString("category")));
        detail.setMain(rs.getBoolean("is_main"));
        detail.setPublishAt(AbstractOption.apply(rs.getTimestamp("publish_at")).map(Timestamp::toLocalDateTime).orNull());
        detail.setCreateAt(rs.getTimestamp("create_at").toLocalDateTime());
        return detail;
    };

}
