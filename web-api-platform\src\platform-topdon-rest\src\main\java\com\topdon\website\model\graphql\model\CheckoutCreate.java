package com.topdon.website.model.graphql.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import lombok.Data;

import java.util.List;

@Data
@JsonRootName(value = "checkoutCreate")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckoutCreate {

    public static String NODE_NAME = "checkoutCreate";

    @JsonProperty("checkout")
    private Checkout checkout;

    @JsonProperty("checkoutUserErrors")
    private List<ShopifyGraphqlError> error;
}
