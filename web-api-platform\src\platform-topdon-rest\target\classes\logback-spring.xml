<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <contextName>app</contextName>

    <springProperty scope="context" name="LOG_ROOT_LEVEL" source="logging.level.root" defaultValue="DEBUG"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!--encoder 默认配置为PatternLayoutEncoder-->
        <encoder>
            <pattern>%d [%t] %-5level %C.%method\(%L\) - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <logger name="org.springframework" level="INFO" />
    <logger name="org.apache" level="INFO" />
    <logger name="com.alibaba" level="INFO" />

    <!-- 日志只输出在控制台：用于开发环境，或 服务器环境启动时（配置中心加载logback正式配置前的日志输出） -->
    <root level="${LOG_ROOT_LEVEL}">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>