<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topdon.admin.mapper.InformationMapper">
  <resultMap id="BaseResultMap" type="com.topdon.admin.entity.Information">
    <!--@mbg.generated-->
    <!--@Table information-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="BLOB" property="content" />
    <result column="media_url" jdbcType="VARCHAR" property="mediaUrl" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="group_by" jdbcType="VARCHAR" property="groupBy" />
    <result column="other_param" jdbcType="VARCHAR" property="otherParam" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="information_group_id" jdbcType="INTEGER" property="informationGroupId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, `name`, content, media_url, create_at, update_at, download_url, `type`, 
    group_by, other_param, file_type, sort, information_group_id
  </sql>

  <select id="getPage" resultType="com.topdon.admin.vo.InformationVo">
    select *,
    p.name productName,
    IF(ig1.`name` is null ,ig3.`name`,ig1.`name`) group1,
    ig2.`name` group2,
    IF(ig1.type is null ,ig3.type,ig1.type) sortType,
    IF(ig1.sort is null ,ig3.sort,ig1.sort) sortField
    from information i
    left join product p on p.id = i.product_id
    left join information_group ig1 on ig1.id = i.information_group_id and ig1.parent_id = 0
    left join information_group ig2 on ig2.id = i.information_group_id and ig2.parent_id > 0
    left join information_group ig3 on ig3.id = ig2.parent_id
    <where>
      <if test="information.productName != null and information.productName != ''">
        p.name like concat('%',#{information.productName,jdbcType=VARCHAR},'%')
      </if>
      <if test="information.name !=null and information.name != ''">
        and i.name like concat('%',#{information.name,jdbcType=VARCHAR},'%')
      </if>
      <if test="information.type !=null and information.type != ''">
        and i.type = #{information.type,jdbcType=VARCHAR}
      </if>
      <if test="information.content !=null and information.content != ''">
        and i.content like concat('%',#{information.content,jdbcType=VARCHAR},'%')
      </if>
      <if test="information.group1 !=null and information.group1 != ''">
        and (ig1.`name` like concat('%',#{information.group1,jdbcType=VARCHAR},'%') or
        ig3.`name` like concat('%',#{information.group1,jdbcType=VARCHAR},'%'))
      </if>
      <if test="information.group2 !=null and information.group2 != ''">
        and ig2.`name` like concat('%',#{information.group2,jdbcType=VARCHAR},'%')
      </if>
    </where>
    <if test="pageVo.orderBy !=null and pageVo.orderBy != ''">
      order by ${pageVo.orderBy}
    </if>
    <if test="pageVo.orderBy ==null or pageVo.orderBy == ''">
      order by sortType,sortField,group1,ig2.sort,i.sort
    </if>
  </select>
</mapper>