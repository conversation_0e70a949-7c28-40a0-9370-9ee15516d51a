package com.jeesite.modules.recruitment.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.recruitment.entity.Recruitment;
import com.jeesite.modules.recruitment.dao.RecruitmentDao;

/**
 * 招聘管理Service
 * <AUTHOR>
 * @version 2022-04-20
 */
@Service
@Transactional(readOnly=true)
public class RecruitmentService extends CrudService<RecruitmentDao, Recruitment> {
	
	/**
	 * 获取单条数据
	 * @param recruitment
	 * @return
	 */
	@Override
	public Recruitment get(Recruitment recruitment) {
		return super.get(recruitment);
	}
	
	/**
	 * 查询分页数据
	 * @param recruitment 查询条件
	 * @param recruitment.page 分页对象
	 * @return
	 */
	@Override
	public Page<Recruitment> findPage(Recruitment recruitment) {
		return super.findPage(recruitment);
	}
	
	/**
	 * 查询列表数据
	 * @param recruitment
	 * @return
	 */
	@Override
	public List<Recruitment> findList(Recruitment recruitment) {
		return super.findList(recruitment);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param recruitment
	 */
	@Override
	@Transactional(readOnly=false)
	public void save(Recruitment recruitment) {
		super.save(recruitment);
	}
	
	/**
	 * 更新状态
	 * @param recruitment
	 */
	@Override
	@Transactional(readOnly=false)
	public void updateStatus(Recruitment recruitment) {
		super.updateStatus(recruitment);
	}
	
	/**
	 * 删除数据
	 * @param recruitment
	 */
	@Override
	@Transactional(readOnly=false)
	public void delete(Recruitment recruitment) {
		super.delete(recruitment);
	}
	
}