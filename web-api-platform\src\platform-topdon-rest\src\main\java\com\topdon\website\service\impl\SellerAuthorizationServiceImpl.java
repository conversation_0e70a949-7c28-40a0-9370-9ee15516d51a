package com.topdon.website.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.entity.*;
import com.topdon.website.mapper.SellerAuthorizationMapper;
import com.topdon.website.service.*;
import com.topdon.website.vo.SellerAuthorizationVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SellerAuthorizationServiceImpl extends ServiceImpl<SellerAuthorizationMapper, SellerAuthorization> implements SellerAuthorizationService {

    @Resource
    private AuthorizedPlatformsService authorizedPlatformsService;
    @Resource
    private RegionV1Service regionV1Service;
    @Resource
    private SellerAuthRegionMappingService sellerAuthRegionMappingService;
    @Resource
    private SellerAuthProductLinesMappingService sellerAuthProductLinesMappingService;
    @Resource
    private ProductLinesService productLinesService;

    @Override
    public AbstractEither<ErrorMessage, SellerAuthorizationVo> getByAuthCertNo(String authCertNo) {
        List<SellerAuthorization> list = lambdaQuery()
                .eq(SellerAuthorization::getAuthCertNo, authCertNo)
                .eq(SellerAuthorization::getStatus, 1)
                .orderByDesc(SellerAuthorization::getCreateDate)
                .list();
        if (list.isEmpty()) {
            return Right.apply(null);
        }
        SellerAuthorization sellerAuthorization = list.get(0);

        SellerAuthorizationVo sellerAuthorizationVo = new SellerAuthorizationVo();
        BeanUtils.copyProperties(sellerAuthorization, sellerAuthorizationVo);

        sellerAuthorizationVo.setAuthPlatform(
                authorizedPlatformsService.lambdaQuery()
                        .eq(AuthorizedPlatforms::getSellerAuthId, sellerAuthorization.getId())
                        .list()
                        .stream()
                        .map(AuthorizedPlatforms::getAuthPlatform)
                        .distinct()
                        .collect(Collectors.toList())
        );

        List<SellerAuthRegionMapping> authRegionMappingList = sellerAuthRegionMappingService.lambdaQuery()
                .eq(SellerAuthRegionMapping::getSellerAuthId, sellerAuthorization.getId())
                .list();
        if (authRegionMappingList.isEmpty()) {
            sellerAuthorizationVo.getRegion().add("Global");
        } else {
            sellerAuthorizationVo.setRegion(
                    regionV1Service.lambdaQuery()
                            .in(Region::getCode, authRegionMappingList.stream()
                                    .map(SellerAuthRegionMapping::getRegionCode).collect(Collectors.toList()))
                            .list()
                            .stream()
                            .map(Region::getName)
                            .collect(Collectors.toList())
            );
        }

        List<SellerAuthProductLinesMapping> authProductLinesMappingList = sellerAuthProductLinesMappingService.lambdaQuery()
                .eq(SellerAuthProductLinesMapping::getSellerAuthId, sellerAuthorization.getId())
                .list();
        if (!authProductLinesMappingList.isEmpty()) {
            List<ProductLines> productLines = productLinesService.lambdaQuery()
                    .in(ProductLines::getId, authProductLinesMappingList.stream().map(SellerAuthProductLinesMapping::getProductLinesId).collect(Collectors.toList()))
                    .list();
            for (ProductLines productLine : productLines) {
                SellerAuthorizationVo.ProductLines productLines1 = new SellerAuthorizationVo.ProductLines();

                switch (productLine.getType()) {
                    case 0:
                        productLines1.setProductLines(productLine.getName());
                        break;
                    case 1:
                        productLinesService.getOptById(productLine.getParentId())
                                .map(ProductLines::getName)
                                .ifPresent(productLines1::setProductLines);
                        productLines1.setProductSeries(productLine.getName());
                        break;
                    case 2:
                        ProductLines byId = productLinesService.getById(productLine.getParentId());
                        if (byId != null) {
                            productLines1.setProductSeries(byId.getName());
                            productLinesService.getOptById(byId.getParentId())
                                    .map(ProductLines::getName)
                                    .ifPresent(productLines1::setProductLines);
                        }
                        productLines1.setProductModels(productLine.getName());
                        break;
                }

                sellerAuthorizationVo.getProduct().add(productLines1);
            }
        }


        return Right.apply(sellerAuthorizationVo);
    }
}
