package com.jeesite.modules.classification.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.classification.entity.Classification;
import com.jeesite.modules.classification.entity.ClassificationExtensionLink;
import com.jeesite.modules.classification.service.ClassificationExtensionLinkService;
import com.jeesite.modules.classification.service.ClassificationService;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 分类管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-02
 */
@Controller
@RequestMapping(value = "${adminPath}/classification/classification")
public class ClassificationController extends BaseController {

    @Autowired
    private ClassificationService classificationService;

    @Autowired
    private ClassificationExtensionLinkService classificationExtensionLinkService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public Classification get(String code, boolean isNewRecord) {
        return classificationService.get(code, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("classification:classification:view")
    @RequestMapping(value = {"list", ""})
    public String list(Classification classification, Model model) {
        model.addAttribute("classification", classification);
        return "modules/classification/classificationList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("classification:classification:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public List<Classification> listData(Classification classification) {
        if (StringUtils.isBlank(classification.getParentCode())) {
            classification.setParentCode(Classification.ROOT_CODE);
        }
        if (StringUtils.isNotBlank(classification.getName())) {
            classification.setParentCode(null);
        }
        List<Classification> list = classificationService.findList(classification);
        return list;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("classification:classification:view")
    @RequestMapping(value = "form")
    public String form(Classification classification, Model model) {
        // 创建并初始化下一个节点信息
        classification = createNextNode(classification);
        model.addAttribute("classification", classification);
        return "modules/classification/classificationForm";
    }

    /**
     * 创建并初始化下一个节点信息，如：排序号、默认值
     */
    @RequiresPermissions("classification:classification:edit")
    @RequestMapping(value = "createNextNode")
    @ResponseBody
    public Classification createNextNode(Classification classification) {
        if (StringUtils.isNotBlank(classification.getParentCode())) {
            classification.setParent(classificationService.get(classification.getParentCode()));
        }
        if (classification.getIsNewRecord()) {
            Classification where = new Classification();
            where.setParentCode(classification.getParentCode());
            Classification last = classificationService.getLastByParentCode(where);
            // 获取到下级最后一个节点
            if (last != null) {
                classification.setTreeSort(last.getTreeSort() + 30);
                classification.setCode(IdGen.nextCode(last.getCode()));
            } else if (classification.getParent() != null) {
                classification.setCode(classification.getParent().getCode() + "001");
            }
        }
        // 以下设置表单默认数据
        if (classification.getTreeSort() == null) {
            classification.setTreeSort(Classification.DEFAULT_TREE_SORT);
        }
        return classification;
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("classification:classification:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated Classification classification, HttpServletRequest request) {
        classificationService.save(classification);

        // 处理拓展链接数据
        String extensionLinksJson = request.getParameter("extensionLinksJson");
        if (StringUtils.isNotBlank(extensionLinksJson)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, Object>> extensionLinksData = objectMapper.readValue(extensionLinksJson,
                    new TypeReference<List<Map<String, Object>>>() {});

                List<ClassificationExtensionLink> extensionLinks = new ArrayList<>();
                for (Map<String, Object> linkData : extensionLinksData) {
                    // 验证必填字段
                    if (linkData.get("iconDefault") == null || StringUtils.isBlank(linkData.get("iconDefault").toString())) {
                        return renderResult(Global.FALSE, text("图标(默认)必须上传"));
                    }
                    if (linkData.get("iconHover") == null || StringUtils.isBlank(linkData.get("iconHover").toString())) {
                        return renderResult(Global.FALSE, text("图标(鼠标移入)必须上传"));
                    }
                    if (linkData.get("navText") == null || StringUtils.isBlank(linkData.get("navText").toString())) {
                        return renderResult(Global.FALSE, text("导航文案不能为空"));
                    }
                    if (linkData.get("categoryText") == null || StringUtils.isBlank(linkData.get("categoryText").toString())) {
                        return renderResult(Global.FALSE, text("二级品类文案不能为空"));
                    }
                    if (linkData.get("sortOrder") == null) {
                        return renderResult(Global.FALSE, text("排序不能为空"));
                    }
                    if (linkData.get("isDisplay") == null || StringUtils.isBlank(linkData.get("isDisplay").toString())) {
                        return renderResult(Global.FALSE, text("是否展示不能为空"));
                    }
                    if (linkData.get("jumpLink") == null || StringUtils.isBlank(linkData.get("jumpLink").toString())) {
                        return renderResult(Global.FALSE, text("跳转链接不能为空"));
                    }

                    ClassificationExtensionLink link = new ClassificationExtensionLink();
                    link.setClassificationCode(classification.getCode());
                    link.setIconDefault(linkData.get("iconDefault").toString().trim());
                    link.setIconHover(linkData.get("iconHover").toString().trim());
                    link.setNavText(linkData.get("navText").toString().trim());
                    link.setCategoryText(linkData.get("categoryText").toString().trim());

                    // 安全的数字转换
                    try {
                        link.setSortOrder(Integer.valueOf(linkData.get("sortOrder").toString()));
                    } catch (NumberFormatException e) {
                        return renderResult(Global.FALSE, text("排序必须是有效的数字"));
                    }

                    link.setIsDisplay(linkData.get("isDisplay").toString().trim());
                    link.setJumpLink(linkData.get("jumpLink").toString().trim());
                    extensionLinks.add(link);
                }

                // 保存拓展链接
                classificationExtensionLinkService.saveExtensionLinks(classification.getCode(), extensionLinks);
            } catch (Exception e) {
                logger.error("保存拓展链接失败", e);
                return renderResult(Global.FALSE, text("保存拓展链接失败：" + e.getMessage()));
            }
        }

        return renderResult(Global.TRUE, text("保存分类成功！"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("classification:classification:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(Classification classification) {
        classificationService.delete(classification);
        return renderResult(Global.TRUE, text("删除分类成功！"));
    }

    /**
     * 获取树结构数据
     *
     * @param excludeCode 排除的Code
     * @param isShowCode  是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
     * @return
     */
    @RequiresPermissions("classification:classification:view")
    @RequestMapping(value = "treeData")
    @ResponseBody
    public List<Map<String, Object>> treeData(String excludeCode, String isShowCode) {
        List<Map<String, Object>> mapList = ListUtils.newArrayList();
        List<Classification> list = classificationService.findList(new Classification());
        for (int i = 0; i < list.size(); i++) {
            Classification e = list.get(i);
            // 过滤被排除的编码（包括所有子级）
            if (StringUtils.isNotBlank(excludeCode)) {
                if (e.getId().equals(excludeCode)) {
                    continue;
                }
                if (e.getParentCodes().contains("," + excludeCode + ",")) {
                    continue;
                }
            }
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("id", e.getId());
            map.put("pId", e.getParentCode());
            map.put("name", StringUtils.getTreeNodeName(isShowCode, e.getCode(), e.getName()));
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 修复表结构相关数据
     */
    @RequiresPermissions("classification:classification:edit")
    @RequestMapping(value = "fixTreeData")
    @ResponseBody
    public String fixTreeData(Classification classification) {
        if (!UserUtils.getUser().isAdmin()) {
            return renderResult(Global.FALSE, "操作失败，只有管理员才能进行修复！");
        }
        classificationService.fixTreeData();
        return renderResult(Global.TRUE, "数据修复成功");
    }

}