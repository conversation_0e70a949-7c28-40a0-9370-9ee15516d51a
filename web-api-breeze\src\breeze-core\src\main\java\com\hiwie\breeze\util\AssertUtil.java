package com.hiwie.breeze.util;

/**
 * <AUTHOR>
 */
public class AssertUtil {

    private AssertUtil() {
    }

    public static void notNull(Object object, String message) {
        isTrue(null != object, message);
    }

    public static void isFalse(boolean expression, String message) {
        isTrue(!expression, message);
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new NullPointerException(message);
        }
    }

}
