spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

server.id=PM
#server.domain=https://www.topdon.com
server.domain=http://**************:8082/api

mail.host=smtp.gmail.com
mail.port=587
mail.user_name=<EMAIL>
mail.password=ahpznayulikdyhhx

shopify.api.graphql.version=2022-10
shopify.graphql.url=https://topdon-it.myshopify.com/admin/api/2022-10/graphql.json
shopify.admin.graphql.token=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
shopify.storefront.graphql.url=https://topdon-it.myshopify.com/api/2022-10/graphql.json
shopify.storefront.graphql.token=5d6dd7a3b4d08828b52242bfacd0ca6f

support.receive.email=<EMAIL>

file.root.path=/root/filetmp/

file.zip.root.path=/opt/data
file.client.aliyun.endpoint=oss-cn-shenzhen.aliyuncs.com
file.client.aliyun.accessKeyId=lvlRALFmoww3WMKM
file.client.aliyun.accessKeySecret=xyDXRcwLgPEFYk2veamxsLIOjexbsy
file.client.aliyun.bucketName=sign-public
file.client.aliyun.root=topdon-web/
file.client.aliyun.proxyEndpoint=

topdon.url=http://api.topdon.top:8022

desk.sdk.config.fileName=zohoSdkConfig
zoho.config.enabled=true
zoho.config.clientId=1000.DWW3COBUPRKA7AP16D2FH96V0XLCVN
zoho.config.clientSecret=55e2a416fdc42e2ac9346bcde24e75ec399397b71d
zoho.config.redirectUri=http://************:8081/api/zoho/auth/callBackCode
zoho.config.timeOut=900
zoho.config.scope=Desk.tickets.ALL,Desk.tasks.ALL,Desk.settings.ALL,Desk.events.ALL,Desk.basic.ALL,Desk.contacts.ALL
zoho.config.minLogLevel=INFO
zoho.config.dc=com
zoho.config.userIdentifier=<EMAIL>
zoho.config.oauthTokensFilePath=C:\\Users\\<USER>\\Downloads