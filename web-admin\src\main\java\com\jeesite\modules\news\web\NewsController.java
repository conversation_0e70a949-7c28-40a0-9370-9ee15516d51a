package com.jeesite.modules.news.web;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.news.entity.News;
import com.jeesite.modules.news.service.NewsService;
import org.apache.logging.log4j.util.Strings;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 新闻管理Controller
 *
 * <AUTHOR>
 * @version 2022-03-03
 */
@Controller
@RequestMapping(value = "${adminPath}/news/news")
public class NewsController extends BaseController {

    private final static String STATUS_DRAFT = "1";
    private final static String STATUS_SAVE = "10";

    @Autowired
    private NewsService newsService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public News get(String id, boolean isNewRecord) {
        return newsService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("news:news:view")
    @RequestMapping(value = {"list", ""})
    public String list(News news, Model model) {
        model.addAttribute("news", news);
        return "modules/news/newsList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("news:news:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<News> listData(News news, HttpServletRequest request, HttpServletResponse response) {
        news.setPage(new Page<>(request, response));
        Page<News> page = newsService.findPage(news);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("news:news:view")
    @RequestMapping(value = "form")
    public String form(News news, Model model) {
        model.addAttribute("news", news);
        return "modules/news/newsForm";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("news:news:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated News news, HttpServletRequest request) {
        news.setNewsStatus(STATUS_SAVE);
        newsService.save(news);
        return renderResult(Global.TRUE, text("保存新闻成功！"));
    }

    /**
     * 暂存数据
     */
    @RequiresPermissions("news:news:edit")
    @PostMapping(value = "draft")
    @ResponseBody
    public String draft(News news, HttpServletRequest request) {
        news.setNewsStatus(STATUS_DRAFT);
        newsService.save(news);
        return renderResult(Global.TRUE, news.getId());
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("news:news:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(News news) {
        newsService.delete(news);
        return renderResult(Global.TRUE, text("删除新闻成功！"));
    }

}