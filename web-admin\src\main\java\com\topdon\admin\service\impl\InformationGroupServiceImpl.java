package com.topdon.admin.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeesite.modules.sys.utils.UserUtils;
import com.topdon.admin.dto.InformationGroupTreeDTO;
import com.topdon.admin.entity.InformationGroup;
import com.topdon.admin.mapper.InformationGroupMapper;
import com.topdon.admin.service.InformationGroupService;
import com.topdon.admin.service.InformationServiceV1;
import com.topdon.admin.vo.InformationGroupVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InformationGroupServiceImpl extends ServiceImpl<InformationGroupMapper, InformationGroup> implements InformationGroupService {

    @Resource
    private InformationServiceV1 informationServiceV1;


    @Override
    public List<Tree<Integer>> getTree(InformationGroupTreeDTO informationGroupTreeDTO) {
        List<InformationGroupVo> list = this.baseMapper
                .getList(informationGroupTreeDTO);

        if (StrUtil.isNotBlank(informationGroupTreeDTO.getProductName())) {
            List<InformationGroupVo> informationGroupVos = list.stream().filter(item -> StrUtil.isNotBlank(item.getProductName()) && item.getProductName().equals(informationGroupTreeDTO.getProductName()))
                    .collect(Collectors.toList());
            if (!informationGroupVos.isEmpty()) {
                list = informationGroupVos;
            } else {
                list = list.stream().filter(item -> item.getProductId() == null).collect(Collectors.toList());
            }
        }

        List<TreeNode<Integer>> treeNodeList = list
                .stream()
                .map(item ->
                        new TreeNode<>(item.getId(), item.getParentId(), item.getName(), item)
                                .setExtra(Dict.create()
                                        .set("sort", item.getSort())
                                        .set("productName", item.getProductName())
                                        .set("productId", item.getProductId())
                                        .set("type", item.getType()))
                )
                .collect(Collectors.toList());

        List<Tree<Integer>> build = TreeUtil.build(treeNodeList);
        if (build == null) {
            return new ArrayList<>();
        }
        return build;
    }

    @Override
    public String delete(Integer id) {
        List<Integer> ids = this.lambdaQuery()
                .eq(InformationGroup::getParentId, id)
                .list()
                .stream().map(InformationGroup::getId)
                .collect(Collectors.toList());
        if (informationServiceV1.getCountByGroupId(id, ids) > 0) {
            return "删除失败,存在产品资料关联";
        }

        this.removeById(id);
        return null;
    }

    @Override
    public boolean saveOrUpdate(InformationGroup entity) {
        entity.setUpdateBy(UserUtils.getLoginInfo().getId());
        entity.setUpdateDate(new Date());
        if (entity.getId() == null) {
            entity.setCreateDate(new Date());
            entity.setCreateBy(UserUtils.getLoginInfo().getId());
        }
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean removeById(Serializable id) {
        this.lambdaUpdate()
                .eq(InformationGroup::getParentId, id)
                .remove();
        return super.removeById(id);
    }

}
