<project version="4">
  <component name="ASMSmaliIdeaPluginConfiguration">
    <asm skipDebug="true" skipFrames="true" skipCode="false" expandFrames="false" />
    <groovy codeStyle="LEGACY" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/web-admin/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-security/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-website/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-breeze/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-breeze/src/breeze-dependencies/pom.xml" />
        <option value="$PROJECT_DIR$/parent/pom.xml" />
        <option value="$PROJECT_DIR$/modules/core/pom.xml" />
        <option value="$PROJECT_DIR$/modules/template/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/web-api-security/pom.xml" />
        <option value="$PROJECT_DIR$/web-api-website/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_11" default="true" project-jdk-name="11" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>