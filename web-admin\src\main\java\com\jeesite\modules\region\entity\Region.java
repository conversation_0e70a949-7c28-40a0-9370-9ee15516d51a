package com.jeesite.modules.region.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 区域管理Entity
 * <AUTHOR>
 * @version 2022-03-03
 */
@Table(name="region", alias="a", label="区域信息", columns={
		@Column(name="code", attrName="code", label="code", isPK=true),
		@Column(name="name", attrName="name", label="名字", queryType=QueryType.LIKE, isTreeName=true),
		@Column(name="flag", attrName="flag", label="国旗"),
		@Column(includeEntity=TreeEntity.class),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="remarks", attrName="remarks", label="备注", queryType=QueryType.LIKE),
	}, orderBy="a.tree_sorts, a.code"
)
public class Region extends TreeEntity<Region> {
	
	private static final long serialVersionUID = 1L;
	private String code;		// code
	private String name;		// 名字
	private String flag;		// 国旗
	
	public Region() {
		this(null);
	}

	public Region(String id){
		super(id);
	}
	
	@Override
	public Region getParent() {
		return parent;
	}

	@Override
	public void setParent(Region parent) {
		this.parent = parent;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	@NotBlank(message="名字不能为空")
	@Size(min=0, max=256, message="名字长度不能超过 256 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Size(min=0, max=1024, message="国旗长度不能超过 1024 个字符")
	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}
	
}