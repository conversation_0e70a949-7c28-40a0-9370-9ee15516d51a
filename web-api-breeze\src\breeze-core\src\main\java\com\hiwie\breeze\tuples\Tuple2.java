package com.hiwie.breeze.tuples;

/**
 * <AUTHOR>
 */
public class Tuple2<T1, T2> implements Tuple {

    private final T1 _1;
    private final T2 _2;

    Tuple2(T1 t1, T2 t2) {
        this._1 = t1;
        this._2 = t2;
    }

    public T1 _1() {
        return _1;
    }

    public T2 _2() {
        return _2;
    }

    @Override
    public String toString() {
        return "(" + _1 + "," + _2 + ")";
    }

    public Tuple2<T2, T1> swap() {
        return new Tuple2<>(this._2, this._1);
    }
}
