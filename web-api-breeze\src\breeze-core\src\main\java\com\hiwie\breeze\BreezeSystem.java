package com.hiwie.breeze;

import com.google.common.collect.Maps;
import com.hiwie.breeze.tuples.Tuple2;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BreezeSystem {

    public static final String ENTITY_NAME = "SYSTEM";

    public static class Errors {

        public static ErrorMessage INTERNAL = new ErrorMessage(BreezeConstants.MODULE, ENTITY_NAME, "INTERNAL");

        private static ErrorMessage DATA_RELATED_BY_OTHERS_INSTANCE = new ErrorMessage(BreezeConstants.MODULE, ENTITY_NAME, "DATA_RELATED_BY_OTHERS");
        public static Function<List<Tuple2<String, Long>>, ErrorMessage> DATA_RELATED_BY_OTHERS = relatedEntities -> DATA_RELATED_BY_OTHERS_INSTANCE.applyParameter("related_entities", relatedEntities.stream().map(relatedEntity -> {
            Map<String, Object> fields = Maps.newHashMapWithExpectedSize(2);
            fields.put("module", relatedEntity._1());
            fields.put("count", relatedEntity._2());
            return fields;
        }).collect(Collectors.toList()));
    }

}
