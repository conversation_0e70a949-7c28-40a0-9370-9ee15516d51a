<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.classification.dao.ClassificationDao">
	
	<!-- 查询数据
	<select id="findList" resultType="Classification">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="getByProductId" resultType="Classification">
		SELECT ${c.sqlMap.column.toSql()}
		,b.product_id productId
		FROM ${c.sqlMap.table.toSql()}
		LEFT JOIN product_classification b on a.code = b.classification_id
		<where>
			b.product_id in
			<foreach collection="productIds" open="(" item="item" close=")" separator=",">
				#{item}
			</foreach>
		</where>
	</select>
</mapper>