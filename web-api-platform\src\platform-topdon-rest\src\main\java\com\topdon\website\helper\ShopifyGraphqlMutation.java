package com.topdon.website.helper;

import org.mountcloud.graphql.request.mutation.GraphqlMutation;
import org.mountcloud.graphql.request.query.GraphqlQuery;
import org.mountcloud.graphql.request.result.ResultAttributtes;

public class ShopifyGraphqlMutation extends GraphqlMutation {
    /**
     * 不可见的构造
     *
     * @param requestName query的名字
     */
    public ShopifyGraphqlMutation(String requestName) {
        super(requestName);
    }

    @Override
    public String toString() {
        StringBuffer requestBuffer = new StringBuffer(requestName);

        //参数列表字符串
        String paramStr = getRequestParameter().toString();

        StringBuffer resultAttrBuffer = new StringBuffer("");
        boolean first = true;
        //第一个前面不拼接","
        for(ResultAttributtes ra :resultAttributes) {
            if(first) {
                first=false;
            }else{
                resultAttrBuffer.append(" ");
            }
            resultAttrBuffer.append(ra.toString());
        }

        String resultAttrStr = resultAttrBuffer.toString();

        requestBuffer.append(paramStr);
        requestBuffer.append("{");
        requestBuffer.append(resultAttrStr);
        requestBuffer.append("}");

        return "{\"query\":\"mutation{"+ requestBuffer +"}\"}";
    }
}
