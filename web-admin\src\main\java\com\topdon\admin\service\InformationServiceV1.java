package com.topdon.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeesite.common.entity.Page;
import com.topdon.admin.entity.Information;
import com.topdon.admin.vo.InformationVo;

import java.util.List;

public interface InformationServiceV1 extends IService<Information> {


    PageDTO<InformationVo> getPage(InformationVo information, Page<InformationVo> objectPage);

    Long getCountByGroupId(Integer id, List<Integer> ids);
}
