package com.topdon.website.helper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.hiwie.breeze.json.Json;
import com.topdon.website.model.CustomerAccessToken;
import com.topdon.website.model.graphql.GraphqlRequestBody;
import com.topdon.website.model.graphql.GraphqlSchemaReaderUtil;
import com.topdon.website.model.graphql.model.*;
import io.netty.handler.logging.LogLevel;
import io.netty.util.internal.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/*
 * This is a Shopify Client that uses GraphQL to retrieve data from Shopify. You should get a client object using the ShopifyGraphQLClientService
 * rather than creating your own.
 *
 * This client has a couple simple methods to retrieve basic Shop and Product data from Shopify, and a method to create a very simple new Product. You
 * can add you own methods to this client to perform more GraphQL based actions with Shopify. At a later date this may be refactored a bit to make it
 * easy to extend, to help decouple your custom code from the core framework. However, at this point, you just pull the framework into your project,
 * and make you changes directly.
 *
 * <AUTHOR>
 *
 * @since 0.0.1
 */
@Slf4j
@Data
public class ShopifyGraphQLClient {
    private static final String CONTENT_TYPE_HEADER_NAME = "Content-Type";
    private static final String SHOPIFY_ACCESS_TOKEN_HEADER_NAME = "X-Shopify-Storefront-Access-Token";
    private static final String DATA_NODE_NAME = "data";
    private static final String EXTENSIONS_NODE_NAME = "extensions";
    private static final String NODE_NAME_FIELD_NAME = "NODE_NAME";

    private String shopName;
    private String accessToken;
    private String apiVersion;

    private WebClient webClient;

    public ShopifyGraphQLClient(String shopName, String accessToken, String apiVersion) {
        this.shopName = shopName;
        this.accessToken = accessToken;
        this.apiVersion = apiVersion;

        HttpClient httpClient = HttpClient.create().wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);

        this.webClient = WebClient.builder().baseUrl("https://" + shopName + "/api/" + apiVersion + "/graphql.json")
                .defaultHeader(SHOPIFY_ACCESS_TOKEN_HEADER_NAME, accessToken)
                .defaultHeader(CONTENT_TYPE_HEADER_NAME, MediaType.APPLICATION_JSON_VALUE).clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    public CartCreateResponse createCart(CartCreate cartCreate){
        String jsonString = runQueryWrapperInput("cartCreate", Json.writeValueAsString(cartCreate));
        CartCreateResponse checkoutCreate = handleResponse(jsonString, CartCreateResponse.class);
        if (checkoutCreate != null) {
            return checkoutCreate;
        } else {
            log.error("Error createCart");
            return null;
        }
    }

    public AbstractEither<ErrorMessage, Checkout> applyDiscountCode(String checkoutId, String discountCode) {

        Map<String, String> variablesMap = Maps.newHashMapWithExpectedSize(2);
        variablesMap.put("$checkoutId", checkoutId);
        variablesMap.put("$discountCode", discountCode);
        String jsonString = runQuery("checkoutDiscountCodeApplyV2", variablesMap);
        DiscountApply checkoutCreate = handleResponse(jsonString, DiscountApply.class);
        if (checkoutCreate != null) {
            List<ShopifyGraphqlError> errors = checkoutCreate.getError();
            if (!errors.isEmpty()) {
                return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
            }
            return Right.apply(checkoutCreate.getCheckout());
        } else {
            log.error("Error applyDiscountCode");
            return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
        }
    }

    public AbstractEither<ErrorMessage, Void> cartBuyerIdentityUpdate(String cartId, String multipass,String email) {
        CartBuyerIdentityUpdate cartBuyerIdentityUpdate = new CartBuyerIdentityUpdate();
        cartBuyerIdentityUpdate.setCartId(cartId);
        CartBuyerIdentityUpdate.BuyerIdentity buyerIdentity = new CartBuyerIdentityUpdate.BuyerIdentity();
        buyerIdentity.setCustomerAccessToken(multipass);
        buyerIdentity.setEmail(email);
        cartBuyerIdentityUpdate.setBuyerIdentity(buyerIdentity);

        String jsonString = runQuery("cartBuyerIdentityUpdate", cartBuyerIdentityUpdate);
        CartBuyerIdentityUpdateResponse cartBuyerIdentityUpdateResponse = handleResponse(jsonString, CartBuyerIdentityUpdateResponse.class);
        if (cartBuyerIdentityUpdateResponse != null) {
            List<ShopifyGraphqlError> errors = cartBuyerIdentityUpdateResponse.getUserErrors();
            if (!errors.isEmpty()) {
                return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
            }
            return Right.apply(null);
        } else {
            log.error("Error cartBuyerIdentityUpdate");
            return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
        }
    }

    public AbstractEither<ErrorMessage, CustomerAccessToken> customerAccessTokenCreateWithMultipass(String multipass){
        Map<String, String> variablesMap = Maps.newHashMapWithExpectedSize(2);
        variablesMap.put("$multipassToken", multipass);
        String jsonString = runQuery("customerAccessTokenCreateWithMultipass", variablesMap);
        CustomerAccessTokenReturn customerAccessTokenReturn = handleResponse(jsonString, CustomerAccessTokenReturn.class);
        if (customerAccessTokenReturn != null) {
            List<ShopifyGraphqlError> errors = customerAccessTokenReturn.getError();
            if (!errors.isEmpty()) {
                return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
            }
            return Right.apply(customerAccessTokenReturn.getCustomerAccessToken());
        } else {
            log.error("Error customerAccessTokenCreateWithMultipass");
            return Left.apply(Checkout.Errors.DISCOUNT_UNAVAILABLE);
        }
    }


    /**
     * @param queryFileName
     * @param variablesMap
     * @return String
     */
    /*
     * This method is used to run a GraphQL query, with an optional variables file, and return the response as a JSON String.
     */
    private String runQuery(String queryFileName, Map<String, String> variablesMap) {
        String jsonString = null;
        try {
            // Load the query from a file
            String query = GraphqlSchemaReaderUtil.getSchemaFromFileName(queryFileName);
            // Try to load variables if there are any for this query
            String variablesString = GraphqlSchemaReaderUtil.getVariablesFromFileName(queryFileName);
            log.debug("variablesString: " + variablesString);

            GraphqlRequestBody graphQLRequestBody = new GraphqlRequestBody();
            graphQLRequestBody.setQuery(query);
            // If we have a variables JSON file AND the variablesMap is not empty, then we have variables to process
            if (!StringUtil.isNullOrEmpty(variablesString) && variablesMap != null) {
                for (Map.Entry<String, String> entry : variablesMap.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    variablesString = variablesString.replace(key, value);
                }
                log.debug("Variables: " + variablesString);
                graphQLRequestBody.setVariables(Json.readValue(variablesString, Map.class));
            }
            // Run the GraphQL API Call and return the JSON String response
            jsonString = webClient.post().bodyValue(graphQLRequestBody).retrieve().bodyToMono(String.class).block();
        } catch (IOException e) {
            log.error("IOException in runQuery!", e);
        }
        return jsonString;
    }

    private String runQueryWrapperInput(String queryFileName, String variablesString) {
        String jsonString = null;
        try {
            // Load the query from a file
            String query = GraphqlSchemaReaderUtil.getSchemaFromFileName(queryFileName);

            GraphqlRequestBody graphQLRequestBody = new GraphqlRequestBody();
            graphQLRequestBody.setQuery(query);
            // If we have a variables JSON file AND the variablesMap is not empty, then we have variables to process
            log.debug("Variables: " + variablesString);
            InputWrapper inputWrapper = new InputWrapper(variablesString);
            graphQLRequestBody.setVariables(inputWrapper);
            // Run the GraphQL API Call and return the JSON String response
            jsonString = webClient.post().bodyValue(graphQLRequestBody).retrieve().bodyToMono(String.class).block();
            System.out.println(jsonString);
        } catch (IOException e) {
            log.error("IOException in runQuery!", e);
        }
        return jsonString;
    }

    private String runQuery(String queryFileName, Object variables) {
        String jsonString = null;
        try {
            // Load the query from a file
            String query = GraphqlSchemaReaderUtil.getSchemaFromFileName(queryFileName);

            GraphqlRequestBody graphQLRequestBody = new GraphqlRequestBody();
            graphQLRequestBody.setQuery(query);
            // If we have a variables JSON file AND the variablesMap is not empty, then we have variables to process
            log.debug("Variables: " + variables);
            graphQLRequestBody.setVariables(variables);
            // Run the GraphQL API Call and return the JSON String response
            jsonString = webClient.post().bodyValue(graphQLRequestBody).retrieve().bodyToMono(String.class).block();
            System.out.println(jsonString);
        } catch (IOException e) {
            log.error("IOException in runQuery!", e);
        }
        return jsonString;
    }


    /**
     * @param queryFileName
     * @return String
     */
    /*
     * This method is used to run a GraphQL query and return the response as a JSON String.
     */
    private String runQueryWrapperInput(String queryFileName) {
        return runQueryWrapperInput(queryFileName, "");
    }


    /**
     * This method is used to parse the JSON response from the GraphQL API call into a Java object. It takes the object class in as a parameter and
     * returns the fully constructed object.
     * <p>
     * The Shopify response has a top level "data" object that contains the actual data inside it, and an "extensions" object contains additional
     * information.
     *
     * @param <T>
     * @param jsonString the full Shopify GraphQL response
     * @param clazz      the class literal of the object to be constructed
     * @return the fully constructed object of type clazz
     * @throws IOException
     */
    private static <T> T handleResponse(String jsonString, Class<T> clazz) {
        T objectToReturn = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonString);

            JsonNode errorNode = jsonNode.get("errors");
            if (errorNode != null) {
                log.error("Error in GraphQL API call: " + errorNode.toString());
                return null;
            }

            JsonNode extensionsNode = jsonNode.get(EXTENSIONS_NODE_NAME);
            Extensions extensions = null;
            if (extensionsNode != null) {
                extensions = objectMapper.readValue(extensionsNode.toString(), Extensions.class);
                log.debug("extensions: {}", extensions.toString());
            }

            JsonNode dataNode = jsonNode.get(DATA_NODE_NAME);
            log.debug("dataNode: {}", dataNode.toString());

            JsonNode objectNode = dataNode.get(clazz.getField(NODE_NAME_FIELD_NAME).get(null).toString());
            objectToReturn = objectMapper.readValue(objectNode.toString(), clazz);
            if (objectToReturn instanceof GraphQLResponse && extensions != null) {
                ((GraphQLResponse) objectToReturn).setExtensions(extensions);
                ((GraphQLResponse) objectToReturn).setExtensions(extensions);
            }
        } catch (NoSuchFieldException e) {
            log.error("NoSuchFieldException: {}", e.getMessage());
        } catch (SecurityException e) {
            log.error("SecurityException: {}", e.getMessage());
        } catch (IllegalAccessException e) {
            log.error("IllegalAccessException: {}", e.getMessage());
        } catch (IOException e) {
            log.error("IOException: {}", e.getMessage());
        }

        return objectToReturn;
    }

}
