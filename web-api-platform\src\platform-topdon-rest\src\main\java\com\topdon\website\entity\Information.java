package com.topdon.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "information")
public class Information {
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @TableField(value = "product_id")
    private String productId;

    @TableField(value = "`name`")
    private String name;

    @TableField(value = "content")
    private String content;

    @TableField(value = "media_url")
    private String mediaUrl;

    @TableField(value = "create_at")
    private Date createAt;

    @TableField(value = "update_at")
    private Date updateAt;

    @TableField(value = "download_url")
    private String downloadUrl;

    @TableField(value = "`type`")
    private String type;

    @TableField(value = "group_by")
    private String groupBy;

    @TableField(value = "other_param")
    private String otherParam;

    @TableField(value = "file_type")
    private String fileType;

    @TableField(value = "sort")
    private Integer sort;

    @TableField(value = "information_group_id")
    private Integer informationGroupId;
}