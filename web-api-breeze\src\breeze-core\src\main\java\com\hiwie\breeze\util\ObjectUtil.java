package com.hiwie.breeze.util;

import java.io.*;

/**
 * <AUTHOR>
 */
public class ObjectUtil {

    public static boolean isNull(Object object) {
        return null == object;
    }

    public static boolean notNull(Object object) {
        return !isNull(object);
    }

    public static byte[] serialize(Object obj) {
        ObjectOutput out;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            out = new ObjectOutputStream(bos);
            out.writeObject(obj);
            out.flush();
            return bos.toByteArray();
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T deserialize(byte[] bytes) {
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        try (ObjectInput in = new ObjectInputStream(bis)) {
            return (T) in.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new IllegalArgumentException(e);
        }
    }

}
