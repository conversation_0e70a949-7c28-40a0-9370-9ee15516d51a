package com.topdon.website.mappers;

import com.topdon.website.model.Dict;
import org.springframework.jdbc.core.RowMapper;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/8 14:32
 */
public class DictMapper {

    public static final RowMapper<Dict> DETAIL = (rs, index) -> {
        Dict dict = new Dict();
        dict.setId(rs.getString("dict_code"));
        dict.setType(rs.getString("dict_type"));
        dict.setValue(rs.getString("dict_value"));
        dict.setLabel(rs.getString("dict_label"));
        dict.setSort(rs.getString("tree_sort"));
        dict.setExtendS1(rs.getString("extend_s1"));
        return dict;
    };
}
