package com.topdon.website.services;

import com.google.common.collect.Lists;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Left;
import com.hiwie.breeze.Right;
import com.topdon.website.form.CartForm;
import com.topdon.website.form.SkuUpdateForm;
import com.topdon.website.model.Cart;
import com.topdon.website.model.CartGroup;
import com.topdon.website.model.Device;
import com.topdon.website.repositories.CartRepository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Named
public class CartService {

    private final CartRepository cartRepository;
    private final ProductServices productService;
    private final TransactionTemplate transactionTemplate;

    @Inject
    public CartService(CartRepository cartRepository, ProductServices productService, TransactionTemplate transactionTemplate) {
        this.cartRepository = cartRepository;
        this.productService = productService;
        this.transactionTemplate = transactionTemplate;
    }

    public AbstractEither<ErrorMessage, Long> create(long userId, CartForm cartForm) {
        if (cartRepository.isExist(userId, cartForm.getSn(), cartForm.getProductId())) {
            return Left.apply(Cart.Errors.EXISTS);
        }
        return Right.apply(cartRepository.create(userId, cartForm));
    }

    public AbstractEither<ErrorMessage, Cart> get(long id) {
        return cartRepository.get(id).toRight(Cart.Errors.NOT_FOUND);
    }

    public AbstractEither<ErrorMessage, List<CartGroup>> list(long userId, String topdonToken) {
        List<Cart> carts = cartRepository.list(userId);
        carts.forEach(cart -> productService.getTopdon(cart.getProductId() + "", cart.getSn(), topdonToken).ifRight(topdonResponse -> {
            cart.setDetail(topdonResponse.getData());
        }));
        Map<String, List<Cart>> map = carts.stream().collect(Collectors.groupingBy(cart -> cart.getSn() + "=" + cart.getSnName()));
        List<CartGroup> cartGroups = Lists.newArrayList();
        map.forEach((k, v) -> cartGroups.add(new CartGroup(Device.apply(k.split("=")[0], k.split("=")[1]), v)));
        return Right.apply(cartGroups);
    }

    public AbstractEither<ErrorMessage, List<Cart>> listCarts(long userId) {
        List<Cart> carts = cartRepository.list(userId);
        carts.forEach(cart -> productService.getTopdon(cart.getProductId() + "", cart.getSn(), "").ifRight(topdonResponse -> {
            cart.setDetail(topdonResponse.getData());
        }));
        return Right.apply(carts);
    }

    public AbstractEither<ErrorMessage, Long> count(long userId) {
        return Right.apply(cartRepository.count(userId));
    }

    public AbstractEither<ErrorMessage, Integer> delete(long userId, List<Long> cartIds) {
        return transactionTemplate.execute(status -> {
            for (Long cartId : cartIds) {
                delete(userId, cartId);
            }
            return Right.apply(1);
        });
    }

    public AbstractEither<ErrorMessage, Integer> delete(long userId, long id) {
        return Right.apply(cartRepository.delete(userId, id));
    }

    public AbstractEither<ErrorMessage, Integer> updateSku(long id, SkuUpdateForm form) {
        return Right.apply(cartRepository.updateSku(id, form));
    }

}
