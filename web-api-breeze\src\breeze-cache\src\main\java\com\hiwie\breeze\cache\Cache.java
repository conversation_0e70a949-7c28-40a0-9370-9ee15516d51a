package com.hiwie.breeze.cache;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Cache client
 *
 * <AUTHOR>
 */
public class Cache {

    private final JedisPool pool;

    public Cache(JedisPool pool) {
        this.pool = pool;
    }

    public <T> T apply(Function<Jedis, T> func) {
        Jedis jedis = pool.getResource();
        T result = func.apply(jedis);
        jedis.close();
        return result;
    }

    public void accept(Consumer<Jedis> consumer) {
        Jedis jedis = pool.getResource();
        consumer.accept(jedis);
        jedis.close();
    }

}
