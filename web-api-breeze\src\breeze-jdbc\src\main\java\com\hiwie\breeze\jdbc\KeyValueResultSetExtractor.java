package com.hiwie.breeze.jdbc;

import com.google.common.collect.Maps;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class KeyValueResultSetExtractor {

    public static ResultSetExtractor<Map<String, String>> apply() {
        return (rs) -> {
            Map<String, String> result = Maps.newHashMap();
            while (rs.next()) {
                result.put(rs.getString(1), rs.getString(2));
            }
            return result;
        };
    }

}