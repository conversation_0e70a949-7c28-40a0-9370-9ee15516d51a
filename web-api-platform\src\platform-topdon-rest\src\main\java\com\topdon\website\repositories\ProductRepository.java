package com.topdon.website.repositories;

import cn.hutool.core.collection.CollectionUtil;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.jdbc.MysqlJDBCSupport;
import com.hiwie.breeze.util.StringUtil;
import com.topdon.website.form.ProductQueryForm;
import com.topdon.website.mappers.ProductCompareMapper;
import com.topdon.website.mappers.ProductExtensionMapper;
import com.topdon.website.mappers.ProductMapper;
import com.topdon.website.mappers.ProductOverviewMapper;
import com.topdon.website.model.Classification;
import com.topdon.website.model.Product;
import com.topdon.website.vo.ProductExtensionVo;
import com.topdon.website.vo.ProductVo;
import org.apache.commons.lang3.StringUtils;
import org.intellij.lang.annotations.Language;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.*;
import java.util.stream.Collectors;

@Named
public class ProductRepository extends MysqlJDBCSupport {

    @Inject
    protected ProductRepository(JdbcTemplate db, NamedParameterJdbcTemplate namedDB) {
        super(db, namedDB);
    }

    public AbstractOption<Product> option(String id) {
        @Language("SQL") String sql = "select p.id, p.name, p.cover, p.discontinued, d.classification_id class_id,GROUP_CONCAT(d.classification_id) class_id_concat,c.name class_name, p.description, p.create_at,p.search_cover,p.mobile_cover,p.new_product from product p left join product_classification d on d.product_id = p.id left join classification c on d.classification_id = c.code where id=:id group by p.id";
        MapSqlParameterSource param = new MapSqlParameterSource("id", id);
        AbstractOption<Product> productAbstractOption = option(sql, ProductMapper.DETAIL, param);
        setClassificationList(Arrays.asList(productAbstractOption.get()));
        return productAbstractOption;
    }

    public List<Product> list(ProductQueryForm queryForm) {
        @Language("SQL") String sql = "select p.id, p.name, p.cover, p.discontinued, d.classification_id class_id,GROUP_CONCAT(d.classification_id) class_id_concat,c.name class_name, p.description, p.create_at,p.search_cover,p.mobile_cover,p.new_product,p.discontinued from product p left join product_classification d on d.product_id = p.id left join classification c on d.classification_id = c.code where 1=1";
        MapSqlParameterSource param = new MapSqlParameterSource();
        sql += queryForm.getClassId().map(classId -> {
            param.addValue("classId", classId);
            return " AND d.classification_id = :classId";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getName().map(name -> {
            param.addValue("name", like(name.toUpperCase()));
            return " AND upper(p.name) like :name";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getSearchView().map(b -> {
            param.addValue("search_view", b ? 1 : 0);
            return " AND p.search_view = :search_view";
        }).getOrElse(StringUtil.EMPTY);

        sql += queryForm.getRmaToolShow().map(b -> {
            param.addValue("rma_tool_show", b ? 1 : 0);
            return " AND p.rma_tool_show = :rma_tool_show";
        }).getOrElse(StringUtil.EMPTY);

        sql += " group by p.id order by p.sort";
        return setClassificationList(list(sql, ProductMapper.DETAIL, param));
    }

    public List<ProductExtensionVo> extension(String productName, Boolean draft) {
        @Language("SQL") String sql = "select pe.id, pe.product_id, p.name product_name, pe.name, pe.content, if((select count(1) from product_property where product_id = pe.product_id and draft = :draft limit 1)>0,true,false) compare,(select cc.classification_code from product_property pp left join classification_compare cc on cc.id = pp.classification_compare_id where pp.product_id = p.id and pp.draft = :draft and cc.draft = :draft limit 1) compare_classification_code  from product_extension pe left join product p on p.id = pe.product_id left join product_classification pc on pc.product_id = pe.product_id where p.name = :productName";
        MapSqlParameterSource param = new MapSqlParameterSource();
        param.addValue("productName", productName);
        param.addValue("draft", Optional.ofNullable(draft).orElse(false));
        return list(sql, ProductExtensionMapper.DETAIL_VO, param);
    }

    public List<Map<String, Object>> overview(String productName) {
        @Language("SQL") String sql = "select po.id, po.product_id,p.name product_name, po.type, po.`desc`,po.image,po.title,po.background,po.title_color,po.desc_color from product_overview po left join product p on p.id = po.product_id where p.name = :productName";
        MapSqlParameterSource param = new MapSqlParameterSource();
        param.addValue("productName", productName);
        return list(sql, ProductOverviewMapper.DETAIL, param);
    }

    public List<Map<String, Object>> compare(String productName) {
        @Language("SQL") String sql = "select pc.id, pc.product_id,p.name product_name, pc.type, pc.name,pc.image,pc.param from product_compare pc left join product p on p.id = pc.product_id where p.name = :productName";
        MapSqlParameterSource param = new MapSqlParameterSource();
        param.addValue("productName", productName);
        return list(sql, ProductCompareMapper.DETAIL, param);
    }

    public List<Product> relation(String productName) {
        @Language("SQL") String sql = "select p.id, p.name, p.cover, p.discontinued, d.classification_id class_id,GROUP_CONCAT(d.classification_id) class_id_concat,c.name class_name, p.description, p.create_at,p.search_cover,p.mobile_cover,p.new_product from product p left join product_classification d on d.product_id = p.id left join classification c on d.classification_id = c.code where p.id in (select relation_product_id from product_relation pr left join product p2 on p2.id = pr.product_id where p2.name = :productName) group by p.id;";
        MapSqlParameterSource param = new MapSqlParameterSource();
        param.addValue("productName", productName);
        return setClassificationList(list(sql, ProductMapper.DETAIL, param));
    }

    public List<Product> recommend() {
        @Language("SQL") String sql = "select p.id, p.name, p.cover, p.discontinued, d.classification_id class_id,GROUP_CONCAT(d.classification_id) class_id_concat,c.name class_name, p.description, p.create_at,p.search_cover,p.mobile_cover,p.new_product from product_recommend pr  left join product p on p.id = pr.product_id left join product_classification d on d.product_id = p.id left join classification c on d.classification_id = c.code where p.id is not null group by p.id order by pr.sort";
        return setClassificationList(list(sql, ProductMapper.DETAIL));
    }

    public List<ProductVo> getByName(String productName, String draft) {
        @Language("SQL") String sql = "select p.id, if((select count(1) from product_property where product_id = p.id and draft = :draft limit 1)>0,true,false) compare,(select cc.classification_code from product_property pp left join classification_compare cc on cc.id = pp.classification_compare_id where pp.product_id = p.id and pp.draft = :draft and cc.draft = :draft limit 1) compare_classification_code from product p where p.name = :productName limit 1";
        MapSqlParameterSource param = new MapSqlParameterSource();
        param.addValue("productName", productName);
        param.addValue("draft", draft);
        return list(sql, ProductMapper.Product, param);
    }

    private List<Product> setClassificationList(List<Product> products) {
        Optional.ofNullable(products).filter(CollectionUtil::isNotEmpty).map(l -> l.stream().map(Product::getClassIdConcat).filter(StringUtils::isNotEmpty).map(o -> Arrays.asList(o.split(","))).flatMap(Collection::stream).collect(Collectors.toList())).filter(CollectionUtil::isNotEmpty).ifPresent(l -> {
            @Language("SQL") String sql1 = "select d.product_id productId ,c.code,c.`name` from product_classification d left join classification c on d.classification_id = c.`code` where c.code in (:codes)";
            MapSqlParameterSource param1 = new MapSqlParameterSource();
            param1.addValue("codes", l);
            List<Classification> classifications = list(sql1, (rs, b) -> Classification.defaultPk(rs.getString("code"), rs.getString("name"), rs.getString("productId")), param1);
            Map<String, List<Classification>> map = classifications.stream().collect(Collectors.groupingBy(Classification::getProductId));
            products.stream().forEach(o -> Optional.ofNullable(map.get(o.getId())).ifPresent(o::setClassificationList));
        });
        return products;
    }
}
