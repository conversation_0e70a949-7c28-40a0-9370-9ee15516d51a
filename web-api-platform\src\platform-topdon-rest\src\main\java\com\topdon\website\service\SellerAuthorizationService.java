package com.topdon.website.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.SellerAuthorization;
import com.topdon.website.vo.SellerAuthorizationVo;

public interface SellerAuthorizationService extends IService<SellerAuthorization>{


    AbstractEither<ErrorMessage, SellerAuthorizationVo> getByAuthCertNo(String authCertNo);
}
