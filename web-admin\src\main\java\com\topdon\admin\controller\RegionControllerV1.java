package com.topdon.admin.controller;

import cn.hutool.core.lang.tree.Tree;
import com.topdon.admin.service.RegionServiceV1;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping(value = "${adminPath}/v1/region")
public class RegionControllerV1 {

    @Resource
    private RegionServiceV1 regionServiceV1;

    @ResponseBody
    @GetMapping("/tree")
    public List<Tree<String>> tree() {
        return regionServiceV1.getTree();
    }
}
