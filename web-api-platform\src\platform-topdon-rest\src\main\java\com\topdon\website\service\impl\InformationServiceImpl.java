package com.topdon.website.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.entity.Information;
import com.topdon.website.form.InformationQueryForm;
import com.topdon.website.mapper.InformationMapper;
import com.topdon.website.service.InformationService;
import com.topdon.website.vo.InformationGroupVo;
import com.topdon.website.vo.InformationVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InformationServiceImpl extends ServiceImpl<InformationMapper, Information> implements InformationService {


    @Override
    public AbstractEither<ErrorMessage, List<Tree<Integer>>> getList(InformationQueryForm form) {
        List<InformationGroupVo> list = this.baseMapper.getList(form);

        List<InformationGroupVo> list1 = list.stream()
                .filter(item -> StrUtil.equals(item.getProductName(), form.getProductName()) || item.getParentId() == 0)
                .collect(Collectors.toList());
        if (list1.isEmpty()) {
            list = list.stream()
                    .filter(item -> item.getProductId() == null).collect(Collectors.toList());
        } else {
            list = list1;
        }

        List<TreeNode<Integer>> treeNodeList = list.stream()
                .map(item -> {
                    TreeNode<Integer> tree = new TreeNode<>(item.getId(), item.getParentId(), item.getName(), item.getSort());
                    List<JSONObject> data = item.getInformationVos().stream().map(item1 -> {
                        JSONObject jsonObject = JSONUtil.parseObj(item1);
                        jsonObject.putOpt("mediaUrl", Dict.create().set("option", item1.getMediaUrl()));
                        if (StrUtil.isNotBlank(item1.getOtherParam())) {
                            jsonObject.putOpt("otherParams", JSONUtil.parseArray(item1.getOtherParam()));
                        }
                        return jsonObject;
                    }).collect(Collectors.toList());
                    tree.setExtra(Dict.create()
                            .set("data", data)
                            .set("compare", item.getCompare())
                            .set("compareClassificationCode", item.getCompareClassificationCode())
                            .set("productId", item.getProduct_id()));
                    return tree;
                }).collect(Collectors.toList());


        List<Tree<Integer>> build = TreeUtil.build(treeNodeList);

        return Right.apply(build);
    }

    @Override
    public AbstractEither<ErrorMessage, List<Dict>> search(InformationQueryForm form) {
        Map<String, List<InformationVo>> map = this.baseMapper.search(form)
                .stream()
                .collect(Collectors.groupingBy(InformationVo::getGroupName));

        return Right.apply(map.entrySet().stream().map(item->Dict.create()
                .set("groupBy",item.getKey())
                .set("data",item.getValue().stream().map(item1->{
                    JSONObject jsonObject = JSONUtil.parseObj(item1);
                    jsonObject.putOpt("mediaUrl",Dict.create().set("option",item1.getMediaUrl()));
                    if(StrUtil.isNotBlank(item1.getOtherParam())){
                        jsonObject.putOpt("otherParams",JSONUtil.parseArray(item1.getOtherParam()));
                    }
                    return jsonObject;
                }).collect(Collectors.toList())))
                .collect(Collectors.toList()));
    }
}
