//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.jeesite.common.image;

import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.StringUtils;
import net.coobird.thumbnailator.Thumbnails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class ImageUtils {
    private static Logger logger = LoggerFactory.getLogger(ImageUtils.class);

    public ImageUtils() {
    }

    public static void thumbnails(File imageFile, int maxWidth, int maxHeight, String outputFormat) {
        if (imageFile != null && imageFile.exists() && (maxWidth > 0 || maxHeight > 0)) {
            String extension = FileUtils.getFileExtension(imageFile.getName());
            if (StringUtils.inString(extension, new String[]{"png", "jpg", "jpeg", "bmp", "ico"})) {
                try {
                    BufferedImage bufferedImage = ImageIO.read(imageFile);
                    Thumbnails.Builder<File> bilder = Thumbnails.of(imageFile);
                    if (bufferedImage != null) {
                        if (maxWidth > 0) {
                            if (bufferedImage.getWidth() <= maxWidth) {
                                bilder.width(bufferedImage.getWidth());
                            } else {
                                bilder.width(maxWidth);
                            }
                        }

                        if (maxHeight > 0) {
                            if (bufferedImage.getHeight() <= maxHeight) {
                                bilder.height(bufferedImage.getHeight());
                            } else {
                                bilder.height(maxHeight);
                            }
                        }

                        if (StringUtils.isNotBlank(outputFormat)) {
                            bilder.outputFormat(outputFormat);
                        }

                        bilder.toFile(imageFile);
                    }
                } catch (IOException e) {
                    logger.error("图片压缩失败：" + imageFile.getAbsoluteFile(), e);
                }

            }
        }
    }
}
