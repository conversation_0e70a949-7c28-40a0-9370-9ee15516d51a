<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/modules/core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/modules/template/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-application/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-application/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-cache/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-cache/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-context/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-context/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-dependencies/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-dependencies/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-import/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-import/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-jdbc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-jdbc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-json/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-json/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-repository/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-repository/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-rest/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-rest/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-scheduler/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-scheduler/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-validator/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-breeze/src/breeze-validator/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-management-rest/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-management-rest/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-rest/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-rest/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-spring-integration/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-spring-integration/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-security/src/security-test/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-rest/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-rest/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/web-api-website/src/website-service/src/main/resources" charset="UTF-8" />
  </component>
</project>