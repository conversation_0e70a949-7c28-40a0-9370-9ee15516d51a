<% layout('/layouts/default.html', {title: '数据管理', libs: ['validate','fileupload','ueditor','dataGrid']}){ %>
<div class="main-content print-form">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-notebook"></i> 表格表单实例
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${testData}" action="${ctx}/test/testData/save" method="post" class="form-horizontal">
			<div class="table-fdiv width-960">
 				<h3>产品信息情况</h3>
				<table class="table-form">
					<tr class="form-title"><td colspan="6">基本信息</td></tr>
					<tr>
						<td class="form-label" width="150"><span class="required">*</span> 负责人</td>
						<td>
							<#form:input path="testInput" maxlength="200" class="form-control required" defaultValue="小王"/>
						</td>
						<td class="form-label" width="150">所属部门</td>
						<td>
							<#form:treeselect id="testOffice" title="机构选择"
									path="testOffice.officeCode" labelPath="testOffice.officeName"
									url="${ctx}/sys/office/treeData" defaultLabel="技术部"
									class="required" allowClear="true"/>
						</td>
						<td rowspan="3" width="110">
							<div style="width:110px;height:110px;overflow:hidden;">
								<#form:fileupload id="uploadImage" bizKey="${testData.id}" bizType="testData_image"
									uploadType="image" class="" readonly="false" preview="true"
									maxUploadNum="1" isMini="true"/>
							</div>
						</td>
					</tr>
					<tr>
						<td class="form-label">单行文本</td>
						<td>
							<#form:input path="testInput" maxlength="200" class="form-control" defaultValue="演示文本"/>
						</td>
						<td class="form-label">单行文本</td>
						<td>
							<#form:input path="testInput" maxlength="200" class="form-control" defaultValue="演示文本"/>
						</td>
					</tr>
					<tr>
						<td class="form-label">下拉框</td>
						<td>
							<#form:select path="testSelect" dictType="sys_menu_type" blankOption="true" class="form-control" defaultValue="1"/>
						</td>
						<td class="form-label">下拉多选</td>
						<td>
							<#form:select path="testSelectMultiple" dictType="sys_menu_type" multiple="true" blankOption="true" class="form-control" defaultValue="2"/>
						</td>
					</tr>
					<tr>
						<td class="form-label">单选框</td>
						<td>
							<#form:radio path="testRadio" dictType="sys_menu_type" class="form-control" defaultValue="1"/>
						</td>
						<td class="form-label">复选框</td>
						<td colspan="2">
							<#form:checkbox path="testCheckbox" dictType="sys_menu_type" class="form-control" defaultValue="1"/>
						</td>
					</tr>
					<tr>
						<td class="form-label">日期选择</td>
						<td>
							<#form:input path="testDate" readonly="true" maxlength="20" class="form-control laydate "
									dataFormat="date" data-type="date" data-format="yyyy-MM-dd" defaultValue="${date()}"/>
						</td>
						<td class="form-label">日期时间</td>
						<td colspan="2">
							<#form:input path="testDatetime" readonly="true" maxlength="20" class="form-control laydate "
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}"/>
						</td>
					</tr>
					<tr>
						<td class="form-label">团队人数</td>
						<td colspan="4">共 <input class="text-center width-60" value="100"/> 人。其中技术人员
							 <input class="text-center width-60 digits" value="60"/> 人。
						</td>
					</tr>
					<tr>
						<td class="form-label">办公面积</td>
						<td><input class="width-60 text-center number" value="200"/> M²</td>
						<td class="form-label">服务户数</td>
						<td colspan="2"><input class="width-60 text-center digits" value="9999"/> 家</td>
					</tr>
					<tr class="form-title"><td colspan="6">详细信息</td></tr>
					<tr>
						<td colspan="6">
							<table class="table-form">
								<tr>
									<td class="form-label">项目编号</td>
									<td><input class="form-control" value="1234567890123"/></td>
									<td class="form-label">项目名称</td>
									<td><input class="form-control" value="JeeSite"/></td>
									<td class="form-label">项目版本</td>
									<td><input class="form-control" value="V4.3.0"/></td>
								</tr>
								<tr>
									<td class="form-label">新增产值</td>
									<td><input class="width-60 text-right" value="0.00"/> 万元</td>
									<td class="form-label">新增销售额</td>
									<td><input class="width-60 text-right" value="0.00"/> 万元</td>
									<td class="form-label">新增交税总额</td>
									<td><input class="width-60 text-right" value="0.00"/> 万元</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td class="form-label">内容简介</td>
						<td colspan="4">
							<textarea name="remarks" rows="5" class="form-control autoHeight">这是一个文本，支持自动高度，在这里按回车看效果</textarea>
						</td>
					</tr>
					<tr>
						<td class="form-label" nowrap="nowrap">子表数据</td>
						<td colspan="4">
							<table id="testDataChildDataGrid"></table>
							<a href="#" id="testDataChildDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> 增行</a>
						</td>
					</tr>
				</table>
				<table class="table-form mt10">
					<tr class="form-title"><td colspan="4">附件</td></tr>
					<tr>
						<td colspan="4">
							<#form:fileupload id="uploadFile" bizKey="${testData.id}" bizType="testData_file"
									uploadType="all" class="" readonly="false" preview="true"/>
						</td>
					</tr>
				</table>
				<div class="form-actions">
					<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> 保 存</button>&nbsp;
					<button type="button" class="btn btn-sm btn-info" id="btnPrint" onclick="window.print()"><i class="fa fa-print"></i> 打 印</button>&nbsp;
					<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> 关 闭</button>
				</div>
			</div>
		
		</#form:form>
	</div>
</div>
<% } %>
<script>
//初始化测试数据子表DataGrid对象
$("#testDataChildDataGrid").dataGrid({

	data: ${toJson(testData.testDataChildList)},
	datatype: "local", // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	
	// 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'单行文本', name:'testInput', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'200', 'class':'form-control'}},
		{header:'多行文本', name:'testTextarea', width:100, editable:true, edittype:'textarea', editoptions:{'maxlength':'200', 'class':'form-control', 'rows':'1'}},
		{header:'下拉框', name:'testSelect', width:100, 
			editable:true, edittype:'select', editoptions:{'class':'form-control',
				items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], ${@DictUtils.getDictListJson('sys_menu_type')}),
				itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
					js.select2(element).on("change",function(){$(this).resetValid()});
				}
			}
		},
		{header:'日期选择', name:'testDate', width:100, 
			formatter:'date', formatoptions:{srcformat:'Y-m-d H:i:s',newformat:'Y-m-d'},
			editable:true, edittype:'text', editoptions:{'class':'form-control laydate ', 'readonly':'true',
				dataInit: function(element){
					laydate.render({elem:element, type:'date', format:'yyyy-MM-dd'});
				}
			}
		},
		{header:'用户选择', name:'testUser', width:100,
			formatter: function(val, obj, row, act){
				return js.val(row, 'testUser.userCode')+'|'+js.val(row, 'testUser.userName');
			}, editable: true, edittype: "custom", editoptions: {
				custom_element: function(val, editOptions) {
					return js.template('treeselectTpl', {
						id: 'user_'+editOptions.id, title: '用户选择', 
						name: 'testUser.userCode', value: val.split('|')[0], 
						labelName: 'testUser.userName', labelValue: val.split('|')[1],
						url: '${ctx}/sys/office/treeData?isLoadUser=true', cssClass: ''
					});
				}
			}
		},
		{header:'操作', name:'actions', width:80, sortable:false, fixed:true, formatter: function(val, obj, row, act){
			var actions = [];
			if (val == 'new'){
				actions.push('<a href="#" onclick="js.confirm(\'你确认要删除这条数据吗？\', function(){$(\'#testDataChildDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
			}else{
				actions.push('<a href="#" onclick="js.confirm(\'你确认要删除这条数据吗？\', function(){$(\'#testDataChildDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'})});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');return false;"><i class="fa fa-trash-o"></i></a>&nbsp;');
			}
			return actions.join('');
		}, editoptions: {defaultValue: 'new'}}
	],
	
	// 编辑表格参数
	editGrid: true,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#testDataChildDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上 v4.1.7
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据
	
	// 编辑表格的提交数据参数
	editGridInputFormListName: 'testDataChildList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,testSort,testData.id,testInput,testTextarea,testSelect,testSelectMultiple,testRadio,testCheckbox,testDate,testDatetime,testUser.userCode,testOffice.officeCode,testAreaCode,testAreaName,', // 提交数据列表的属性字段
	
	// 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>
<script id="treeselectTpl" type="text/template">//<!--<div>
<#form:treeselect id="{{d.id}}" title="{{d.title}}" name="{{d.name}}" value="{{d.value}}"
	labelName="{{d.labelName}}" labelValue="{{d.labelValue}}" url="{{d.url}}"
	class="{{d.cssClass}}" btnClass="btn-sm" allowClear="true"/>
</div>//--></script>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
// 		js.ajaxSubmitForm($(form), function(data){
// 			js.showMessage(data.message);
// 			if(data.result == Global.TRUE){
// 				js.closeCurrentTabPage(function(contentWindow){
// 					contentWindow.page();
// 				});
// 			}
// 		}, "json");
		js.showMessage('模拟保存成功');
    }
});
</script>