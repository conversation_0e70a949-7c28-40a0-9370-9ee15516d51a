package com.topdon.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.admin.dto.MenuClickLogListDto;
import com.topdon.admin.dto.MenuClickLogPageDto;
import com.topdon.admin.entity.MenuClickLog;
import com.topdon.admin.vo.MenuClickLogExcelVo;

import java.util.List;

public interface MenuClickLogService extends IService<MenuClickLog>{


    PageDTO<MenuClickLog> getPage(MenuClickLogPageDto menuClickLogPageDto);

    List<MenuClickLogExcelVo> getExcelList(MenuClickLogListDto menuClickLogListDto);
}
