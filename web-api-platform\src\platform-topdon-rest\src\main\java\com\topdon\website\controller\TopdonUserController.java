package com.topdon.website.controller;

import com.hiwie.breeze.Right;
import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.hiwie.breeze.util.HttpUtil;
import com.hiwie.security.SecurityHelper;
import com.hiwie.security.forms.SessionForm;
import com.hiwie.security.models.AbstractUserAccount;
import com.topdon.website.form.TopdonUserQuickLoginForm;
import com.topdon.website.form.TopdonUserSigninForm;
import com.topdon.website.services.TopdonUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * TopdonUserController
 *
 */
@RestController
@RequestMapping("topdon_user")
public class TopdonUserController extends ControllerSupport {

    /**
     * TopdonUserService实例
     */
    private final TopdonUserService topdonUserService;

    /**
     * 构造函数，注入TopdonUserService实例
     *
     * @param topdonUserService TopdonUserService实例
     */
    @Autowired
    public TopdonUserController(TopdonUserService topdonUserService) {
        this.topdonUserService = topdonUserService;
    }

    /**
     * 用户登录接口
     *
     * @param signinForm 登录表单
     * @param request    HttpServletRequest对象
     * @param response   HttpServletResponse对象
     * @return AbstractRestResponse对象
     */
    @PostMapping("/signin")
    public AbstractRestResponse userSingin(TopdonUserSigninForm signinForm, HttpServletRequest request, HttpServletResponse response) {
        // 创建SessionForm对象
        SessionForm sessionForm = new SessionForm();
        // 设置SessionForm属性
        sessionForm.setAccount(AbstractUserAccount.Type.TOKEN);
        sessionForm.setApp("TOPDON");
        sessionForm.setClient(HttpUtil.getInfo(request).toString());
        sessionForm.setIp(HttpUtil.getClientIP(request));
        sessionForm.setExpireTime("PT24H");

        // 调用TopdonUserService.signin方法，获取登录结果
        return AbstractRestResponse.apply(topdonUserService.signin(signinForm, sessionForm).flatMap(info -> {
            // 添加Cookie到响应头中
            response.addCookie(SecurityHelper.generateCookie(info.getSid()));
            // 返回Right对象
            return Right.apply(info);
        }));
    }

    /**
     * 快速登录接口
     *
     * @param signinForm 登录表单
     * @param request    HttpServletRequest对象
     * @param response   HttpServletResponse对象
     * @return AbstractRestResponse对象
     */
    @PostMapping("/quickLogin")
    public AbstractRestResponse quickLogin(@RequestBody TopdonUserQuickLoginForm signinForm, HttpServletRequest request, HttpServletResponse response) {
        // 创建SessionForm对象
        SessionForm sessionForm = new SessionForm();
        // 设置SessionForm属性
        sessionForm.setAccount(AbstractUserAccount.Type.TOKEN);
        sessionForm.setApp("TOPDON");
        sessionForm.setClient(HttpUtil.getInfo(request).toString());
        sessionForm.setIp(HttpUtil.getClientIP(request));
        sessionForm.setExpireTime("PT24H");

        // 调用TopdonUserService.quickLogin方法，获取登录结果
        return AbstractRestResponse.apply(topdonUserService.quickLogin(signinForm, sessionForm).flatMap(info -> {
            // 添加Cookie到响应头中
            response.addCookie(SecurityHelper.generateCookie(info.getSid()));
            // 返回Right对象
            return Right.apply(info);
        }));
    }
}
