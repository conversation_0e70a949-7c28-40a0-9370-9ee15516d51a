package com.topdon.website.model.api;

import java.io.Serializable;

/**
 * 返回请求结果
 *
 * <AUTHOR>
 */
public class RequestResult implements Serializable {
    private static final long serialVersionUID = 903133110511739665L;

    /**
     * 返回状态码  200:成功 500:错误
     **/
    private int code = 200;
    /**
     * 返回信息描述
     **/
    private String message = "成功";

    /**
     * 返回数据
     **/
    private Object data;

    /**
     * 附加数据
     * private Object appendData;
     **/


    public RequestResult() {
    }

    public RequestResult(int code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public static RequestResult ok(Object data) {
        RequestResult requestResult = new RequestResult();
        requestResult.setData(data);
        return requestResult;
    }

    public static RequestResult error(int code,String error) {
        RequestResult requestResult = new RequestResult();
        requestResult.setCode(code);
        requestResult.setMessage(error);
        return requestResult;
    }

    public RequestResult error(String error) {
        this.message = error;
        this.code = 500;
        return this;
    }
}
