package com.jeesite.modules.classification.web;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.classification.entity.ClassificationExtensionLink;
import com.jeesite.modules.classification.service.ClassificationExtensionLinkService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分类拓展链接Controller
 * <AUTHOR>
 * @version 2025-07-31
 */
@Controller
@RequestMapping(value = "${adminPath}/classification/classificationExtensionLink")
public class ClassificationExtensionLinkController extends BaseController {

	@Autowired
	private ClassificationExtensionLinkService classificationExtensionLinkService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ClassificationExtensionLink get(String id, boolean isNewRecord) {
		return classificationExtensionLinkService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("classification:classification:view")
	@RequestMapping(value = {"list", ""})
	public String list(ClassificationExtensionLink classificationExtensionLink, Model model) {
		model.addAttribute("classificationExtensionLink", classificationExtensionLink);
		return "modules/classification/classificationExtensionLinkList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("classification:classification:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ClassificationExtensionLink> listData(ClassificationExtensionLink classificationExtensionLink, HttpServletRequest request, HttpServletResponse response) {
		classificationExtensionLink.setPage(new Page<>(request, response));
		Page<ClassificationExtensionLink> page = classificationExtensionLinkService.findPage(classificationExtensionLink);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("classification:classification:view")
	@RequestMapping(value = "form")
	public String form(ClassificationExtensionLink classificationExtensionLink, Model model) {
		model.addAttribute("classificationExtensionLink", classificationExtensionLink);
		return "modules/classification/classificationExtensionLinkForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("classification:classification:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ClassificationExtensionLink classificationExtensionLink) {
		classificationExtensionLinkService.save(classificationExtensionLink);
		return renderResult(Global.TRUE, text("保存分类拓展链接成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("classification:classification:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ClassificationExtensionLink classificationExtensionLink) {
		classificationExtensionLinkService.delete(classificationExtensionLink);
		return renderResult(Global.TRUE, text("删除分类拓展链接成功！"));
	}
	
	/**
	 * 根据分类编码获取拓展链接列表
	 */
	@RequiresPermissions("classification:classification:view")
	@RequestMapping(value = "getByClassificationCode")
	@ResponseBody
	public List<ClassificationExtensionLink> getByClassificationCode(String classificationCode) {
		return classificationExtensionLinkService.findByClassificationCode(classificationCode);
	}
	
}
