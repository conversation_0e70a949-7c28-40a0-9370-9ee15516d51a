package com.topdon.website.controller;

import com.hiwie.breeze.rest.AbstractRestResponse;
import com.hiwie.breeze.rest.ControllerSupport;
import com.topdon.website.services.WebsiteSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/website_setting")
public class WebsiteSettingController extends ControllerSupport {

    private final WebsiteSettingService websiteSettingService;

    @Autowired
    public WebsiteSettingController(WebsiteSettingService websiteSettingService) {
        this.websiteSettingService = websiteSettingService;
    }

    @GetMapping
    public AbstractRestResponse get(String key) {
        return AbstractRestResponse.apply(websiteSettingService.get(key));
    }
}
