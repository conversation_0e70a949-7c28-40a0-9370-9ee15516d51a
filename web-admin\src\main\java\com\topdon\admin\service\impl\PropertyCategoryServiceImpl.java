package com.topdon.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jeesite.modules.sys.utils.UserUtils;
import com.topdon.admin.convert.PropertyCategoryConvert;
import com.topdon.admin.dto.ProductPropertySaveDTO;
import com.topdon.admin.entity.ProductProperty;
import com.topdon.admin.vo.PropertyCategoryListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topdon.admin.entity.PropertyCategory;
import com.topdon.admin.mapper.PropertyCategoryMapper;
import com.topdon.admin.service.PropertyCategoryService;

import javax.annotation.Resource;

@Service
@Slf4j
public class PropertyCategoryServiceImpl extends ServiceImpl<PropertyCategoryMapper, PropertyCategory> implements PropertyCategoryService {
    @Resource
    private PropertyCategoryConvert propertyCategoryConvert;

    @Override
    public Map<Integer, List<PropertyCategoryListVo>> getList(Integer classificationCompareId, boolean draft) {
        Map<Integer, List<PropertyCategoryListVo>> result = new HashMap<>();
        List<PropertyCategory> propertyCategories = getListed(classificationCompareId, draft, 0);

        for (PropertyCategory parentPropertyCategory : propertyCategories) {

            result.computeIfAbsent(0, k -> new ArrayList<>()).add(propertyCategoryConvert.toPropertyCategoryListVo(parentPropertyCategory, null));

            List<PropertyCategory> categoryList = getListed(classificationCompareId, draft, parentPropertyCategory.getId());
            // 如果没有子类 构造一个
            if (categoryList.isEmpty()) {
                PropertyCategoryListVo propertyCategoryListVo = new PropertyCategoryListVo();
                propertyCategoryListVo.setClassificationCompareId(classificationCompareId);
                propertyCategoryListVo.setParentId(parentPropertyCategory.getId());
                propertyCategoryListVo.setParentCategory(parentPropertyCategory.getCategory());
                propertyCategoryListVo.setCategory("");
                propertyCategoryListVo.setSort(0);
                propertyCategoryListVo.setDraft(draft);
                result.computeIfAbsent(1, k -> new ArrayList<>()).add(propertyCategoryListVo);
            } else {
                result.computeIfAbsent(1, k -> new ArrayList<>()).addAll(categoryList.stream().map(item -> propertyCategoryConvert.toPropertyCategoryListVo(item, parentPropertyCategory.getCategory())).collect(Collectors.toList()));
            }
        }

        return result;
    }

    @Override
    public Map<String, PropertyCategory> saveOrUpdateList(boolean draft, Integer compareId, List<ProductPropertySaveDTO.PropertyCategorySaveDTO> propertyCategories) {
        // 删除不存在的数据
        List<Integer> propertyCategoryIds = propertyCategories.stream().map(PropertyCategory::getId).filter(Objects::nonNull).collect(Collectors.toList());
        lambdaUpdate()
                .eq(PropertyCategory::getClassificationCompareId, compareId)
                .eq(PropertyCategory::getDraft, draft)
                .notIn(CollUtil.isNotEmpty(propertyCategoryIds), PropertyCategory::getId, propertyCategoryIds)
                .remove();

        Map<String, PropertyCategory> parentResult = new HashMap<>();
        Map<String, PropertyCategory> childResult = new HashMap<>();
        // 保存父类
        for (PropertyCategory propertyCategory : propertyCategories.stream()
                .filter(item -> StrUtil.isBlank(item.getParentCategory()))
                .peek(item -> {
                    if (item.getId() != null && this.lambdaQuery()
                            .eq(PropertyCategory::getDraft, draft)
                            .eq(PropertyCategory::getClassificationCompareId, compareId)
                            .eq(PropertyCategory::getId,item.getId())
                            .count() == 0) {
                        item.setId(null);
                    }
                })
                .collect(Collectors.toList())) {
            propertyCategory.setClassificationCompareId(compareId);
            propertyCategory.setDraft(draft);
            propertyCategory.setUpdateDate(new Date());
            propertyCategory.setUpdateBy(UserUtils.getLoginInfo().getId());
            if (propertyCategory.getId() == null) {
                propertyCategory.setCreateDate(new Date());
                propertyCategory.setCreateBy(UserUtils.getLoginInfo().getId());
            }
            saveOrUpdate(propertyCategory);
            parentResult.put(propertyCategory.getCategory(), propertyCategory);
        }
        // 保存子类
        for (ProductPropertySaveDTO.PropertyCategorySaveDTO propertyCategory : propertyCategories
                .stream()
                .filter(item -> StrUtil.isNotBlank(item.getParentCategory()))
                .peek(item -> {
                    if (item.getId() != null && this.lambdaQuery()
                            .eq(PropertyCategory::getDraft, draft)
                            .eq(PropertyCategory::getClassificationCompareId, compareId)
                            .eq(PropertyCategory::getId,item.getId())
                            .count() == 0) {
                        item.setId(null);
                    }
                })
                .collect(Collectors.toList())) {
            PropertyCategory parentPropertyCategory = parentResult.get(propertyCategory.getParentCategory());
            propertyCategory.setParentId(parentPropertyCategory.getId());
            propertyCategory.setClassificationCompareId(compareId);
            propertyCategory.setDraft(draft);
            propertyCategory.setUpdateDate(new Date());
            propertyCategory.setUpdateBy(UserUtils.getLoginInfo().getId());
            if (propertyCategory.getId() == null) {
                propertyCategory.setCreateDate(new Date());
                propertyCategory.setCreateBy(UserUtils.getLoginInfo().getId());
            }
            saveOrUpdate(propertyCategory);
            childResult.put(propertyCategory.getCategory(), propertyCategory);
        }

        return childResult;
    }

    private List<PropertyCategory> getListed(Integer classificationCompareId, boolean draft, Integer parentId) {
        return lambdaQuery()
                .eq(PropertyCategory::getClassificationCompareId, classificationCompareId)
                .eq(PropertyCategory::getDraft, draft)
                .eq(PropertyCategory::getParentId, parentId)
                .orderByAsc(PropertyCategory::getSort)
                .list();
    }
}
