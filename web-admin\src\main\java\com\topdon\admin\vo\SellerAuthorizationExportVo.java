package com.topdon.admin.vo;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import lombok.Data;

import java.util.Date;

@Data
public class SellerAuthorizationExportVo {
    @ExcelFields({
            @ExcelField(title = "授权证书编号", attrName = "authCertNo", align = ExcelField.Align.LEFT, sort = 50),
            @ExcelField(title = "被授权主体", attrName = "authorizedEntity", align = ExcelField.Align.LEFT, sort = 60),
            @ExcelField(title = "经销商层级", attrName = "dealerLevelStr", align = ExcelField.Align.LEFT, sort = 70),
            @ExcelField(title = "发布状态", attrName = "statusStr", align = ExcelField.Align.LEFT, sort = 80),
            @ExcelField(title = "授权区域", attrName = "region", align = ExcelField.Align.LEFT, sort = 90),
            @ExcelField(title = "授权品线/型号", attrName = "productLineName", align = ExcelField.Align.LEFT, sort = 100),
            @ExcelField(title = "授权类型", attrName = "authTypeStr", align = ExcelField.Align.LEFT, sort = 110),
            @ExcelField(title = "授权起始日期", attrName = "authStartDate", align = ExcelField.Align.LEFT, sort = 120,dataFormat = "yyyy-MM-dd"),
            @ExcelField(title = "授权结束日期", attrName = "authEndDate", align = ExcelField.Align.LEFT, sort = 130,dataFormat = "yyyy-MM-dd"),
            @ExcelField(title = "授权平台", attrName = "authPlatform", align = ExcelField.Align.LEFT, sort = 140),
            @ExcelField(title = "店铺名称", attrName = "storeName", align = ExcelField.Align.LEFT, sort = 150),
            @ExcelField(title = "店铺链接", attrName = "storeLink", align = ExcelField.Align.LEFT, sort = 160),
            @ExcelField(title = "联系电话", attrName = "contactPhone", align = ExcelField.Align.LEFT, sort = 170),
            @ExcelField(title = "联系邮箱", attrName = "contactEmail", align = ExcelField.Align.LEFT, sort = 180),
            @ExcelField(title = "联系地址", attrName = "contactAddress", align = ExcelField.Align.LEFT, sort = 190),
            @ExcelField(title = "业务员", attrName = "salesRep", align = ExcelField.Align.LEFT, sort = 200),
            @ExcelField(title = "经销商编码", attrName = "dealerCode", align = ExcelField.Align.LEFT, sort = 210),
            @ExcelField(title = "经销商名称", attrName = "dealerName", align = ExcelField.Align.LEFT, sort = 220),
            @ExcelField(title = "所属上级经销商", attrName = "parentDealer", align = ExcelField.Align.LEFT, sort = 230),
    })
    public SellerAuthorizationExportVo() {
    }

    private String authCertNo;
    private String authorizedEntity;
    private Integer status;
    private String statusStr;
    private Integer authType;
    private String authTypeStr;
    private Date authStartDate;
    private Date authEndDate;
    private String contactPhone;
    private String contactEmail;
    private String contactAddress;
    private String salesRep;
    private Integer dealerLevel;
    private String dealerLevelStr;
    private String dealerCode;
    private String dealerName;
    private String parentDealer;
    private String otherMsg;

    private String authPlatform;
    private String storeName;
    private String storeLink;

    private String productLineName;
    private String region;

    public String getDealerLevelStr() {
        if(dealerLevel==null){
            return "";
        }
        switch (dealerLevel){
            case 0:return "一级";
            case 1:return "二级";
            case 2:return "三级";
        }
        return "";
    }

    public String getStatusStr() {
        switch (dealerLevel){
            case 0:return "草稿";
            case 1:return "正式";
        }
        return "";
    }

    public String getAuthTypeStr() {
        switch (dealerLevel){
            case 0:return "普通";
            case 1:return "独家";
        }
        return "";
    }
}
