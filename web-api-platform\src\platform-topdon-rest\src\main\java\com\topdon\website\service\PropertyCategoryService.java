package com.topdon.website.service;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.ErrorMessage;
import com.topdon.website.entity.PropertyCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topdon.website.vo.PropertyCategoryTreeVo;

public interface PropertyCategoryService extends IService<PropertyCategory>{


    AbstractEither<ErrorMessage, PropertyCategoryTreeVo> getList(String classificationCode, boolean draft);
}
