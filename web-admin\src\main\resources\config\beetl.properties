
#设置与beetl-default.properties相同的属性将被覆盖默认设置

##导入项目中的调用静态方法类（项目中设置，自动合并IMPORT_PACKAGE设置）
#IMPORT_PACKAGE_PROJECT=\
#	com.jeesite.modules.project.utils.;\
	
## 内置的方法
#FN.date = org.beetl.ext.fn.DateFunction

##内置的功能包
#FNP.strutil = org.beetl.ext.fn.StringUtil

##内置的格式化函数
#FT.dateFormat =  org.beetl.ext.format.DateFormat

##内置的默认格式化函数
#FTC.java.util.Date = org.beetl.ext.format.DateFormat

## 标签类
#TAG.include= org.beetl.ext.tag.IncludeTag
